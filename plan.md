

### **Plano de Melhoria - Filtro de Estoque por Fornecedor**

**Versão do Plano:** 5.0
**Data:** 20/08/2024
**Objetivo:** Adicionar a funcionalidade de filtrar a lista de estoque de códigos de recarga por fornecedor.

---

### **Tarefa 1: Backend - Habilitar o Filtro no Banco de Dados**

Começaremos pela camada mais profunda, o repositório, para permitir que a consulta ao MongoDB aceite o novo filtro.

**Arquivo:** `infocell-server/src/repositories/recharge_code_repository.rs`

**Ação:** Modifique a função `find_all` para aceitar um `erp_supplier_id` opcional e adicioná-lo à query do MongoDB.

```rust
// DENTRO DE: pub struct RechargeCodeRepository { ... }
// Modifique a assinatura da função find_all para incluir o novo parâmetro:
pub async fn find_all(
    &self,
    page: u32,
    per_page: u32,
    package_type: Option<String>,
    is_available: Option<bool>,
    search_code: Option<String>,
    erp_supplier_id: Option<String>, // +++ ADICIONE ESTE PARÂMETRO
    sort_by: Option<String>,
    sort_order: Option<String>,
) -> Result<(Vec<RechargeCode>, u64)> {
    let mut filter = doc! {};

    if let Some(pt) = package_type {
        filter.insert("package_type", pt);
    }
    if let Some(avail) = is_available {
        filter.insert("is_available", avail);
    }
    if let Some(search) = search_code {
        filter.insert("code", doc! { "$regex": search, "$options": "i" });
    }
    
    // +++ ADICIONE ESTE BLOCO PARA O NOVO FILTRO +++
    if let Some(supplier_id) = erp_supplier_id {
        if !supplier_id.is_empty() {
            filter.insert("erp_supplier_id", supplier_id);
        }
    }
    // ++++++++++++++++++++++++++++++++++++++++++++++

    // ... o resto da função continua igual ...
}
```

---

### **Tarefa 2: Backend - Propagar o Filtro pela Camada de Serviço**

Agora, vamos passar o novo parâmetro através da camada de serviço.

**Arquivo:** `infocell-server/src/services/recharge_service.rs`

**Ação:** Adicione `erp_supplier_id` à assinatura da função `get_recharge_stock` e passe-o para a chamada do repositório.

```rust
// DENTRO DE: pub struct RechargeService { ... }
// Modifique a assinatura da função:
pub async fn get_recharge_stock(
    &self,
    page: u32,
    per_page: u32,
    package_type: Option<String>,
    is_available: Option<bool>,
    search_code: Option<String>,
    erp_supplier_id: Option<String>, // +++ ADICIONE ESTE PARÂMETRO
    sort_by: Option<String>,
    sort_order: Option<String>,
) -> Result<(Vec<RechargeCode>, u64)> {
    // Modifique a chamada para o repositório:
    self.code_repo
        .find_all(
            page, 
            per_page, 
            package_type, 
            is_available, 
            search_code, 
            erp_supplier_id, // +++ PASSE O PARÂMETRO AQUI
            sort_by, 
            sort_order
        )
        .await
}
```

---

### **Tarefa 3: Backend - Expor o Filtro na API**

Finalmente, no backend, vamos permitir que a rota da API receba o novo parâmetro.

**Arquivo:** `infocell-server/src/handlers/recharge_handlers.rs`

**Ação:** Adicione o campo `erp_supplier_id` à struct `GetStockQuery` e passe-o para a chamada do serviço.

```rust
// ...

#[derive(Deserialize)]
pub struct GetStockQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub package_type: Option<String>,
    pub is_available: Option<bool>,
    pub search_code: Option<String>,
    pub erp_supplier_id: Option<String>, // +++ ADICIONE ESTE CAMPO
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

// ...

pub async fn get_stock_handler(
    State(state): State<AppState>,
    Query(params): Query<GetStockQuery>,
) -> Result<Json<ApiResponse<StockResponse>>> {
    let page = params.page.unwrap_or(1);
    let per_page = params.per_page.unwrap_or(20);

    let (codes, total) = state
        .recharge_service
        .get_recharge_stock(
            page,
            per_page,
            params.package_type,
            params.is_available,
            params.search_code,
            params.erp_supplier_id, // +++ PASSE O PARÂMETRO AQUI
            params.sort_by,
            params.sort_order
        )
        .await?;
    
    // ... o resto da função continua igual ...
}
```

---

### **Tarefa 4: Frontend - Adicionar o Componente de Filtro na UI**

Agora que o backend está pronto, vamos adicionar o campo de busca de fornecedor na página de estoque.

**Arquivo:** `tools_infocell/src/app/dashboard-infocell/recharges/stock/page.jsx`

**Ação:** Importe e utilize o componente `SupplierSearchInput`.

```javascript
"use client";

import React, { useState, useEffect, useCallback } from 'react';
// ... outras importações
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search } from "lucide-react";
import infocellApi from '@/lib/infocellApi';
import { useDebounce } from '@/lib/hooks/useDebounce';
import SupplierSearchInput from '@/components/custom/SupplierSearchInput'; // +++ ADICIONE ESTA IMPORTAÇÃO

export default function RechargeStockPage() {
    // ...
    const [pagination, setPagination] = useState({ page: 1, per_page: 20, total: 0 });
    const [filters, setFilters] = useState({
        package_type: 'all',
        is_available: 'all',
        search_code: '',
        erp_supplier_id: '', // +++ ADICIONE ESTE CAMPO
        sort_by: 'imported_at',
        sort_order: 'desc'
    });
    const [selectedSupplier, setSelectedSupplier] = useState(null); // +++ ADICIONE ESTE ESTADO

    const debouncedFilters = useDebounce(filters, 500);

    const fetchStock = useCallback(async (page = 1) => {
        setIsLoading(true);
        setError(null);
        try {
            const apiParams = { page, per_page: pagination.per_page };
            if (debouncedFilters.package_type !== 'all') {
                apiParams.package_type = debouncedFilters.package_type;
            }
            if (debouncedFilters.is_available !== 'all') {
                apiParams.is_available = debouncedFilters.is_available === 'true';
            }
            if (debouncedFilters.search_code) {
                apiParams.search_code = debouncedFilters.search_code;
            }
            // +++ ADICIONE ESTE BLOCO PARA O FILTRO DE FORNECEDOR +++
            if (debouncedFilters.erp_supplier_id) {
                apiParams.erp_supplier_id = debouncedFilters.erp_supplier_id;
            }
            // +++++++++++++++++++++++++++++++++++++++++++++++++++++++
            if (debouncedFilters.sort_by) {
                apiParams.sort_by = debouncedFilters.sort_by;
                apiParams.sort_order = debouncedFilters.sort_order;
            }

            const response = await infocellApi.getRechargeStock(apiParams);
            setStock(response.data.codes || []);
            setPagination(prev => ({ ...prev, total: response.data.total, page }));
        } catch (err) {
            setError('Falha ao buscar estoque.');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    }, [pagination.per_page, debouncedFilters]);

    // ... (useEffect e outras funções) ...

    const handleFilterChange = (name, value) => {
        setFilters(prev => ({ ...prev, [name]: value }));
    };

    // +++ ADICIONE ESTA NOVA FUNÇÃO +++
    const handleSupplierSelect = (supplier) => {
        setSelectedSupplier(supplier);
        handleFilterChange('erp_supplier_id', supplier ? supplier.id : '');
    };

    // ... (resto do componente) ...

    return (
        <Card>
            <CardHeader>
                <CardTitle>Estoque de Códigos de Recarga</CardTitle>
            </CardHeader>
            <CardContent>
                {/* Modifique o grid de filtros para ter 4 colunas em telas grandes */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div>
                        {/* ... (input de busca por código) ... */}
                    </div>

                    {/* +++ ADICIONE O CAMPO DE FILTRO POR FORNECEDOR AQUI +++ */}
                    <div>
                        <Label>Filtrar por Fornecedor</Label>
                        <SupplierSearchInput 
                            onSelect={handleSupplierSelect} 
                            selectedSupplier={selectedSupplier}
                        />
                    </div>
                    {/* ++++++++++++++++++++++++++++++++++++++++++++++++++++++ */}

                    <div>
                        {/* ... (select de pacote) ... */}
                    </div>
                    <div>
                        {/* ... (select de status) ... */}
                    </div>
                </div>

                {/* O resto do componente (tabela, paginação) continua o mesmo */}
            </CardContent>
        </Card>
    );
}
```

