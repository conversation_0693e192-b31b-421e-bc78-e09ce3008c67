// Migração para adicionar índice único no campo 'code' da coleção recharge_codes
// Execute este script no MongoDB para garantir que não haja códigos duplicados

// Conectar ao banco de dados
use infocell_dashboard;

// Verificar se já existe o índice
print("Verificando índices existentes na coleção recharge_codes...");
db.recharge_codes.getIndexes().forEach(function(index) {
    print("Índice: " + index.name + " - Campos: " + JSON.stringify(index.key));
});

// Verificar se há códigos duplicados antes de criar o índice
print("\nVerificando códigos duplicados...");
var duplicates = db.recharge_codes.aggregate([
    {
        $group: {
            _id: "$code",
            count: { $sum: 1 },
            docs: { $push: "$_id" }
        }
    },
    {
        $match: {
            count: { $gt: 1 }
        }
    }
]);

var hasDuplicates = false;
duplicates.forEach(function(doc) {
    hasDuplicates = true;
    print("Código duplicado encontrado: " + doc._id + " (aparece " + doc.count + " vezes)");
    print("IDs dos documentos: " + JSON.stringify(doc.docs));
});

if (hasDuplicates) {
    print("\nATENÇÃO: Códigos duplicados encontrados!");
    print("Você deve resolver as duplicatas antes de criar o índice único.");
    print("Sugestão: Mantenha apenas o código mais recente de cada duplicata.");
} else {
    print("Nenhum código duplicado encontrado. Prosseguindo com a criação do índice...");
    
    try {
        // Criar índice único no campo 'code'
        db.recharge_codes.createIndex(
            { "code": 1 }, 
            { 
                unique: true,
                name: "unique_recharge_code",
                background: true
            }
        );
        print("Índice único criado com sucesso no campo 'code'!");
        
        // Verificar se o índice foi criado
        print("\nÍndices após a criação:");
        db.recharge_codes.getIndexes().forEach(function(index) {
            print("Índice: " + index.name + " - Campos: " + JSON.stringify(index.key) + 
                  (index.unique ? " (ÚNICO)" : ""));
        });
        
    } catch (error) {
        print("Erro ao criar índice único: " + error);
    }
}

print("\nMigração concluída.");
