# Build stage
FROM rust:1.82-slim AS builder

WORKDIR /app

# Instalar dependências do sistema necessárias
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copiar arquivos de configuração
COPY Cargo.toml Cargo.lock ./

# Criar diretório src com main.rs dummy para cache das dependências
RUN mkdir src && echo "fn main() {}" > src/main.rs

# Build das dependências (cache layer)
RUN cargo build --release
RUN rm src/main.rs

# Copiar código fonte
COPY src ./src

# Build da aplicação
RUN touch src/main.rs
RUN cargo build --release

# Runtime stage  
FROM debian:bookworm-slim

# Instalar dependências de runtime
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copiar binário da stage de build
COPY --from=builder /app/target/release/infocell-server /app/infocell-server

# Definir porta
EXPOSE 3000

# Comando para executar a aplicação
CMD ["./infocell-server"] 