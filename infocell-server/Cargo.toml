[package]
name = "infocell-server"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
axum = { version = "0.8.4", features = ["multipart"] }
tokio = { version = "1.37.0", features = ["full"] }
serde = { version = "1.0.203", features = ["derive"] }
serde_json = "1.0.117"
dotenvy = "0.15.7"
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
jsonwebtoken = "9.3.1"
axum-extra = { version = "0.10.1", features = ["typed-header"] }
# MongoDB integration
mongodb = "3.2.4"
bson = { version = "2.11.0", features = ["chrono-0_4"] }
futures-util = "0.3.30"
# AWS S3 SDK para MinIO
aws-config = "1.1.7"
aws-sdk-s3 = { version = "1.21.0", features = ["behavior-version-latest"] }
aws-credential-types = "1.1.7"
# HTTP client para integração com APIs externas
reqwest = { version = "0.12.4", default-features = false, features = ["json", "stream", "native-tls-vendored"] }
# Utilitários adicionais
uuid = { version = "1.8.0", features = ["v4", "serde"] }
chrono = { version = "0.4.38", features = ["serde"] }
anyhow = "1.0.82"
tower = "0.4.13"
tower-http = { version = "0.6.2", features = ["cors"] }
regex = "1.10.4"
urlencoding = "2.1.3"
base64 = "0.22.1" # Para codificar o template para o Carbone.io
carbone-sdk-rust = "1.0.0"
# Para manipulação de documentos e inserção de imagens
zip = "0.6"
image = "0.24"
# Para normalização Unicode (remover acentos)
unicode-normalization = "0.1"
futures = "0.3.30"
clap = { version = "4.5.40", features = ["derive"] }
