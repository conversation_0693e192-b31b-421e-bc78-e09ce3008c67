#[cfg(test)]
mod tests {
    use infocell_server::services::recharge_service::RechargeService;

    #[test]
    fn test_validate_recharge_code() {
        // Teste de código válido
        assert!(RechargeService::validate_recharge_code("1234567890123456").is_ok());
        
        // Teste de código muito curto
        assert!(RechargeService::validate_recharge_code("123456789012345").is_err());
        
        // Teste de código muito longo
        assert!(RechargeService::validate_recharge_code("12345678901234567").is_err());
        
        // Teste de código com letras
        assert!(RechargeService::validate_recharge_code("123456789012345a").is_err());
        
        // Teste de código com caracteres especiais
        assert!(RechargeService::validate_recharge_code("123456789012345-").is_err());
    }

    #[test]
    fn test_validate_codes_batch() {
        let codes = vec![
            "1234567890123456".to_string(), // Válido
            "1234567890123457".to_string(), // Válido
            "123456789012345".to_string(),  // Inválido (muito curto)
            "123456789012345a".to_string(), // Inválido (contém letra)
            "1234567890123456".to_string(), // Duplicado
            "".to_string(),                 // Vazio (deve ser ignorado)
            "  1234567890123458  ".to_string(), // Válido (com espaços)
        ];

        let (valid_codes, errors) = RechargeService::validate_codes_batch(&codes);

        // Deve ter 3 códigos válidos (incluindo o com espaços, mas sem o duplicado)
        assert_eq!(valid_codes.len(), 3);
        assert!(valid_codes.contains(&"1234567890123456".to_string()));
        assert!(valid_codes.contains(&"1234567890123457".to_string()));
        assert!(valid_codes.contains(&"1234567890123458".to_string()));

        // Deve ter 3 erros (código curto, código com letra, duplicado)
        assert_eq!(errors.len(), 3);
        
        // Verificar se os erros contêm as mensagens esperadas
        assert!(errors.iter().any(|e| e.contains("deve ter exatamente 16 dígitos")));
        assert!(errors.iter().any(|e| e.contains("deve conter apenas dígitos")));
        assert!(errors.iter().any(|e| e.contains("está duplicado na requisição")));
    }

    #[test]
    fn test_validate_codes_batch_empty_input() {
        let codes = vec![];
        let (valid_codes, errors) = RechargeService::validate_codes_batch(&codes);
        
        assert_eq!(valid_codes.len(), 0);
        assert_eq!(errors.len(), 0);
    }

    #[test]
    fn test_validate_codes_batch_only_empty_codes() {
        let codes = vec!["".to_string(), "   ".to_string(), "\t".to_string()];
        let (valid_codes, errors) = RechargeService::validate_codes_batch(&codes);
        
        // Códigos vazios devem ser ignorados silenciosamente
        assert_eq!(valid_codes.len(), 0);
        assert_eq!(errors.len(), 0);
    }
}
