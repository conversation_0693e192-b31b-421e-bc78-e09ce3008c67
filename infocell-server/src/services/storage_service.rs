use crate::config::AppConfig;
use crate::error::{AppError, Result};
use aws_credential_types::Credentials;
use aws_sdk_s3::{Client, Config};
use aws_sdk_s3::config::Region;
use aws_sdk_s3::presigning::PresigningConfig;
use aws_sdk_s3::primitives::ByteStream;
use std::time::Duration;

#[derive(Clone)]
pub struct StorageService {
    client: Client,
    bucket: String,
}

impl StorageService {
    pub async fn new(config: &AppConfig) -> Result<Self> {
        let credentials = Credentials::new(
            &config.minio_access_key,
            &config.minio_secret_key,
            None,
            None,
            "minio",
        );

        let s3_config = Config::builder()
            .endpoint_url(&config.minio_endpoint)
            .region(Region::new(config.minio_region.clone()))
            .credentials_provider(credentials)
            .force_path_style(true) 
            .build();

        let client = Client::from_conf(s3_config);

        
        let bucket_name = config.minio_bucket.clone();
        match client.head_bucket().bucket(&bucket_name).send().await {
            Ok(_) => {
                tracing::info!("Bucket '{}' já existe", bucket_name);
            }
            Err(_) => {
                tracing::info!("Criando bucket '{}'", bucket_name);
                client
                    .create_bucket()
                    .bucket(&bucket_name)
                    .send()
                    .await
                    .map_err(|e| AppError::InternalServerError(format!("Erro ao criar bucket: {}", e)))?;
            }
        }

        Ok(Self {
            client,
            bucket: bucket_name,
        })
    }

    pub async fn upload_file(
        &self,
        key: &str,
        data: Vec<u8>,
        content_type: &str,
    ) -> Result<()> {
        let put_object = self
            .client
            .put_object()
            .bucket(&self.bucket)
            .key(key)
            .body(ByteStream::from(data))
            .content_type(content_type)
            .send()
            .await
            .map_err(|e| AppError::InternalServerError(format!("Erro no upload: {}", e)))?;

        let etag = put_object
            .e_tag()
            .unwrap_or("unknown")
            .to_string();

        tracing::info!("Arquivo '{}' enviado com sucesso. ETag: {}", key, etag);
        Ok(())
    }

    pub async fn download_file(&self, key: &str) -> Result<Vec<u8>> {
        let response = self
            .client
            .get_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await
            .map_err(|e| AppError::NotFound(format!("Arquivo não encontrado: {}", e)))?;

        let data = response
            .body
            .collect()
            .await
            .map_err(|e| AppError::InternalServerError(format!("Erro ao ler arquivo: {}", e)))?
            .into_bytes()
            .to_vec();

        Ok(data)
    }

    pub async fn delete_file(&self, key: &str) -> Result<()> {
        self.client
            .delete_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await
            .map_err(|e| AppError::InternalServerError(format!("Erro ao excluir arquivo: {}", e)))?;

        tracing::info!("Arquivo '{}' excluído com sucesso", key);
        Ok(())
    }

    pub async fn file_exists(&self, key: &str) -> Result<bool> {
        match self
            .client
            .head_object()
            .bucket(&self.bucket)
            .key(key)
            .send()
            .await
        {
            Ok(_) => Ok(true),
            Err(_) => Ok(false),
        }
    }

    pub async fn get_file_url(&self, key: &str, _expires_in: u64) -> Result<String> {
        // Para MinIO, precisamos gerar uma URL pré-assinada
        // Isso requer uma configuração adicional que pode ser implementada depois
        // Por enquanto, retornamos o caminho do arquivo
        Ok(format!("minio://{}/{}", self.bucket, key))
    }

    pub async fn get_presigned_download_url(&self, key: &str, expires_in_secs: u64) -> Result<String> {
        let presigning_config = PresigningConfig::expires_in(Duration::from_secs(expires_in_secs))
            .map_err(|e| AppError::InternalServerError(format!("Erro ao criar configuração de pré-assinatura: {}", e)))?;

        let presigned_request = self.client
            .get_object()
            .bucket(&self.bucket)
            .key(key)
            .presigned(presigning_config)
            .await
            .map_err(|e| AppError::InternalServerError(format!("Erro ao gerar URL pré-assinada: {}", e)))?;
        
        Ok(presigned_request.uri().to_string())
    }
}
