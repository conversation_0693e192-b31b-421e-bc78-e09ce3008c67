use crate::config::AppConfig;
use crate::error::{AppError, Result};
use crate::models::{
    ErpCliente, ClientListResponse, ErpEndereco, ErpDetalhesEndereco, 
    CreateErpClientPayload, UpdateErpClientPayload, PaginationMeta,
    ErpSupplierListResponse, ErpEmployeeListResponse
};
use chrono::NaiveDateTime;
use reqwest::Client;
use serde_json::{Value, json};
use tracing;
use urlencoding;

#[derive(Clone)]
pub struct ErpService {
    client: Client,
    api_url: String,
    api_key: String,
    erp_secret_key: String,
}

impl ErpService {
    pub fn new(config: &AppConfig) -> Self {
        Self {
            client: Client::new(),
            api_url: config.erp_api_url.clone(),
            api_key: config.erp_api_key.clone(),
            erp_secret_key: config.erp_secret_key.clone(),
        }
    }

    pub async fn get_clients(
        &self,
        page: u32,
        per_page: u32,
        nome: Option<String>,
    ) -> Result<ClientListResponse> {
        let mut url = format!("{}/clientes", self.api_url);
        
        // Adicionar parâmetros de query
        let mut params = vec![
            ("pagina", page.to_string()),
            ("limite", per_page.to_string()),
        ];

        if let Some(search_term) = nome {
            params.push(("nome", search_term));
        }

        let query_string = params
            .iter()
            .map(|(k, v)| format!("{}={}", k, urlencoding::encode(&v)))
            .collect::<Vec<_>>()
            .join("&");

        if !query_string.is_empty() {
            url.push_str(&format!("?{}", query_string));
        }

        tracing::debug!("Chamando ERP para obter clientes: {}", url);

        let response = self
            .client
            .get(&url)
            .header("Access-Token", self.api_key.clone())
            .header("Secret-Access-Token", &self.erp_secret_key)
            .header("Content-Type", "application/json")
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(AppError::ExternalServiceError(
                format!("ERP API retornou status: {}", response.status()),
            ));
        }

        let data: Value = response.json().await?;
   
        // Adaptar a resposta do ERP para nosso modelo
        self.parse_clients_response(data, page, per_page).await
    }

    pub async fn get_suppliers(
        &self,
        page: u32,
        per_page: u32,
        nome: Option<String>,
    ) -> Result<ErpSupplierListResponse> {
        let mut url = format!("{}/fornecedores", self.api_url);

        let mut params = vec![
            ("pagina", page.to_string()),
            ("limite", per_page.to_string()),
        ];

        if let Some(search_term) = nome {
            params.push(("nome", search_term));
        }

        let query_string = params
            .iter()
            .map(|(k, v)| format!("{}={}", k, urlencoding::encode(v)))
            .collect::<Vec<_>>()
            .join("&");
        
        if !query_string.is_empty() {
            url.push_str(&format!("?{}", query_string));
        }

        tracing::debug!("Chamando ERP para obter fornecedores: {}", url);

        let response = self
            .client
            .get(&url)
            .header("Access-Token", &self.api_key)
            .header("Secret-Access-Token", &self.erp_secret_key)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(AppError::ExternalServiceError(format!(
                "ERP API (fornecedores) retornou status: {}",
                response.status()
            )));
        }

        // Alinhando com o padrão de get_clients
        let data: Value = response.json().await?;
        serde_json::from_value(data)
            .map_err(|e| AppError::ExternalServiceError(format!("Erro ao parsear resposta de fornecedores: {}", e)))
    }

    pub async fn get_employees(
        &self,
        page: u32,
        per_page: u32,
        nome: Option<String>,
    ) -> Result<ErpEmployeeListResponse> {
        let mut url = format!("{}/funcionarios", self.api_url);

        let mut params = vec![
            ("pagina", page.to_string()),
            ("limite", per_page.to_string()),
        ];

        if let Some(search_term) = nome {
            params.push(("nome", search_term));
        }

        let query_string = params
            .iter()
            .map(|(k, v)| format!("{}={}", k, urlencoding::encode(v)))
            .collect::<Vec<_>>()
            .join("&");
        
        if !query_string.is_empty() {
            url.push_str(&format!("?{}", query_string));
        }

        tracing::debug!("Chamando ERP para obter funcionários: {}", url);

        let response = self
            .client
            .get(&url)
            .header("Access-Token", &self.api_key)
            .header("Secret-Access-Token", &self.erp_secret_key)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(AppError::ExternalServiceError(format!(
                "ERP API (funcionários) retornou status: {}",
                response.status()
            )));
        }
        
        let response_value: Value = response.json().await?;

        // Extrai o campo "data" que contém a lista de funcionários
        let data = serde_json::from_value(
            response_value
                .get("data")
                .cloned()
                .ok_or_else(|| AppError::ExternalServiceError(
                    "Campo 'data' não encontrado na resposta de funcionários".to_string()
                ))?
        ).map_err(|e| AppError::ExternalServiceError(format!("Erro ao parsear 'data' de funcionários: {}", e)))?;

        // Extrai o campo "meta" para paginação
        let meta = serde_json::from_value(
            response_value
                .get("meta")
                .cloned()
                .ok_or_else(|| AppError::ExternalServiceError(
                    "Campo 'meta' não encontrado na resposta de funcionários".to_string()
                ))?
        ).map_err(|e| AppError::ExternalServiceError(format!("Erro ao parsear 'meta' de funcionários: {}", e)))?;

        // Retorna a estrutura correta
        Ok(ErpEmployeeListResponse { data, meta })
    }

    pub async fn get_client_by_id(&self, client_id: &str) -> Result<ErpCliente> {
        // A URL está correta, usando o ID como parâmetro de query
        let url = format!("{}/clientes/?id={}", self.api_url, client_id);

        tracing::debug!("Chamando ERP para obter cliente por ID: {}", url);

        let response = self
            .client
            .get(&url)
            .header("Access-Token", &self.api_key) // Correto: Envia o Access Token diretamente
            .header("Secret-Access-Token", &self.erp_secret_key) // Correto: Envia o Secret Access Token
            // .header("Content-Type", "application/json") // Geralmente não é necessário para GET
            .send()
            .await?;

        if response.status().as_u16() == 404 {
            // Este erro pode nem ser alcançado se a API retornar 200 com data:[] para ID não encontrado
            return Err(AppError::NotFound(format!(
                "Cliente com ID {} não encontrado no ERP (status 404 da API)",
                client_id
            )));
        }

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_else(|_| "Corpo do erro ilegível".to_string());
            tracing::error!(
                "Erro ao buscar cliente por ID no ERP. Status: {}, Resposta: {}",
                status,
                error_text
            );
            return Err(AppError::ExternalServiceError(format!(
                "ERP API (visualizar cliente) retornou status: {}. Detalhes: {}",
                status, error_text
            )));
        }

        let response_value: Value = response.json().await.map_err(|e| {
            tracing::error!("Erro ao parsear JSON da resposta de visualizar cliente: {}", e);
            AppError::ExternalServiceError(format!(
                "Erro ao parsear resposta JSON do ERP (visualizar cliente): {}",
                e
            ))
        })?;
        
        // *** CORREÇÃO PRINCIPAL AQUI ***
        // O ERP retorna o cliente dentro de um array no campo "data".
        let client_data_array = response_value
            .get("data")
            .and_then(|d| d.as_array())
            .ok_or_else(|| {
                tracing::error!("Resposta do ERP para get_client_by_id não continha um array 'data' esperado. Resposta: {:?}", response_value);
                AppError::ExternalServiceError(
                    "Formato de resposta inválido do ERP: campo 'data' não é um array ou não encontrado.".to_string(),
                )
            })?;

        if let Some(client_object_value) = client_data_array.get(0) {
            // Agora passamos o objeto do cliente para parse_client_data
            self.parse_client_data(client_object_value.clone()).await
        } else {
            // Se o array "data" estiver vazio, o cliente não foi encontrado.
            Err(AppError::NotFound(format!(
                "Cliente com ID {} não encontrado nos dados da resposta do ERP (array 'data' vazio)",
                client_id
            )))
        }
    }

    async fn parse_clients_response(
        &self,
        data: Value,
        page: u32,
        per_page: u32,
    ) -> Result<ClientListResponse> {
        // Extrair dados de clientes
        let clients_data = data
            .get("data")
            .ok_or_else(|| AppError::ExternalServiceError(
                "Formato de resposta inválido do ERP: campo 'data' não encontrado".to_string(),
            ))?;

        // Extrair metadados de paginação
        let meta_data = data.get("meta").ok_or_else(|| AppError::ExternalServiceError(
            "Formato de resposta inválido do ERP: campo 'meta' não encontrado".to_string(),
        ))?;

        let pagination_meta = PaginationMeta {
            total_registros: meta_data.get("total_registros").and_then(|v| v.as_u64()),
            total_paginas: meta_data.get("total_paginas").and_then(|v| v.as_u64()).map(|v| v as u32),
            pagina_atual: meta_data.get("pagina_atual").and_then(|v| v.as_u64()).map(|v| v as u32),
            registros_por_pagina: meta_data.get("registros_por_pagina").and_then(|v| v.as_u64()).map(|v| v as u32),
            total_registros_pagina: meta_data.get("total_registros_pagina").and_then(|v| v.as_u64()).map(|v| v as u32),
        };

        let total = pagination_meta.total_registros.unwrap_or(0);

        let mut clients = Vec::new();

        if let Some(clients_array) = clients_data.as_array() {
            for client_data in clients_array {
                match self.parse_client_data(client_data.clone()).await {
                    Ok(client) => clients.push(client),
                    Err(e) => {
                        tracing::warn!("Erro ao parsear cliente do ERP: {}", e);
                        continue;
                    }
                }
            }
        }

        Ok(ClientListResponse {
            clients,
            total,
            page,
            per_page,
            meta: Some(pagination_meta),
        })
    }

    async fn parse_client_data(&self, data: Value) -> Result<ErpCliente> {
        // Extrair campos básicos
        let id = data
            .get("id")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AppError::ExternalServiceError(
                "ID do cliente não encontrado".to_string(),
            ))?
            .to_string();

        let nome = data
            .get("nome")
            .and_then(|v| v.as_str())
            .ok_or_else(|| AppError::ExternalServiceError(
                "Nome do cliente não encontrado".to_string(),
            ))?
            .to_string();

        // Extrair campos opcionais
        let tipo_pessoa = data.get("tipo_pessoa").and_then(|v| v.as_str()).map(|s| s.to_string());
        let razao_social = data.get("razao_social").and_then(|v| v.as_str()).map(|s| s.to_string());
        let cnpj = data.get("cnpj").and_then(|v| v.as_str()).map(|s| s.to_string());
        let inscricao_estadual = data.get("inscricao_estadual").and_then(|v| v.as_str()).map(|s| s.to_string());
        let inscricao_municipal = data.get("inscricao_municipal").and_then(|v| v.as_str()).map(|s| s.to_string());
        let tipo_contribuinte = data.get("tipo_contribuinte").and_then(|v| v.as_str()).map(|s| s.to_string());
        let responsavel = data.get("responsavel").and_then(|v| v.as_str()).map(|s| s.to_string());
        let cpf = data.get("cpf").and_then(|v| v.as_str()).map(|s| s.to_string());
        let rg = data.get("rg").and_then(|v| v.as_str()).map(|s| s.to_string());
        let data_nascimento = data.get("data_nascimento").and_then(|v| v.as_str()).map(|s| s.to_string());
        let sexo = data.get("sexo").and_then(|v| v.as_str()).map(|s| s.to_string());
        let email_acesso = data.get("email_acesso").and_then(|v| v.as_str()).map(|s| s.to_string());
        let telefone = data.get("telefone").and_then(|v| v.as_str()).map(|s| s.to_string());
        let celular = data.get("celular").and_then(|v| v.as_str()).map(|s| s.to_string());
        let fax = data.get("fax").and_then(|v| v.as_str()).map(|s| s.to_string());
        let email = data.get("email").and_then(|v| v.as_str()).map(|s| s.to_string());
        let vendedor_id = data.get("vendedor_id").and_then(|v| v.as_str()).map(|s| s.to_string());
        let nome_vendedor = data.get("nome_vendedor").and_then(|v| v.as_str()).map(|s| s.to_string());

        // Converter campos booleanos de string para bool
        let loja_virtual_is_ativo = data.get("loja_virtual_is_ativo")
            .and_then(|v| v.as_str())
            .map(|s| s == "1" || s.to_lowercase() == "true");

        let ativo = data.get("ativo")
            .and_then(|v| v.as_str())
            .map(|s| s == "1" || s.to_lowercase() == "true");

        // Parsear datas
        let cadastrado_em = data
            .get("cadastrado_em")
            .and_then(|v| v.as_str())
            .and_then(|s| NaiveDateTime::parse_from_str(s, "%Y-%m-%d %H:%M:%S").ok())
            .map(|dt| dt.and_utc());

        let modificado_em = data
            .get("modificado_em")
            .and_then(|v| v.as_str())
            .and_then(|s| NaiveDateTime::parse_from_str(s, "%Y-%m-%d %H:%M:%S").ok())
            .map(|dt| dt.and_utc());

        // Parsear endereços
        let mut enderecos = Vec::new();
        if let Some(enderecos_array) = data.get("enderecos").and_then(|v| v.as_array()) {
            for endereco_data in enderecos_array {
                if let Some(detalhes_endereco) = endereco_data.get("endereco") {
                    let endereco_detalhes = ErpDetalhesEndereco {
                        tipo_id: detalhes_endereco.get("tipo_id").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        nome_tipo: detalhes_endereco.get("nome_tipo").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        cep: detalhes_endereco.get("cep").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        logradouro: detalhes_endereco.get("logradouro").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        numero: detalhes_endereco.get("numero").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        complemento: detalhes_endereco.get("complemento").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        bairro: detalhes_endereco.get("bairro").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        pais: detalhes_endereco.get("pais").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        cidade_id: detalhes_endereco.get("cidade_id").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        nome_cidade: detalhes_endereco.get("nome_cidade").and_then(|v| v.as_str()).map(|s| s.to_string()),
                        estado: detalhes_endereco.get("estado").and_then(|v| v.as_str()).map(|s| s.to_string()),
                    };

                    enderecos.push(ErpEndereco {
                        endereco: endereco_detalhes,
                    });
                }
            }
        }

        Ok(ErpCliente {
            id,
            tipo_pessoa,
            nome,
            razao_social,
            cnpj,
            inscricao_estadual,
            inscricao_municipal,
            tipo_contribuinte,
            responsavel,
            cpf,
            rg,
            data_nascimento,
            sexo,
            loja_virtual_is_ativo,
            email_acesso,
            telefone,
            celular,
            fax,
            email,
            ativo,
            vendedor_id,
            nome_vendedor,
            cadastrado_em,
            modificado_em,
            enderecos,
        })
    }

    pub async fn create_erp_client(&self, payload: &CreateErpClientPayload) -> Result<ErpCliente> {
        // Validar tipo_pessoa
        match payload.tipo_pessoa.as_str() {
            "PF" | "PJ" | "ES" => {},
            _ => return Err(AppError::ValidationError("tipo_pessoa deve ser 'PF', 'PJ' ou 'ES'".to_string())),
        }

        // Validar nome
        if payload.nome.trim().is_empty() {
            return Err(AppError::ValidationError("nome não pode estar vazio".to_string()));
        }

        let url = format!("{}/clientes", self.api_url);

        // Criar payload para envio ao ERP
        let erp_payload = json!({
            "tipo_pessoa": payload.tipo_pessoa,
            "nome": payload.nome,
            "razao_social": payload.razao_social,
            "cnpj": payload.cnpj,
            "inscricao_estadual": payload.inscricao_estadual,
            "inscricao_municipal": payload.inscricao_municipal,
            "tipo_contribuinte": payload.tipo_contribuinte,
            "responsavel": payload.responsavel,
            "cpf": payload.cpf,
            "rg": payload.rg,
            "data_nascimento": payload.data_nascimento,
            "sexo": payload.sexo,
            "loja_virtual_is_ativo": 0, // Sempre 0 por padrão
            "email_acesso": payload.email_acesso,
            "telefone": payload.telefone,
            "celular": payload.celular,
            "fax": payload.fax,
            "email": payload.email,
            "ativo": payload.ativo.map(|v| if v { 1 } else { 0 }), // Converter boolean para inteiro
            "vendedor_id": payload.vendedor_id,
            "nome_vendedor": payload.nome_vendedor,
            "enderecos": payload.enderecos,
        });

        let response = self
            .client
            .post(&url)
            .header("Access-Token", self.api_key.clone())
            .header("Secret-Access-Token", &self.erp_secret_key)
            .header("Content-Type", "application/json")
            .json(&erp_payload)
            .send()
            .await?;

        if !response.status().is_success() {
            let status = response.status();
            let error_body = response.text().await.unwrap_or_else(|_| "Erro ao ler corpo da resposta".to_string());
            return Err(AppError::ExternalServiceError(
                format!("ERP API retornou status: {} - Detalhes: {}", status, error_body),
            ));
        }

        let response_data: Value = response.json().await?;
        
        // Extrair dados do cliente da resposta da API
        let client_data = response_data
            .get("data")
            .ok_or_else(|| {
                AppError::ExternalServiceError(
                    "Formato de resposta inválido do ERP: campo 'data' não encontrado".to_string()
                )
            })?;
        
        self.parse_client_data(client_data.clone()).await
    }

    pub async fn update_erp_client(
        &self,
        erp_client_id: &str,
        payload: &UpdateErpClientPayload,
    ) -> Result<ErpCliente> {
        let url = format!("{}/clientes/{}", self.api_url, erp_client_id);

        let erp_payload_for_api = json!({
            "tipo_pessoa": payload.tipo_pessoa.as_ref().unwrap(),
            "nome": payload.nome.as_ref().unwrap(),
            "razao_social": payload.razao_social,
            "cnpj": payload.cnpj,
            "inscricao_estadual": payload.inscricao_estadual,
            "inscricao_municipal": payload.inscricao_municipal,
            "tipo_contribuinte": payload.tipo_contribuinte,
            "responsavel": payload.responsavel,
            "cpf": payload.cpf,
            "rg": payload.rg,
            "data_nascimento": payload.data_nascimento,
            "sexo": payload.sexo,
            "loja_virtual_is_ativo": payload.loja_virtual_is_ativo,
            "email_acesso": payload.email_acesso,
            "telefone": payload.telefone,
            "celular": payload.celular,
            "fax": payload.fax,
            "email": payload.email,
            "ativo": payload.ativo,
            "vendedor_id": payload.vendedor_id,
            "nome_vendedor": payload.nome_vendedor,
            "enderecos": payload.enderecos,
        });

        let response = self
            .client
            .put(&url)
            .header("Access-Token", &self.api_key)
            .header("Secret-Access-Token", &self.erp_secret_key)
            .header("Content-Type", "application/json")
            .json(&erp_payload_for_api)
            .send()
            .await?;

        let status = response.status();
        let response_bytes = response.bytes().await.map_err(|e| {
            tracing::error!("Erro ao ler bytes da resposta do ERP (atualizar cliente): {}", e);
            AppError::ExternalServiceError(format!("Erro ao ler corpo da resposta do ERP: {}", e))
        })?;

        if !status.is_success() {
            if let Ok(json_error) = serde_json::from_slice::<Value>(&response_bytes) {
                 if let Some(message) = json_error.get("message").and_then(|m| m.as_str()) {
                    return Err(AppError::ExternalServiceError(format!(
                        "ERP API (editar cliente) retornou status: {}. Mensagem: {}",
                        status, message
                    )));
                 } else if let Some(data_node) = json_error.get("data") {
                    if let Some(error_message_in_data) = data_node.get("message").and_then(|m| m.as_str()) {
                        return Err(AppError::ExternalServiceError(format!(
                            "ERP API (editar cliente) retornou status: {}. Detalhes: {}",
                            status, error_message_in_data
                        )));
                    } else if data_node.is_string() {
                         return Err(AppError::ExternalServiceError(format!(
                            "ERP API (editar cliente) retornou status: {}. Detalhes: {}",
                            status, data_node.as_str().unwrap_or_default()
                        )));
                    }
                 }
            }
            return Err(AppError::ExternalServiceError(format!(
                "ERP API (editar cliente) retornou status: {}",
                status
            )));
        }

        let response_value: Value = serde_json::from_slice(&response_bytes).map_err(|e| {
            AppError::ExternalServiceError(format!("Erro ao parsear resposta JSON do ERP (atualizar cliente): {}", e))
        })?;

        let client_object_value = response_value
            .get("data")
            .ok_or_else(|| {
                AppError::ExternalServiceError(
                    "Formato de resposta inválido do ERP: campo 'data' não encontrado após atualizar cliente.".to_string(),
                )
            })?;

        self.parse_client_data(client_object_value.clone()).await
    }

    pub async fn health_check(&self) -> Result<bool> {
        let url = format!("{}/health", self.api_url);

        match self
            .client
            .get(&url)
            .header("Access-Token", self.api_key.clone())
            .header("Secret-Access-Token", &self.erp_secret_key)
            .send()
            .await
        {
            Ok(response) => Ok(response.status().is_success()),
            Err(_) => Ok(false),
        }
    }
}
