use serde_json::Value;

use crate::config::AppConfig;
use crate::error::{AppError, Result};
use crate::models::document::OutputFormat;

use carbone_sdk_rust::config::Config as CarboneSdkConfig;
use carbone_sdk_rust::carbone::Carbone;
use carbone_sdk_rust::types::{ApiJsonToken, JsonData, ApiVersion}; // Adicionado ApiVersion
use carbone_sdk_rust::template::TemplateId;
use carbone_sdk_rust::errors::CarboneError;
// anyhow não é necessário se os map_err estiverem corretos para CarboneError

#[derive(Clone)]
pub struct CarboneService {
    api_url: String, // URL do servidor Carbone self-hosted
    api_key: String, // Chave da <PERSON>, se o servidor on-premise usar
}

impl CarboneService {
    pub fn new(config: &AppConfig) -> Self {
        Self {
            api_url: config.carbone_api_url.clone(),
            api_key: config.carbone_api_key.clone(),
        }
    }

    // Método para gerar a partir de um template_id (que será o caso mais comum aqui,
    // pois os templates são gerenciados e armazenados no MinIO, e teremos seus IDs)
    pub async fn generate_document_from_template_id(
        &self,
        template_id_str: &str,
        json_data_for_template: Value,
        output_format: OutputFormat,
    ) -> Result<Vec<u8>> {
        // Criar a instância do Carbone dentro do método para evitar problemas de lifetime
        let api_version = match ApiVersion::new("4".to_string()) {
            Ok(version) => version,
            Err(_) => return Err(AppError::CarboneSdkError("Falha ao criar ApiVersion".to_string())),
        };

        let sdk_config = CarboneSdkConfig::new(
            self.api_url.clone(),
            30, // Timeout em segundos (ex: 30s) - ajuste conforme necessário
            api_version
        ).map_err(|e| AppError::CarboneSdkError(format!("Erro ao criar config do Carbone SDK: {}", e)))?;

        let api_token_option = if !self.api_key.is_empty() {
            Some(ApiJsonToken::new(self.api_key.clone())
                .map_err(|e| AppError::CarboneSdkError(format!("Erro ao criar token API Carbone: {}", e)))?)
        } else {
            None
        };
        
        let carbone_instance = Carbone::new(&sdk_config, api_token_option.as_ref())
            .map_err(|e: CarboneError| AppError::CarboneSdkError(e.to_string()))?;

        // O SDK espera que `json_data` inclua o campo `convertTo`
        // e os dados do template sob uma chave "data".
        let final_json_payload_str = serde_json::json!({
            "data": json_data_for_template,
            "convertTo": output_format.to_string().to_lowercase(),
        }).to_string();

        let sdk_json_data = JsonData::new(final_json_payload_str)
            .map_err(|e: CarboneError| AppError::CarboneSdkError(e.to_string()))?;

        let sdk_template_id = TemplateId::new(template_id_str.to_string())
            .map_err(|e: CarboneError| AppError::CarboneSdkError(e.to_string()))?;
        
        tracing::debug!("Chamando Carbone SDK para gerar relatório com template ID: {}", template_id_str);

        let report_content = carbone_instance
            .generate_report_with_template_id(sdk_template_id, sdk_json_data)
            .await
            .map_err(|e: CarboneError| {
                tracing::error!("Erro do Carbone SDK ao gerar relatório: {}", e.to_string());
                AppError::CarboneSdkError(e.to_string())
            })?;
        
        tracing::info!("Relatório gerado com sucesso pelo Carbone SDK.");
        Ok(report_content.to_vec()) // Convertido para Vec<u8>
    }

    // Se precisarmos gerar a partir de bytes de template (ex: baixados do MinIO)
    // e o template não estiver pré-carregado no Carbone Studio.
    pub async fn generate_document_from_template_bytes(
        &self,
        template_name: &str, // ex: "curriculo.odt"
        template_bytes: Vec<u8>,
        json_data_for_template: Value,
        output_format: OutputFormat,
    ) -> Result<Vec<u8>> {
        // Criar a instância do Carbone dentro do método para evitar problemas de lifetime
        let api_version = match ApiVersion::new("4".to_string()) {
            Ok(version) => version,
            Err(_) => return Err(AppError::CarboneSdkError("Falha ao criar ApiVersion".to_string())),
        };

        let sdk_config = CarboneSdkConfig::new(
            self.api_url.clone(),
            30, // Timeout em segundos (ex: 30s) - ajuste conforme necessário
            api_version
        ).map_err(|e| AppError::CarboneSdkError(format!("Erro ao criar config do Carbone SDK: {}", e)))?;

        let api_token_option = if !self.api_key.is_empty() {
            Some(ApiJsonToken::new(self.api_key.clone())
                .map_err(|e| AppError::CarboneSdkError(format!("Erro ao criar token API Carbone: {}", e)))?)
        } else {
            None
        };
        
        let carbone_instance = Carbone::new(&sdk_config, api_token_option.as_ref())
            .map_err(|e: CarboneError| AppError::CarboneSdkError(e.to_string()))?;

        let final_json_payload_str = serde_json::json!({
            "data": json_data_for_template,
            "convertTo": output_format.to_string().to_lowercase(),
        }).to_string();
        
        let sdk_json_data = JsonData::new(final_json_payload_str)
            .map_err(|e: CarboneError| AppError::CarboneSdkError(e.to_string()))?;

        tracing::debug!("Chamando Carbone SDK para gerar relatório com bytes de template: {}", template_name);
        
        // O SDK não tem um método direto para "generate_report_from_template_bytes" que também aceite `convertTo`.
        // O método `generate_report` do SDK parece ser para um fluxo onde o `convertTo` está dentro do `json_data`.
        // Vamos usar `generate_report` e garantir que `json_data` (que se torna `sdk_json_data`) contenha `convertTo`.

        let report_content = carbone_instance
            .generate_report(template_name.to_string(), template_bytes, sdk_json_data, None, None)
            .await
            .map_err(|e: CarboneError| {
                tracing::error!("Erro do Carbone SDK ao gerar relatório com bytes: {}", e.to_string());
                AppError::CarboneSdkError(e.to_string())
            })?;
        
        tracing::info!("Relatório gerado com sucesso pelo Carbone SDK a partir de bytes.");
        Ok(report_content.to_vec()) // Convertido para Vec<u8>
    }
}

// Manter a implementação ToString para OutputFormat
impl ToString for OutputFormat {
    fn to_string(&self) -> String {
        match self {
            OutputFormat::Pdf => "pdf".to_string(),
            OutputFormat::Docx => "docx".to_string(),
            OutputFormat::Odt => "odt".to_string(),
        }
    }
}