use std::io::{<PERSON><PERSON><PERSON>, Read, Write};
use serde_json::Value;
use zip::{ZipArchive, ZipWriter, write::FileOptions};
use base64::Engine;

use crate::error::{AppError, Result};
use crate::models::document::OutputFormat;

#[derive(Clone)]
pub struct ImageInsertionService;

impl ImageInsertionService {
    pub fn new() -> Self {
        Self
    }


    pub async fn replace_placeholder_image(
        &self,
        document_bytes: Vec<u8>,
        image_data: &Value,
        output_format: &OutputFormat,
    ) -> Result<Vec<u8>> {
        match output_format {
            OutputFormat::Docx => self.replace_image_in_docx(document_bytes, image_data).await,
            OutputFormat::Pdf => self.replace_image_in_pdf(document_bytes, image_data).await,
            OutputFormat::Odt => {
                tracing::warn!("Inserção de imagem em ODT ainda não implementada");
                Ok(document_bytes)
            },
        }
    }

    
    async fn replace_image_in_docx(&self, doc_bytes: Vec<u8>, image_data: &Value) -> Result<Vec<u8>> {
        tracing::info!("🖼️ Iniciando substituição de imagem no documento DOCX");

        // 1. Decodificar base64 da imagem real ou baixar de URL
        let real_image_bytes = self.decode_base64_image(image_data).await?;
        tracing::debug!("Imagem decodificada. Tamanho: {} bytes", real_image_bytes.len());

        // 2. Processar imagem (redimensionar se necessário)
        let processed_image_bytes = self.process_image_for_docx(real_image_bytes)?;

        // 3. Abrir DOCX como ZIP
        let cursor = Cursor::new(doc_bytes);
        let mut zip = ZipArchive::new(cursor)
            .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao abrir DOCX: {}", e)))?;

        // 4. Criar novo ZIP com imagem substituída
        let mut new_zip_data = Vec::new();
        {
            let mut new_zip_writer = ZipWriter::new(Cursor::new(&mut new_zip_data));

            // Iterar por todos os arquivos do ZIP original
            for i in 0..zip.len() {
                let mut file = zip.by_index(i)
                    .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao acessar arquivo no ZIP: {}", e)))?;
                
                let file_name = file.name().to_string();
                
                if file_name == "word/media/image1.png" {
                    // Substituir pela imagem real
                    tracing::debug!("Substituindo {}", file_name);
                    new_zip_writer.start_file(&file_name, FileOptions::default())
                        .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao criar arquivo no ZIP: {}", e)))?;
                    new_zip_writer.write_all(&processed_image_bytes)
                        .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao escrever imagem: {}", e)))?;
                } else {
                    // Copiar arquivo original
                    let mut contents = Vec::new();
                    file.read_to_end(&mut contents)
                        .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao ler arquivo {}: {}", file_name, e)))?;
                    
                    new_zip_writer.start_file(&file_name, FileOptions::default())
                        .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao criar arquivo no ZIP: {}", e)))?;
                    new_zip_writer.write_all(&contents)
                        .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao escrever arquivo {}: {}", file_name, e)))?;
                }
            }

            new_zip_writer.finish()
                .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao finalizar ZIP: {}", e)))?;
        }

        tracing::info!("✅ Imagem substituída com sucesso no documento DOCX");
        Ok(new_zip_data)
    }

    /// Placeholder para PDF - Currículos só devem usar DOCX
    async fn replace_image_in_pdf(&self, pdf_bytes: Vec<u8>, _image_data: &Value) -> Result<Vec<u8>> {
        tracing::warn!("🚫 Tentativa de inserir imagem em PDF detectada");
        tracing::info!("💡 RECOMENDAÇÃO: Use formato DOCX para currículos com imagem");
        
        // Retornar PDF original sem modificação
        // Currículos devem ser validados no backend para serem apenas DOCX
        Ok(pdf_bytes)
    }

    /// Decodifica imagem base64 ou baixa de URL HTTP (remove data URI se presente)
    async fn decode_base64_image(&self, image_data: &Value) -> Result<Vec<u8>> {
        let image_str = image_data.as_str()
            .ok_or_else(|| AppError::DocumentProcessingError("Dados de imagem inválidos".to_string()))?;
        tracing::info!("Decodificando imagem: {}", &image_str);


        // Se for uma URL HTTP, baixar a imagem
        if image_str.starts_with("http://") || image_str.starts_with("https://") {
            tracing::info!("Baixando imagem de URL: {}", image_str);
            
            let response = reqwest::get(image_str).await
                .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao baixar imagem de URL: {}", e)))?;
            
            if !response.status().is_success() {
                return Err(AppError::DocumentProcessingError(format!("Erro HTTP ao baixar imagem: {}", response.status())));
            }
            
            let image_bytes = response.bytes().await
                .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao ler bytes da imagem: {}", e)))?;
            
            Ok(image_bytes.to_vec())
        } else {
            // Remove data URI prefix se existir (ex: "data:image/jpeg;base64,")
            let clean_base64 = if image_str.starts_with("data:image/") {
                let base64_part = image_str.split(',').nth(1)
                    .ok_or_else(|| AppError::DocumentProcessingError("Formato base64 inválido".to_string()))?;
                

                
                base64_part
            } else {

                image_str
            };

            base64::engine::general_purpose::STANDARD.decode(clean_base64)
                .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao decodificar base64: {}", e)))
        }
    }

    /// Processa a imagem para DOCX (redimensiona se necessário)
    fn process_image_for_docx(&self, image_bytes: Vec<u8>) -> Result<Vec<u8>> {
        use image::{ImageFormat, DynamicImage};

        tracing::info!("Processando imagem para DOCX. Primeiros 10 bytes: {:?}", &image_bytes.get(..10));

        // Carregar imagem
        let img = image::load_from_memory(&image_bytes)
            .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao carregar imagem: {}", e)))?;

        // Redimensionar se for muito grande (máximo 800x800 para performance)
        let processed_img = if img.width() > 800 || img.height() > 800 {
            tracing::debug!("Redimensionando imagem de {}x{} para caber em 800x800", img.width(), img.height());
            img.thumbnail(800, 800)
        } else {
            img
        };

        // Converter para PNG
        let mut png_bytes = Vec::new();
        let mut cursor = Cursor::new(&mut png_bytes);
        processed_img.write_to(&mut cursor, ImageFormat::Png)
            .map_err(|e| AppError::DocumentProcessingError(format!("Erro ao converter imagem para PNG: {}", e)))?;

        Ok(png_bytes)
    }

    /// Processa a imagem para PDF (mesmo que DOCX por enquanto)
    fn process_image_for_pdf(&self, image_bytes: Vec<u8>) -> Result<Vec<u8>> {
        // Reutilizar a mesma lógica do DOCX
        self.process_image_for_docx(image_bytes)
    }
} 