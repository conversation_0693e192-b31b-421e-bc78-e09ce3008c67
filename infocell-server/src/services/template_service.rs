use crate::error::{AppError, Result};
use crate::models::{DocumentTemplateMetadata, TemplateListResponse, TemplateType};
use crate::repositories::template_repository::TemplateRepository;
use crate::services::storage_service::StorageService;
use serde_json;

#[derive(Clone)]
pub struct TemplateService {
    repository: TemplateRepository,
    storage_service: StorageService,
}

impl TemplateService {
    pub fn new(repository: TemplateRepository, storage_service: StorageService) -> Self {
        Self {
            repository,
            storage_service,
        }
    }

    pub async fn create_template(
        &self,
        name: String,
        description: Option<String>,
        template_type: TemplateType,
        file_data: Vec<u8>,
        file_name: String,
        content_type: String,
        uploader_id: String,
        uploader_name: String,
    ) -> Result<String> {
        // Validar arquivo
        if file_data.is_empty() {
            return Err(AppError::ValidationError("Arquivo vazio".to_string()));
        }

        // Validar tipo de arquivo
        if !self.is_valid_file_type(&content_type) {
            return Err(AppError::ValidationError(
                "Tipo de arquivo não suportado".to_string(),
            ));
        }

        // Gerar caminho único no storage
        let file_path = format!("templates/{}/{}", uuid::Uuid::new_v4(), file_name);

        // Upload do arquivo para MinIO
        self.storage_service
            .upload_file(&file_path, file_data.clone(), &content_type)
            .await?;

        // Criar metadados do template
        let template = DocumentTemplateMetadata {
            id: None,
            name,
            description,
            file_path: file_path.clone(),
            file_name,
            file_size: file_data.len() as u64,
            content_type,
            template_type,
            uploader_id,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            is_active: true,
        };

        // Salvar no banco de dados
        let id = self.repository.create(template).await?;

        tracing::info!("Template criado com sucesso: {}", id);
        Ok(id.to_string())
    }

    pub async fn get_template(&self, id: &str) -> Result<DocumentTemplateMetadata> {
        self.repository.find_by_id(id).await
    }

    pub async fn list_templates(
        &self,
        page: u32,
        per_page: u32,
        template_type: Option<TemplateType>,
        is_active: Option<bool>,
    ) -> Result<TemplateListResponse> {
        let (templates, total) = self
            .repository
            .find_all(page, per_page, template_type, is_active)
            .await?;

        Ok(TemplateListResponse {
            templates,
            total,
            page,
            per_page,
        })
    }

    pub async fn delete_template(&self, id: &str) -> Result<()> {
        // Buscar template para obter o caminho do arquivo
        let template = self.repository.find_by_id(id).await?;

        // Excluir arquivo do storage
        self.storage_service
            .delete_file(&template.file_path)
            .await?;

        // Excluir do banco de dados
        let deleted = self.repository.delete(id).await?;
        
        if !deleted {
            return Err(AppError::NotFound("Template não encontrado".to_string()));
        }

        tracing::info!("Template excluído com sucesso: {}", id);
        Ok(())
    }

    pub async fn update_template(&self, id: &str, update_request: crate::handlers::template_handlers::UpdateTemplateRequest) -> Result<()> {
        // Buscar template existente
        let current_template = self.repository.find_by_id(id).await?;

        // Preparar dados de atualização
        let name = update_request.name.unwrap_or(current_template.name);
        let description = update_request.description.or(current_template.description);
        let is_active = update_request.is_active.unwrap_or(current_template.is_active);

        // Validar nome se foi fornecido
        if name.trim().is_empty() {
            return Err(AppError::ValidationError("Nome não pode ser vazio".to_string()));
        }

        // Criar JSON com as atualizações
        let updates = serde_json::json!({
            "name": name,
            "description": description,
            "is_active": is_active
        });

        // Atualizar template no banco de dados
        let updated = self.repository.update(id, updates).await?;
        
        if !updated {
            return Err(AppError::NotFound("Template não encontrado".to_string()));
        }

        tracing::info!("Template atualizado com sucesso: {}", id);
        Ok(())
    }

    pub async fn search_templates(&self, name: &str) -> Result<Vec<DocumentTemplateMetadata>> {
        if name.trim().is_empty() {
            return Err(AppError::ValidationError(
                "Nome de busca não pode ser vazio".to_string(),
            ));
        }

        self.repository.search_by_name(name).await
    }

    pub async fn get_template_file(&self, id: &str) -> Result<(Vec<u8>, String, String)> {
        let template = self.repository.find_by_id(id).await?;
        
        let file_data = self
            .storage_service
            .download_file(&template.file_path)
            .await?;

        Ok((file_data, template.file_name, template.content_type))
    }

    fn is_valid_file_type(&self, content_type: &str) -> bool {
        matches!(
            content_type,
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" // .docx
            | "application/vnd.oasis.opendocument.text" // .odt
            | "application/msword" // .doc
        )
    }
}
