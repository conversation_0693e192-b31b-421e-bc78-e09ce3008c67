use crate::{
    error::{AppError, Result},
    models::RechargePackage,
    repositories::{
        recharge_package_repository::RechargePackageRepository,
        recharge_code_repository::RechargeCodeRepository,
    },
};
use bson::oid::ObjectId;

#[derive(Clone)]
pub struct RechargePackageService {
    repo: RechargePackageRepository,
    code_repo: RechargeCodeRepository,
}

impl RechargePackageService {
    pub fn new(repo: RechargePackageRepository, code_repo: RechargeCodeRepository) -> Self {
        Self { repo, code_repo }
    }

    pub async fn create_package(&self, package: &RechargePackage) -> Result<ObjectId> {
        self.repo.create(package).await
    }

    pub async fn get_package(&self, id: &str) -> Result<Option<RechargePackage>> {
        self.repo.find_by_id(id).await
    }

    pub async fn list_packages(&self) -> Result<Vec<RechargePackage>> {
        self.repo.find_all().await
    }

    pub async fn update_package(&self, id: &str, updates: &RechargePackage) -> Result<bool> {
        self.repo.update(id, updates).await
    }

    pub async fn delete_package(&self, id: &str) -> Result<bool> {
        // 1. Encontrar o pacote para obter seu `package_type`
        if let Some(package_to_delete) = self.get_package(id).await? {

            // 2. Verificar se existem códigos usando este `package_type`
            let code_count = self.code_repo.count_by_package_type(&package_to_delete.package_type).await?;

            if code_count > 0 {
                // 3. Se houver códigos, retornar um erro claro
                return Err(AppError::BadRequest(format!(
                    "Não é possível excluir o pacote '{}', pois ele está associado a {} código(s) em estoque.",
                    package_to_delete.package_name, code_count
                )));
            }

            // 4. Se não houver códigos, proceder com a exclusão
            self.repo.delete(id).await

        } else {
            Err(AppError::NotFound("Recharge package not found".to_string()))
        }
    }
} 