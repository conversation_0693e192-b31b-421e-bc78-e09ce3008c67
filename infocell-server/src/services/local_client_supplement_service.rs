use serde_json::{Value, from_value};
use chrono::Utc;

use crate::error::{AppError, Result};
use crate::models::local_client_data::{LocalClientSupplement, LocalEducationEntry, LocalExperienceEntry};
use crate::repositories::local_client_supplement_repository::LocalClientSupplementRepository;
use crate::repositories::DatabaseManager; // Para instanciar o repositório

#[derive(Clone)]
pub struct LocalClientSupplementService {
    repository: LocalClientSupplementRepository,
}

impl LocalClientSupplementService {
    pub fn new(db_manager: &DatabaseManager) -> Self {
        Self {
            repository: LocalClientSupplementRepository::new(&db_manager.database),
        }
    }

    pub async fn get_by_erp_client_id(&self, erp_client_id: &str) -> Result<Option<LocalClientSupplement>> {
        self.repository.find_by_erp_client_id(erp_client_id).await
    }

    /// Salva (cria ou atualiza) os dados suplementares do cliente.
    /// Recebe `additional_data` como `serde_json::Value` (vindo do `DocumentGenerationRequest`),
    /// desserializa para `LocalClientSupplement` e então chama o repositório.
    pub async fn save_supplemental_data(
        &self,
        erp_client_id: &str,
        additional_data: Value, // JSON vindo do frontend
    ) -> Result<String> {
        // Buscar documento existente para preservar dados
        let existing_supplement = self.repository.find_by_erp_client_id(erp_client_id).await?;
        
        // Criar/atualizar suplemento baseado no documento existente
        let mut supplement = if let Some(mut existing) = existing_supplement {
            // Documento existe - preservar dados existentes e atualizar campos
            tracing::info!("Atualizando dados suplementares existentes para ERP ID: {}", erp_client_id);
            
            // Atualizar apenas os campos presentes no additional_data
            if let Some(personal_info) = additional_data.get("personalInfo") {
                existing.personalInfo = Some(personal_info.clone());
            }
            if let Some(objective) = additional_data.get("objective") {
                existing.objective = objective.as_str().map(|s| s.to_string());
            }
            if let Some(education_val) = additional_data.get("education_history") {
                match from_value::<Vec<LocalEducationEntry>>(education_val.clone()) {
                    Ok(education) => existing.education_history = Some(education),
                    Err(e) => tracing::warn!("Falha ao deserializar education_history: {}. Mantendo valor existente.", e),
                }
            }
            if let Some(experience_val) = additional_data.get("professional_experience") {
                match from_value::<Vec<LocalExperienceEntry>>(experience_val.clone()) {
                    Ok(experience) => existing.professional_experience = Some(experience),
                    Err(e) => tracing::warn!("Falha ao deserializar professional_experience: {}. Mantendo valor existente.", e),
                }
            }
            if let Some(qualifications) = additional_data.get("qualifications_summary") {
                existing.qualifications_summary = qualifications.as_str().map(|s| s.to_string());
            }
            if let Some(notes) = additional_data.get("additional_notes") {
                existing.additional_notes = notes.as_str().map(|s| s.to_string());
            }
            
            existing.updated_at = Utc::now();
            existing
        } else {
            // Novo documento - criar do zero
            tracing::info!("Criando novos dados suplementares para ERP ID: {}", erp_client_id);
            
            let education_history = additional_data.get("education_history")
                .and_then(|v| from_value(v.clone()).ok());

            let professional_experience = additional_data.get("professional_experience")
                .and_then(|v| from_value(v.clone()).ok());

            LocalClientSupplement {
                id: None,
                erp_client_id: erp_client_id.to_string(),
                personalInfo: additional_data.get("personalInfo").cloned(),
                objective: additional_data.get("objective").and_then(|v| v.as_str()).map(|s| s.to_string()),
                education_history,
                professional_experience,
                qualifications_summary: additional_data.get("qualifications_summary").and_then(|v| v.as_str()).map(|s| s.to_string()),
                additional_notes: additional_data.get("additional_notes").and_then(|v| v.as_str()).map(|s| s.to_string()),
                created_at: Utc::now(),
                updated_at: Utc::now(),
            }
        };

        self.repository.upsert_by_erp_client_id(&mut supplement).await
    }
}