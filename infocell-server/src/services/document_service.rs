use chrono::{Utc, DateTime, NaiveDate, Local};
use serde_json::{Value, json};
use uuid::Uuid;
use std::collections::HashMap;
use base64::Engine;

use crate::error::{AppError, Result};
use crate::models::document::{DocumentGenerationRequest, GeneratedDocument, OutputFormat};
use crate::models::erp::{ErpCliente, UpdateErpClientPayload, CreateErpClientPayload};
use crate::models::template::TemplateType;

use crate::services::erp_service::ErpService;
use crate::services::template_service::TemplateService;
use crate::services::storage_service::StorageService;
use crate::services::carbone_service::CarboneService;
use crate::services::local_client_supplement_service::LocalClientSupplementService;
use crate::services::image_insertion_service::ImageInsertionService;

use crate::repositories::generated_document_repository::GeneratedDocumentRepository;
use crate::repositories::DatabaseManager;

// Funções auxiliares para formatação de dados para o Carbone.io
fn format_optional_string_date_to_ddmmyyyy(date_opt_str: &Option<String>) -> Value {
    match date_opt_str {
        Some(date_str) => {
            if date_str.is_empty() {
                return Value::Null;
            }
            // Tentar parsear YYYY-MM-DD ou formato ISO DateTime
            if let Ok(parsed_date) = NaiveDate::parse_from_str(date_str, "%Y-%m-%d") {
                Value::String(parsed_date.format("%d/%m/%Y").to_string())
            } else if let Ok(parsed_dt) = date_str.parse::<DateTime<Utc>>() {
                Value::String(parsed_dt.format("%d/%m/%Y").to_string())
            } else {
                Value::String(date_str.clone()) // Fallback se não reconhecer o formato
            }
        }
        None => Value::Null,
    }
}

fn format_iso_string_date_to_mmyyyy(date_iso_opt_str: Option<&Value>) -> Option<String> {
    date_iso_opt_str
        .and_then(|v| v.as_str())
        .and_then(|s| s.parse::<DateTime<Utc>>().ok())
        .map(|dt| dt.format("%m/%Y").to_string())
}

fn format_period_from_iso_strings(start_date_iso_opt: Option<&Value>, end_date_iso_opt: Option<&Value>) -> Value {
    let start_mmyyyy = format_iso_string_date_to_mmyyyy(start_date_iso_opt);
    let end_mmyyyy = format_iso_string_date_to_mmyyyy(end_date_iso_opt);

    match (start_mmyyyy, end_mmyyyy) {
        (Some(s), Some(e)) => Value::String(format!("{} - {}", s, e)),
        (Some(s), None) => Value::String(format!("{} - Atual", s)),
        (None, Some(e)) => Value::String(format!("Até {}", e)),
        (None, None) => Value::Null,
    }
}

fn build_endereco_completo(enderecos: &[crate::models::erp::ErpEndereco]) -> Value {
    if let Some(endereco) = enderecos.get(0) {
        let logradouro = endereco.endereco.logradouro.as_deref().unwrap_or("");
        let numero = endereco.endereco.numero.as_deref().unwrap_or("");
        let complemento = endereco.endereco.complemento.as_deref().unwrap_or("");
        let bairro = endereco.endereco.bairro.as_deref().unwrap_or("");
        let cidade = endereco.endereco.nome_cidade.as_deref().unwrap_or("");
        let estado = endereco.endereco.estado.as_deref().unwrap_or("");
        let cep = endereco.endereco.cep.as_deref().unwrap_or("");

        let mut endereco_parts = vec![];

        if !logradouro.is_empty() {
            if !numero.is_empty() {
                endereco_parts.push(format!("{}, {}", logradouro, numero));
            } else {
                endereco_parts.push(logradouro.to_string());
            }
        }

        if !complemento.is_empty() {
            endereco_parts.push(complemento.to_string());
        }

        if !bairro.is_empty() {
            endereco_parts.push(format!("{}", bairro));
        }

        if !cidade.is_empty() && !estado.is_empty() {
            endereco_parts.push(format!("{} - {}", cidade, estado));
        } else if !cidade.is_empty() {
            endereco_parts.push(cidade.to_string());
        }

        if !cep.is_empty() {
            endereco_parts.push(format!("CEP {}", cep));
        }

        if endereco_parts.is_empty() {
            Value::Null
        } else {
            Value::String(endereco_parts.join(" - "))
        }
    } else {
        Value::Null
    }
}

fn map_erp_client_to_carbone_erp_format(erp_client: &ErpCliente, additional_data: &Option<Value>) -> Value {
    // Extract fields from additional_data.personalInfo that are not in ERP but needed for the erp section
    let mut estado_civil = Value::Null;
    let mut nacionalidade = Value::String("Brasileiro".to_string()); // Default value
    let mut cnh = Value::Null;
    let mut tem_filhos = Value::Null;

    if let Some(additional_data_value) = additional_data {
        if let Some(personal_info) = additional_data_value.get("personalInfo") {
            if let Some(civil_status) = personal_info.get("civilStatus").and_then(|v| v.as_str()) {
                if !civil_status.trim().is_empty() {
                    estado_civil = Value::String(civil_status.to_string());
                }
            }

            if let Some(nationality) = personal_info.get("nacionalidade").and_then(|v| v.as_str()) {
                if !nationality.trim().is_empty() {
                    nacionalidade = Value::String(nationality.to_string());
                }
            }

            if let Some(cnh_value) = personal_info.get("driverLicenseType")
                .and_then(|v| v.as_str())
                .filter(|s| !s.trim().is_empty())
                .filter(|s| *s != "Não possui" && *s != "Nenhuma") // Filtrar valores que indicam ausência de CNH
                .map(|s| s.to_string()) {
                cnh = Value::String(cnh_value);
            }

            if let Some(has_children) = personal_info.get("hasChildren").and_then(|v| v.as_str()) {
                if !has_children.trim().is_empty() {
                    tem_filhos = Value::String(has_children.to_string());
                }
            }
        }
    }

    json!({
        "nomeCompleto": erp_client.nome,
        "dataNascimento": format_optional_string_date_to_ddmmyyyy(&erp_client.data_nascimento),
        "estadoCivil": estado_civil,
        "nacionalidade": nacionalidade,
        "cpf": erp_client.cpf.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null),
        "rg": erp_client.rg.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null),
        "enderecoCompleto": build_endereco_completo(&erp_client.enderecos),
        "telefonePrincipal": erp_client.telefone.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null),
        "celularPrincipal": erp_client.celular.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null),
        "emailPrincipal": erp_client.email.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null),
        "cnh": cnh,
        "temFilhos": tem_filhos
    })
}

fn map_additional_data_to_carbone_cv_format(additional_data: &Value) -> Value {
    // Extract urlFotoPerfil from additional_data or personalInfo
    let url_foto_perfil = additional_data.get("urlFotoPerfil")
        .cloned()
        .or_else(|| {
            additional_data.get("personalInfo")
                .and_then(|pi| pi.get("profilePictureUrl"))
                .cloned()
        })
        .unwrap_or(Value::Null);
    


    json!({
        "urlFotoPerfil": url_foto_perfil,
        "objetivoProfissional": additional_data.get("objective").cloned().unwrap_or(Value::Null),
        "formacaoAcademica": additional_data.get("education_history")
            .and_then(|arr_val| arr_val.as_array())
            .and_then(|arr| {
                // Filtrar apenas itens que tem dados reais (curso ou instituição preenchidos)
                let valid_items: Vec<Value> = arr.iter()
                    .filter(|edu_item| {
                        let has_course = edu_item.get("course")
                            .and_then(|v| v.as_str())
                            .map(|s| !s.trim().is_empty())
                            .unwrap_or(false);
                        let has_institution = edu_item.get("institution")
                            .and_then(|v| v.as_str())
                            .map(|s| !s.trim().is_empty())
                            .unwrap_or(false);
                        
                        has_course || has_institution
                    })
                    .map(|edu_item| {
                        // Extract nivel from the education item if available
                        let nivel = edu_item.get("level")
                            .or_else(|| edu_item.get("courseType"))
                            .and_then(|v| v.as_str())
                            .map(|s| Value::String(s.to_string()))
                            .unwrap_or(Value::Null);

                        json!({
                            "instituicao": edu_item.get("institution").cloned().unwrap_or(Value::Null),
                            "curso": edu_item.get("course").cloned().unwrap_or(Value::Null),
                            "nivel": nivel,
                            "periodo": format_period_from_iso_strings(
                                edu_item.get("start_date"),
                                edu_item.get("end_date")
                            ),
                            "detalhes": edu_item.get("additional_info").cloned().unwrap_or(Value::Null)
                        })
                    })
                    .collect();
                
                // Só retorna o array se há itens válidos, senão retorna None para que seja convertido em Value::Null
                if valid_items.is_empty() {
                    None
                } else {
                    Some(valid_items)
                }
            })
            .map(|valid_items| Value::Array(valid_items))
            .unwrap_or(Value::Null),
        "experienciaProfissional": additional_data.get("professional_experience")
            .and_then(|arr_val| arr_val.as_array())
            .and_then(|arr| {
                // Filtrar apenas itens que tem dados reais (empresa ou cargo preenchidos)
                let valid_items: Vec<Value> = arr.iter()
                    .filter(|exp_item| {
                        let has_company = exp_item.get("company")
                            .and_then(|v| v.as_str())
                            .map(|s| !s.trim().is_empty())
                            .unwrap_or(false);
                        let has_role = exp_item.get("role")
                            .and_then(|v| v.as_str())
                            .map(|s| !s.trim().is_empty())
                            .unwrap_or(false);
                        
                        has_company || has_role
                    })
                    .map(|exp_item| {
                        // Extract localidade from the experience item if available
                        let localidade = exp_item.get("localidade")
                            .or_else(|| exp_item.get("location"))
                            .and_then(|v| v.as_str())
                            .map(|s| Value::String(s.to_string()))
                            .unwrap_or(Value::Null);

                        json!({
                            "empresa": exp_item.get("company").cloned().unwrap_or(Value::Null),
                            "cargo": exp_item.get("role").cloned().unwrap_or(Value::Null),
                            "periodo": format_period_from_iso_strings(
                                exp_item.get("start_date"),
                                exp_item.get("end_date")
                            ),
                            "localidade": localidade,
                            "atividades": exp_item.get("activities").cloned().unwrap_or(Value::Null)
                        })
                    })
                    .collect();
                
                // Só retorna o array se há itens válidos, senão retorna None para que seja convertido em Value::Null
                if valid_items.is_empty() {
                    None
                } else {
                    Some(valid_items)
                }
            })
            .map(|valid_items| Value::Array(valid_items))
            .unwrap_or(Value::Null),
        "habilidadesCompetencias": additional_data.get("qualifications_summary").cloned().unwrap_or(Value::Null),
        "informacoesComplementares": additional_data.get("additional_notes").cloned().unwrap_or(Value::Null)
    })
}

fn map_form_personal_info_to_carbone_erp_format(personal_info_val: &Value) -> Value {
    // Construir seção 'erp' do Carbone estritamente dos dados do formulário personalInfo
    let estado_civil = personal_info_val.get("civilStatus")
        .and_then(|v| v.as_str())
        .filter(|s| !s.trim().is_empty())
        .map(|s| Value::String(s.to_string()))
        .unwrap_or(Value::Null);

    let nacionalidade = personal_info_val.get("nacionalidade")
        .and_then(|v| v.as_str())
        .filter(|s| !s.trim().is_empty())
        .map(|s| Value::String(s.to_string()))
        .unwrap_or(Value::String("Brasileiro".to_string()));

    let cnh = personal_info_val.get("driverLicenseType")
        .and_then(|v| v.as_str())
        .filter(|s| !s.trim().is_empty())
        .filter(|s| *s != "Não possui" && *s != "Nenhuma") // Filtrar valores que indicam ausência de CNH
        .map(|s| Value::String(s.to_string()))
        .unwrap_or(Value::Null);

    let tem_filhos = personal_info_val.get("hasChildren")
        .and_then(|v| v.as_str())
        .filter(|s| !s.trim().is_empty())
        .map(|s| Value::String(s.to_string()))
        .unwrap_or(Value::Null);

    // Mapeamento do nível de formação para o template
    let nivel_de_formacao_cv = personal_info_val.get("levelOfEducation")
        .and_then(|v| v.as_str())
        .filter(|s| !s.trim().is_empty())
        .map(|level| {
            let formatted_level = match level {
                "fundamental_completo" => "Ensino Fundamental Completo",
                "fundamental_incompleto" => "Ensino Fundamental Incompleto",
                "medio_completo" => "Ensino Médio Completo",
                "medio_incompleto" => "Ensino Médio Incompleto",
                "superior_completo" => "Ensino Superior Completo",
                "superior_incompleto" => "Ensino Superior Incompleto",
                "pos_graduacao" => "Pós-graduação",
                "mestrado" => "Mestrado",
                "doutorado" => "Doutorado",
                _ => level // Usar o valor original se não corresponder a nenhum padrão
            };
            Value::String(formatted_level.to_string())
        })
        .unwrap_or(Value::Null);

    // Construir endereço completo dos campos do formulário
    let endereco_completo = build_endereco_completo_from_form(personal_info_val);

    json!({
        "nomeCompleto": personal_info_val.get("fullName").and_then(|v| v.as_str()).map(|s| Value::String(s.to_string())).unwrap_or(Value::Null),
        "dataNascimento": format_optional_string_date_to_ddmmyyyy(&personal_info_val.get("dateOfBirth").and_then(|v| v.as_str()).map(|s| s.to_string())),
        "estadoCivil": estado_civil,
        "nacionalidade": nacionalidade,
        "nivelDeFormacaoCV": nivel_de_formacao_cv,
        "cpf": personal_info_val.get("cpf").and_then(|v| v.as_str()).map(|s| Value::String(s.to_string())).unwrap_or(Value::Null),
        "rg": personal_info_val.get("rg").and_then(|v| v.as_str()).map(|s| Value::String(s.to_string())).unwrap_or(Value::Null),
        "enderecoCompleto": endereco_completo,
        "telefonePrincipal": personal_info_val.get("phone1").and_then(|v| v.as_str()).map(|s| Value::String(s.to_string())).unwrap_or(Value::Null),
        "celularPrincipal": personal_info_val.get("phone2").and_then(|v| v.as_str()).map(|s| Value::String(s.to_string())).unwrap_or(Value::Null),
        "emailPrincipal": personal_info_val.get("email").and_then(|v| v.as_str()).map(|s| Value::String(s.to_string())).unwrap_or(Value::Null),
        "cnh": cnh,
        "temFilhos": tem_filhos
    })
}

fn build_endereco_completo_from_form(personal_info_val: &Value) -> Value {
    let address = personal_info_val.get("address").and_then(|v| v.as_str()).unwrap_or("").trim();
    let city = personal_info_val.get("city").and_then(|v| v.as_str()).unwrap_or("").trim();
    let state = personal_info_val.get("state").and_then(|v| v.as_str()).unwrap_or("").trim();

    let mut endereco_parts = vec![];

    if !address.is_empty() {
        endereco_parts.push(address.to_string());
    }

    if !city.is_empty() && !state.is_empty() {
        endereco_parts.push(format!("{} - {}", city, state));
    } else if !city.is_empty() {
        endereco_parts.push(city.to_string());
    }

    if endereco_parts.is_empty() {
        Value::Null
    } else {
        Value::String(endereco_parts.join(" - "))
    }
}

// Função para mapear dados de Procuração para formato Carbone
fn map_proxy_data_to_carbone_format(
    outorgante_erp: &ErpCliente,
    outorgante_data: &Value,
    outorgado_data: &Value,
    procuracao_data: &Value
) -> Value {
    // Construir endereço completo do outorgante a partir dos dados do formulário
    let outorgante_endereco_completo = if let (Some(endereco), Some(municipio), Some(estado), Some(cep)) = (
        outorgante_data.get("endereco").and_then(|v| v.as_str()),
        outorgante_data.get("municipio").and_then(|v| v.as_str()),
        outorgante_data.get("estado").and_then(|v| v.as_str()),
        outorgante_data.get("cep").and_then(|v| v.as_str())
    ) {
        if !endereco.trim().is_empty() || !municipio.trim().is_empty() || !estado.trim().is_empty() {
            let mut parts = vec![];
            if !endereco.trim().is_empty() {
                parts.push(endereco.trim().to_string());
            }
            if !municipio.trim().is_empty() && !estado.trim().is_empty() {
                parts.push(format!("{} - {}", municipio.trim(), estado.trim()));
            } else if !municipio.trim().is_empty() {
                parts.push(municipio.trim().to_string());
            }
            if !cep.trim().is_empty() {
                parts.push(format!("CEP: {}", cep.trim()));
            }
            Value::String(parts.join(", "))
        } else {
            build_endereco_completo(&outorgante_erp.enderecos)
        }
    } else {
        build_endereco_completo(&outorgante_erp.enderecos)
    };

    json!({
        "outorgante": {
            "nomeCompleto": outorgante_data.get("nomeCompleto").cloned().unwrap_or_else(|| Value::String(outorgante_erp.nome.clone())),
            "cpf": outorgante_data.get("cpf").cloned().or_else(|| outorgante_erp.cpf.as_ref().map(|s| Value::String(s.clone()))).unwrap_or(Value::Null),
            "rg": outorgante_data.get("rg").cloned().or_else(|| outorgante_erp.rg.as_ref().map(|s| Value::String(s.clone()))).unwrap_or(Value::Null),
            "orgaoEmissor": outorgante_data.get("orgaoEmissor").cloned().unwrap_or(Value::Null),
            // Campos específicos para o template
            "endereco": outorgante_data.get("endereco").cloned().unwrap_or(Value::Null),
            "municipio": outorgante_data.get("municipio").cloned().unwrap_or(Value::Null),
            "estado": outorgante_data.get("estado").cloned().unwrap_or(Value::Null),
            "cep": outorgante_data.get("cep").cloned().unwrap_or(Value::Null),
            "telefone": outorgante_data.get("telefone").cloned().or_else(|| outorgante_erp.telefone.as_ref().map(|s| Value::String(s.clone()))).unwrap_or(Value::Null),
            // Campos de compatibilidade
            "enderecoCompleto": outorgante_endereco_completo,
            "telefonePrincipal": outorgante_data.get("telefone").cloned().or_else(|| outorgante_erp.telefone.as_ref().map(|s| Value::String(s.clone()))).unwrap_or(Value::Null),
            "celularPrincipal": outorgante_erp.celular.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null),
            "emailPrincipal": outorgante_erp.email.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null)
        },
        "outorgado": {
            "nomeCompleto": outorgado_data.get("nomeCompleto").cloned().unwrap_or(Value::Null),
            "cpf": outorgado_data.get("cpf").cloned().unwrap_or(Value::Null),
            "rg": outorgado_data.get("rg").cloned().unwrap_or(Value::Null),
            "orgaoEmissor": outorgado_data.get("orgaoEmissor").cloned().unwrap_or(Value::Null),
            // Campos específicos para o template
            "endereco": outorgado_data.get("enderecoCompleto").cloned().unwrap_or(Value::Null),
            "municipio": outorgado_data.get("municipio").cloned().unwrap_or(Value::Null),
            "estado": outorgado_data.get("estado").cloned().unwrap_or(Value::Null),
            "cep": outorgado_data.get("cep").cloned().unwrap_or(Value::Null),
            // Campos de compatibilidade
            "enderecoCompleto": outorgado_data.get("enderecoCompleto").cloned().unwrap_or(Value::Null),
            "telefonePrincipal": outorgado_data.get("telefonePrincipal").cloned().unwrap_or(Value::Null),
            "celularPrincipal": outorgado_data.get("celularPrincipal").cloned().unwrap_or(Value::Null),
            "emailPrincipal": outorgado_data.get("emailPrincipal").cloned().unwrap_or(Value::Null)
        },
        "procuracao": {
            "representarPerante": procuracao_data.get("representarPerante").cloned().unwrap_or(Value::Null),
            "poderes": procuracao_data.get("poderes").cloned().unwrap_or(Value::Null),
            "dataValidade": procuracao_data.get("dataValidade")
                .and_then(|v| v.as_str())
                .map(|date_str| {
                    if let Ok(parsed_date) = NaiveDate::parse_from_str(date_str, "%Y-%m-%d") {
                        Value::String(parsed_date.format("%d/%m/%Y").to_string())
                    } else {
                        Value::String(date_str.to_string())
                    }
                })
                .unwrap_or(Value::Null),
            "localEData": procuracao_data.get("localEData").cloned().unwrap_or(Value::Null),
            "observacoes": procuracao_data.get("observacoes").cloned().unwrap_or(Value::Null)
        }
    })
}

// Função para mapear dados de Contrato de Aluguel para formato Carbone
fn map_contract_data_to_carbone_format(
    locador_erp: &ErpCliente,
    locador_form_data: &Value,
    locatario_data: &Value,
    imovel_data: &Value,
    contrato_data: &Value
) -> Value {
    json!({
        "locador": {
            "nomeCompleto": locador_form_data.get("nomeCompleto").cloned().unwrap_or_else(|| Value::String(locador_erp.nome.clone())),
            "nacionalidade": locador_form_data.get("nacionalidade").cloned().unwrap_or_else(|| Value::String("brasileiro(a)".to_string())),
            "estadoCivil": locador_form_data.get("estadoCivil").cloned().unwrap_or(Value::Null),
            "cpf": locador_form_data.get("cpf").cloned().or_else(|| locador_erp.cpf.as_ref().map(|s| Value::String(s.clone()))).unwrap_or(Value::Null),
            "rg": locador_form_data.get("rg").cloned().or_else(|| locador_erp.rg.as_ref().map(|s| Value::String(s.clone()))).unwrap_or(Value::Null),
            "enderecoCompleto": build_endereco_completo(&locador_erp.enderecos),
            "telefonePrincipal": locador_erp.telefone.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null),
            "celularPrincipal": locador_erp.celular.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null),
            "emailPrincipal": locador_erp.email.as_ref().map(|s| Value::String(s.clone())).unwrap_or(Value::Null)
        },
        "locatario": {
            "nomeCompleto": locatario_data.get("nomeCompleto").cloned().unwrap_or(Value::Null),
            "nacionalidade": locatario_data.get("nacionalidade").cloned().unwrap_or(Value::Null),
            "estadoCivil": locatario_data.get("estadoCivil").cloned().unwrap_or(Value::Null),
            "cpf": locatario_data.get("cpf").cloned().unwrap_or(Value::Null),
            "rg": locatario_data.get("rg").cloned().unwrap_or(Value::Null),
            "enderecoCompleto": locatario_data.get("enderecoCompleto").cloned().unwrap_or(Value::Null),
            "telefonePrincipal": locatario_data.get("telefonePrincipal").cloned().unwrap_or(Value::Null),
            "celularPrincipal": locatario_data.get("celularPrincipal").cloned().unwrap_or(Value::Null),
            "emailPrincipal": locatario_data.get("emailPrincipal").cloned().unwrap_or(Value::Null)
        },
        "imovel": {
            "endereco": imovel_data.get("endereco").cloned().unwrap_or(Value::Null),
            "cidade": imovel_data.get("cidade").cloned().unwrap_or(Value::Null),
            "estado": imovel_data.get("estado").cloned().unwrap_or(Value::Null),
            "tipo": imovel_data.get("tipo").cloned().unwrap_or(Value::Null),
            "area": imovel_data.get("area").cloned().unwrap_or(Value::Null),
            "descricao": imovel_data.get("descricao").cloned().unwrap_or(Value::Null)
        },
        "contrato": {
            "prazoVigencia": contrato_data.get("prazoVigencia").cloned().unwrap_or(Value::Null),
            "valorMensal": contrato_data.get("valorMensal").cloned().unwrap_or(Value::Null),
            "valorMensalExtenso": contrato_data.get("valorMensalExtenso").cloned().unwrap_or(Value::Null),
            "diaVencimento": contrato_data.get("diaVencimento").cloned().unwrap_or(Value::Null),
            "dataAssinaturaExtenso": contrato_data.get("dataAssinaturaExtenso").cloned().unwrap_or(Value::Null),
            "valorAluguel": contrato_data.get("valorAluguel").cloned().or_else(|| contrato_data.get("valorMensal").cloned()).unwrap_or(Value::Null),
            "dataInicio": contrato_data.get("dataInicio")
                .and_then(|v| v.as_str())
                .map(|date_str| {
                    if let Ok(parsed_date) = NaiveDate::parse_from_str(date_str, "%Y-%m-%d") {
                        Value::String(parsed_date.format("%d/%m/%Y").to_string())
                    } else {
                        Value::String(date_str.to_string())
                    }
                })
                .unwrap_or(Value::Null),
            "dataFim": contrato_data.get("dataFim")
                .and_then(|v| v.as_str())
                .map(|date_str| {
                    if let Ok(parsed_date) = NaiveDate::parse_from_str(date_str, "%Y-%m-%d") {
                        Value::String(parsed_date.format("%d/%m/%Y").to_string())
                    } else {
                        Value::String(date_str.to_string())
                    }
                })
                .unwrap_or(Value::Null),
            "indiceReajuste": contrato_data.get("indiceReajuste").cloned().unwrap_or(Value::Null),
            "caucao": contrato_data.get("caucao").cloned().unwrap_or(Value::Null),
            "observacoes": contrato_data.get("observacoes").cloned().unwrap_or(Value::Null)
        }
    })
}

#[derive(Clone)]
pub struct DocumentGenerationService {
    erp_service: ErpService,
    template_service: TemplateService,
    storage_service: StorageService,
    carbone_service: CarboneService,
    local_client_supplement_service: LocalClientSupplementService,
    image_insertion_service: ImageInsertionService,
    generated_document_repository: GeneratedDocumentRepository,
}

impl DocumentGenerationService {
    pub fn new(
        erp_service: ErpService,
        template_service: TemplateService,
        storage_service: StorageService,
        carbone_service: CarboneService,
        local_client_supplement_service: LocalClientSupplementService,
        db_manager: &DatabaseManager, // Para instanciar o GeneratedDocumentRepository
    ) -> Self {
        Self {
            erp_service,
            template_service,
            storage_service,
            carbone_service,
            local_client_supplement_service,
            image_insertion_service: ImageInsertionService::new(),
            generated_document_repository: GeneratedDocumentRepository::new(&db_manager.database),
        }
    }

    pub async fn generate_document(
        &self,
        request: DocumentGenerationRequest,
        user_id: String, 
    ) -> Result<GeneratedDocument> {
        tracing::info!("Iniciando geração de documento para cliente ID: {:?} com template ID: {}", request.client_id, request.template_id);

        // Buscar metadados do template para validação
        let template_metadata = self.template_service.get_template(&request.template_id).await
            .map_err(|e| {
                tracing::error!("Erro ao buscar metadados do template (ID: {}): {}", request.template_id, e);
                e
            })?;

        // Validação: Currículos só podem ser DOCX
        if template_metadata.template_type == crate::models::template::TemplateType::Resume 
            && request.output_format != OutputFormat::Docx {
            tracing::warn!("Tentativa de gerar currículo em formato não-DOCX: {:?}", request.output_format);
            return Err(AppError::ValidationError(
                "Currículos só podem ser gerados em formato DOCX para garantir inserção automática da foto de perfil".to_string()
            ));
        }

        // BE_UPDATE_ERP_002: Extract ERP-updatable fields from different template types
        // This will be used for resume templates (single client flow)
        let mut update_erp_payload = UpdateErpClientPayload {
            tipo_pessoa: None,
            nome: None,
            razao_social: None,
            cnpj: None,
            inscricao_estadual: None,
            inscricao_municipal: None,
            tipo_contribuinte: None,
            responsavel: None,
            cpf: None,
            rg: None,
            data_nascimento: None,
            sexo: None,
            loja_virtual_is_ativo: None,
            email_acesso: None,
            telefone: None,
            celular: None,
            fax: None,
            email: None,
            ativo: None,
            vendedor_id: None,
            nome_vendedor: None,
            enderecos: None,
        };

        let mut has_erp_updates = false;

        // Resume template logic (backward compatibility)
        if let Some(additional_data) = &request.additional_data {
            if let Some(personal_info_from_form) = additional_data.get("personalInfo") {
                // Map frontend field names to ERP field names
                if let Some(full_name) = personal_info_from_form.get("fullName").and_then(|v| v.as_str()) {
                    if !full_name.trim().is_empty() {
                        update_erp_payload.nome = Some(full_name.to_string());
                        has_erp_updates = true;
                    }
                }

                if let Some(cpf) = personal_info_from_form.get("cpf").and_then(|v| v.as_str()) {
                    if !cpf.trim().is_empty() {
                        update_erp_payload.cpf = Some(cpf.to_string());
                        has_erp_updates = true;
                    }
                }

                if let Some(rg) = personal_info_from_form.get("rg").and_then(|v| v.as_str()) {
                    if !rg.trim().is_empty() {
                        update_erp_payload.rg = Some(rg.to_string());
                        has_erp_updates = true;
                    }
                }

                if let Some(birth_date) = personal_info_from_form.get("dateOfBirth").and_then(|v| v.as_str()) {
                    if !birth_date.trim().is_empty() {
                        update_erp_payload.data_nascimento = Some(birth_date.to_string());
                        has_erp_updates = true;
                    }
                }

                if let Some(email) = personal_info_from_form.get("email").and_then(|v| v.as_str()) {
                    if !email.trim().is_empty() {
                        update_erp_payload.email = Some(email.to_string());
                        has_erp_updates = true;
                    }
                }

                if let Some(telefone) = personal_info_from_form.get("phone1").and_then(|v| v.as_str()) {
                    if !telefone.trim().is_empty() {
                        update_erp_payload.telefone = Some(telefone.to_string());
                        has_erp_updates = true;
                    }
                }

                if let Some(celular) = personal_info_from_form.get("phone2").and_then(|v| v.as_str()) {
                    if !celular.trim().is_empty() {
                        update_erp_payload.celular = Some(celular.to_string());
                        has_erp_updates = true;
                    }
                }
            }
        }

        // NEW LOGIC: Handle multiple clients by role for new template types
        let mut erp_clients_by_role: HashMap<String, ErpCliente> = HashMap::new();
        let primary_client_id: String;

        if let Some(client_ids_by_role) = &request.client_ids_by_role {
            // Multi-client flow for new templates (Proxy, Contract)
            tracing::info!("Processando múltiplos clientes por papel: {:?}", client_ids_by_role);
            
            // Handle client creation/updates for contracts and proxies
            if template_metadata.template_type == crate::models::template::TemplateType::Contract || 
               template_metadata.template_type == crate::models::template::TemplateType::Proxy {
                
                for (role, client_id) in client_ids_by_role {
                    tracing::info!("Processando cliente para papel '{}': {}", role, client_id);
                    
                    // Try to get existing client or create new one if needed
                    let erp_client = match self.erp_service.get_client_by_id(client_id).await {
                        Ok(client) => {
                            tracing::info!("Cliente existente encontrado para papel '{}': {}", role, client_id);
                            
                            // Check if we need to update client with form data
                            if let Some(additional_data) = &request.additional_data {
                                let client_data = match template_metadata.template_type {
                                    crate::models::template::TemplateType::Contract => {
                                        if role == "locador" {
                                            additional_data.get("locador")
                                        } else if role == "locatario" {
                                            additional_data.get("locatario")
                                        } else { None }
                                    },
                                    crate::models::template::TemplateType::Proxy => {
                                        if role == "outorgante" {
                                            // For outorgante, we don't update from form as it comes from ERP
                                            None
                                        } else if role == "outorgado" {
                                            additional_data.get("outorgado")
                                        } else { None }
                                    },
                                    _ => None
                                };
                                
                                if let Some(form_data) = client_data {
                                    if let Err(e) = self.update_client_from_form_data(client_id, form_data).await {
                                        tracing::warn!("Falha ao atualizar cliente {} com dados do formulário: {}", client_id, e);
                                    }
                                }
                            }
                            
                            client
                        },
                        Err(_) => {
                            tracing::info!("Cliente não encontrado, tentando criar novo cliente para papel '{}'", role);
                            
                            // Try to create new client from form data
                            if let Some(additional_data) = &request.additional_data {
                                let client_data = match template_metadata.template_type {
                                    crate::models::template::TemplateType::Contract => {
                                        if role == "locador" {
                                            additional_data.get("locador")
                                        } else if role == "locatario" {
                                            additional_data.get("locatario")
                                        } else { None }
                                    },
                                    crate::models::template::TemplateType::Proxy => {
                                        if role == "outorgado" {
                                            additional_data.get("outorgado")
                                        } else { None }
                                    },
                                    _ => None
                                };
                                
                                if let Some(form_data) = client_data {
                                    match self.create_client_from_form_data(form_data, role).await {
                                        Ok(new_client_id) => {
                                            tracing::info!("Novo cliente criado com ID: {} para papel '{}'", new_client_id, role);
                                            self.erp_service.get_client_by_id(&new_client_id).await
                                                .map_err(|e| {
                                                    tracing::error!("Erro ao buscar cliente recém-criado (ID: {}): {}", new_client_id, e);
                                                    e
                                                })?
                                        },
                                        Err(e) => {
                                            tracing::error!("Falha ao criar novo cliente para papel '{}': {}", role, e);
                                            return Err(AppError::ValidationError(
                                                format!("Falha ao criar cliente para papel '{}': {}", role, e)
                                            ));
                                        }
                                    }
                                } else {
                                    return Err(AppError::ValidationError(
                                        format!("Dados do formulário não encontrados para papel '{}'", role)
                                    ));
                                }
                            } else {
                                return Err(AppError::ValidationError(
                                    "Dados adicionais são obrigatórios para criação de novos clientes".to_string()
                                ));
                            }
                        }
                    };
                    
                    erp_clients_by_role.insert(role.clone(), erp_client);
                }
            } else {
                // For other template types, just fetch existing clients
                for (role, client_id) in client_ids_by_role {
                    tracing::info!("Buscando cliente ERP para papel '{}': {}", role, client_id);
                    let erp_client = self.erp_service.get_client_by_id(client_id).await
                        .map_err(|e| {
                            tracing::error!("Erro ao buscar cliente ERP (ID: {}, papel: {}): {}", client_id, role, e);
                            e
                        })?;
                    erp_clients_by_role.insert(role.clone(), erp_client);
                }
            }
            
            // Set primary client ID for compatibility (use first role's client for naming)
            primary_client_id = match template_metadata.template_type {
                crate::models::template::TemplateType::Proxy => {
                    client_ids_by_role.get("outorgante").cloned()
                        .or_else(|| client_ids_by_role.values().next().cloned())
                        .unwrap_or_else(|| request.client_id.clone().unwrap_or_default())
                },
                crate::models::template::TemplateType::Contract => {
                    client_ids_by_role.get("locador").cloned()
                        .or_else(|| client_ids_by_role.values().next().cloned())
                        .unwrap_or_else(|| request.client_id.clone().unwrap_or_default())
                },
                _ => client_ids_by_role.values().next().cloned().unwrap_or_else(|| request.client_id.clone().unwrap_or_default())
            };
        } else {
            // Single client flow for resume templates (backward compatibility)
            primary_client_id = request.client_id.clone()
                .ok_or_else(|| AppError::ValidationError("client_id é obrigatório para templates de currículo".to_string()))?;
            tracing::info!("Processando cliente único: {}", primary_client_id);
            let current_erp_client = self.erp_service.get_client_by_id(&primary_client_id).await
                .map_err(|e| {
                    tracing::error!("Erro ao buscar cliente ERP atual (ID: {}): {}", primary_client_id, e);
                    e
                })?;
            
            // For resume templates, the role is always "titular"
            erp_clients_by_role.insert("titular".to_string(), current_erp_client);
        }

        // Get primary client for updates and filename generation
        let current_erp_client = erp_clients_by_role.values().next()
            .ok_or_else(|| AppError::ValidationError("Nenhum cliente ERP encontrado".to_string()))?
            .clone();

        if has_erp_updates {
            // Ensure required fields are present
            if update_erp_payload.tipo_pessoa.is_none() {
                update_erp_payload.tipo_pessoa = current_erp_client.tipo_pessoa.clone();
            }
            if update_erp_payload.nome.is_none() {
                update_erp_payload.nome = Some(current_erp_client.nome.clone());
            }

            tracing::info!("Atualizando dados ERP do cliente ID: {}", primary_client_id);
            match self.erp_service.update_erp_client(&primary_client_id, &update_erp_payload).await {
                Ok(_) => tracing::info!("Dados ERP atualizados com sucesso para cliente ID: {}", primary_client_id),
                Err(e) => {
                    tracing::error!("Falha ao atualizar dados ERP para cliente ID {}: {}. Continuando com geração do documento.", primary_client_id, e);
                    // Continue with document generation even if ERP update fails
                }
            }
        }

        // Fetch fresh ERP client data post-update for filename generation
        let erp_client_for_filename = if has_erp_updates {
            self.erp_service.get_client_by_id(&primary_client_id).await
                .map_err(|e| {
                    tracing::error!("Erro ao buscar cliente ERP atualizado (ID: {}): {}", primary_client_id, e);
                    e
                })?
        } else {
            current_erp_client
        };

        // Baixar o arquivo de template do MinIO (template_metadata já foi buscado na validação)
        tracing::debug!("Baixando arquivo de template do MinIO: {}", template_metadata.file_path);
        let template_bytes = self.storage_service.download_file(&template_metadata.file_path).await
            .map_err(|e| {
                tracing::error!("Erro ao baixar arquivo de template '{}' do MinIO: {}", template_metadata.file_path, e);
                e
            })?;

        // BE_SAVE_LOCAL_002: Save the complete form data snapshot to LocalClientSupplement (for all template types)
        if let Some(additional_data_for_save) = &request.additional_data {
            tracing::info!("Salvando dados suplementares para o cliente ERP ID: {} (template tipo: {:?})", primary_client_id, template_metadata.template_type);
            match self.local_client_supplement_service.save_supplemental_data(&primary_client_id, additional_data_for_save.clone()).await {
                Ok(supplement_id) => tracing::info!("Dados suplementares salvos com ID/ref: {}", supplement_id),
                Err(e) => tracing::error!("Falha ao salvar dados suplementares para cliente ERP ID {}: {}", primary_client_id, e),
            }
        }

        // BE_MAIN_LOGIC_002: NEW MULTI-TEMPLATE LOGIC - Prepare data based on template type
        let data_geracao = Local::now().format("%d/%m/%Y").to_string();
        let mut processed_image_data = None;
        
        let combined_data_for_carbone = match template_metadata.template_type {
            crate::models::template::TemplateType::Resume => {
                // Resume template (backward compatibility)
                tracing::info!("Preparando dados para template de Currículo");
                
                let empty_personal_info = json!({});
                let personal_info_from_form = request.additional_data
                    .as_ref()
                    .and_then(|data| data.get("personalInfo"))
                    .unwrap_or(&empty_personal_info);

                let erp_data_for_carbone = map_form_personal_info_to_carbone_erp_format(personal_info_from_form);

                let mut cv_data_for_carbone = if let Some(additional_data_value) = &request.additional_data {
                    map_additional_data_to_carbone_cv_format(additional_data_value)
                } else {
                    json!({
                        "urlFotoPerfil": Value::Null,
                        "objetivoProfissional": Value::Null,
                        "formacaoAcademica": [],
                        "experienciaProfissional": [],
                        "habilidadesCompetencias": Value::Null,
                        "informacoesComplementares": Value::Null
                    })
                };

                // Salvar dados de imagem para pós-processamento (não enviar para Carbone)
                let image_data_for_post_processing = cv_data_for_carbone.get("urlFotoPerfil").cloned();
                
                // Process profile picture from MinIO if it's a MinIO path, but only for post-processing
                processed_image_data = if let Some(url_foto_perfil) = &image_data_for_post_processing {
                    if let Some(url_str) = url_foto_perfil.as_str() {
                        // Check if it's a MinIO path (not HTTP/HTTPS URL or data: URI)
                        if !url_str.starts_with("http://") && 
                           !url_str.starts_with("https://") && 
                           !url_str.starts_with("data:") &&
                           !url_str.is_empty() {
                            
                            tracing::info!("Baixando imagem de perfil do MinIO para pós-processamento: {}", url_str);
                            
                            match self.storage_service.download_file(url_str).await {
                                Ok(image_bytes) => {
                                    // Determine image format based on file extension or content
                                    let image_format = if url_str.ends_with(".png") {
                                        "png"
                                    } else if url_str.ends_with(".gif") {
                                        "gif"
                                    } else if url_str.ends_with(".webp") {
                                        "webp"
                                    } else {
                                        "jpeg" // Default to jpeg
                                    };
                                    
                                    // Convert to base64 for post-processing
                                    let base64_image = base64::engine::general_purpose::STANDARD.encode(&image_bytes);
                                    let data_uri = format!("data:image/{};base64,{}", image_format, base64_image);
                                    
                                    tracing::info!("Imagem preparada para pós-processamento");
                                    Some(Value::String(data_uri))
                                }
                                Err(e) => {
                                    tracing::warn!("Falha ao baixar imagem de perfil do MinIO '{}': {}. Documento será gerado sem imagem.", url_str, e);
                                    None
                                }
                            }
                        } else {
                            // Imagem já é base64 ou URL externa
                            Some(url_foto_perfil.clone())
                        }
                    } else {
                        None
                    }
                } else {
                    None
                };

                // Remover imagem do JSON do Carbone (Community Edition não suporta)
                if let Some(cv_obj) = cv_data_for_carbone.as_object_mut() {
                    cv_obj.insert("urlFotoPerfil".to_string(), Value::Null);
                }

                json!({
                    "erp": erp_data_for_carbone,
                    "cv": cv_data_for_carbone,
                    "dataGeracao": data_geracao
                })
            },
            
            crate::models::template::TemplateType::Proxy => {
                // Proxy template (Procuração)
                tracing::info!("Preparando dados para template de Procuração");
                
                let outorgante_erp = erp_clients_by_role.get("outorgante")
                    .ok_or_else(|| AppError::ValidationError("Cliente outorgante não encontrado".to_string()))?;
                
                let additional_data = request.additional_data.as_ref()
                    .ok_or_else(|| AppError::ValidationError("Dados adicionais são obrigatórios para procuração".to_string()))?;
                
                let outorgado_data = additional_data.get("outorgado")
                    .ok_or_else(|| AppError::ValidationError("Dados do outorgado são obrigatórios".to_string()))?;
                
                let procuracao_data = additional_data.get("procuracao")
                    .ok_or_else(|| AppError::ValidationError("Dados da procuração são obrigatórios".to_string()))?;
                
                let empty_outorgante = json!({});
                let outorgante_data = additional_data.get("outorgante").unwrap_or(&empty_outorgante);
                
                let proxy_data = map_proxy_data_to_carbone_format(outorgante_erp, outorgante_data, outorgado_data, procuracao_data);
                
                json!({
                    "outorgante": proxy_data.get("outorgante").cloned().unwrap_or(Value::Null),
                    "outorgado": proxy_data.get("outorgado").cloned().unwrap_or(Value::Null),
                    "procuracao": proxy_data.get("procuracao").cloned().unwrap_or(Value::Null),
                    "dataGeracao": data_geracao
                })
            },
            
            crate::models::template::TemplateType::Contract => {
                // Contract template (Contrato de Aluguel)
                tracing::info!("Preparando dados para template de Contrato de Aluguel");
                
                let locador_erp = erp_clients_by_role.get("locador")
                    .ok_or_else(|| AppError::ValidationError("Cliente locador não encontrado".to_string()))?;
                
                let additional_data = request.additional_data.as_ref()
                    .ok_or_else(|| AppError::ValidationError("Dados adicionais são obrigatórios para contrato".to_string()))?;
                
                let locatario_data = additional_data.get("locatario")
                    .ok_or_else(|| AppError::ValidationError("Dados do locatário são obrigatórios".to_string()))?;
                
                let imovel_data = additional_data.get("imovel")
                    .ok_or_else(|| AppError::ValidationError("Dados do imóvel são obrigatórios".to_string()))?;
                
                let contrato_data = additional_data.get("contrato")
                    .ok_or_else(|| AppError::ValidationError("Dados do contrato são obrigatórios".to_string()))?;
                
                let empty_locador = json!({});
                let locador_form_data = additional_data.get("locador").unwrap_or(&empty_locador);
                
                let contract_data = map_contract_data_to_carbone_format(
                    locador_erp, 
                    locador_form_data, 
                    locatario_data, 
                    imovel_data, 
                    contrato_data
                );

                // BE_SAVE_CONTRACT_SUPPLEMENT: Salvar dados suplementares específicos para cada cliente do contrato
                if let Some(client_ids_by_role) = &request.client_ids_by_role {
                    // Salvar dados do locador
                    if let Some(locador_client_id) = client_ids_by_role.get("locador") {
                        let locador_personal_info = json!({
                            "personalInfo": {
                                "civilStatus": locador_form_data.get("estadoCivil").cloned().unwrap_or(Value::Null),
                                "fullName": locador_form_data.get("nomeCompleto").cloned().unwrap_or(Value::Null),
                                "cpf": locador_form_data.get("cpf").cloned().unwrap_or(Value::Null),
                                "rg": locador_form_data.get("rg").cloned().unwrap_or(Value::Null),
                                "nacionalidade": locador_form_data.get("nacionalidade").cloned().unwrap_or(Value::Null)
                            }
                        });
                        
                        tracing::info!("Salvando dados suplementares do locador para ERP ID: {}", locador_client_id);
                        match self.local_client_supplement_service.save_supplemental_data(locador_client_id, locador_personal_info).await {
                            Ok(supplement_id) => tracing::info!("Dados suplementares do locador salvos com ID/ref: {}", supplement_id),
                            Err(e) => tracing::error!("Falha ao salvar dados suplementares do locador para cliente ERP ID {}: {}", locador_client_id, e),
                        }
                    }
                    
                    // Salvar dados do locatário se disponível
                    if let Some(locatario_client_id) = client_ids_by_role.get("locatario") {
                        let locatario_personal_info = json!({
                            "personalInfo": {
                                "civilStatus": locatario_data.get("estadoCivil").cloned().unwrap_or(Value::Null),
                                "fullName": locatario_data.get("nomeCompleto").cloned().unwrap_or(Value::Null),
                                "cpf": locatario_data.get("cpf").cloned().unwrap_or(Value::Null),
                                "rg": locatario_data.get("rg").cloned().unwrap_or(Value::Null),
                                "nacionalidade": locatario_data.get("nacionalidade").cloned().unwrap_or(Value::Null)
                            }
                        });
                        
                        tracing::info!("Salvando dados suplementares do locatário para ERP ID: {}", locatario_client_id);
                        match self.local_client_supplement_service.save_supplemental_data(locatario_client_id, locatario_personal_info).await {
                            Ok(supplement_id) => tracing::info!("Dados suplementares do locatário salvos com ID/ref: {}", supplement_id),
                            Err(e) => tracing::error!("Falha ao salvar dados suplementares do locatário para cliente ERP ID {}: {}", locatario_client_id, e),
                        }
                    }
                }
                
                json!({
                    "locador": contract_data.get("locador").cloned().unwrap_or(Value::Null),
                    "locatario": contract_data.get("locatario").cloned().unwrap_or(Value::Null),
                    "imovel": contract_data.get("imovel").cloned().unwrap_or(Value::Null),
                    "contrato": contract_data.get("contrato").cloned().unwrap_or(Value::Null),
                    "dataGeracao": data_geracao
                })
            },
            
            _ => {
                return Err(AppError::ValidationError(
                    format!("Tipo de template não suportado: {:?}", template_metadata.template_type)
                ));
            }
        };

        tracing::debug!("JSON preparado para Carbone (baseado nos dados do formulário): {:?}", combined_data_for_carbone);

        // Log detalhado do JSON sendo enviado ao Carbone
        tracing::info!("🚀 [CARBONE] Enviando dados para geração de documento:");
        tracing::info!("🚀 [CARBONE] Template: {}", template_metadata.file_name);
        tracing::info!("🚀 [CARBONE] Formato de saída: {:?}", request.output_format);
        
        // Log simplificado sem incluir dados grandes como base64
        tracing::debug!("🚀 [CARBONE] Dados preparados para envio (sem imagens)");

        // GD_TASK_008: Call Carbone service
        let generated_document_bytes = self.carbone_service.generate_document_from_template_bytes(
            &template_metadata.file_name, // Usar o nome original do arquivo para Carbone
            template_bytes,
            combined_data_for_carbone,
            request.output_format.clone(), // Clonar porque request.output_format será movido abaixo
        ).await.map_err(|e| {
            tracing::error!("❌ [CARBONE] Erro ao gerar documento via CarboneService: {}", e);
            e
        })?;

        tracing::info!("✅ [CARBONE] Documento gerado com sucesso. Tamanho: {} bytes", generated_document_bytes.len());

        // 🆕 PÓS-PROCESSAMENTO: Inserir imagem se existir
        let final_document_bytes = if let Some(image_data) = processed_image_data {
            tracing::info!("🖼️ Iniciando pós-processamento para inserção de imagem");
            match self.image_insertion_service.replace_placeholder_image(
                generated_document_bytes.clone(), // Clone para evitar move
                &image_data,
                &request.output_format
            ).await {
                Ok(doc_with_image) => {
                    tracing::info!("✅ Imagem inserida com sucesso no documento");
                    doc_with_image
                }
                Err(e) => {
                    tracing::warn!("⚠️ Falha ao inserir imagem: {}. Usando documento original sem imagem.", e);
                    generated_document_bytes // Fallback seguro
                }
            }
        } else {
            tracing::info!("ℹ️ Nenhuma imagem para processar, usando documento do Carbone");
            generated_document_bytes
        };

        // BE_DG_STORE_GENERATED_DOC_MINIO: Fazer upload para o MinIO
        let file_uuid = Uuid::new_v4().to_string();
        
        // Obter o nome do cliente do ERP para incluir no nome do arquivo
        let client_name = erp_client_for_filename.nome.clone();

        // NOVA LÓGICA DE NOMENCLATURA DE ARQUIVO
        let template_type_str = match template_metadata.template_type {
            TemplateType::Resume => "Curriculo",
            TemplateType::Contract => "Contrato",
            TemplateType::Proxy => "Procuracao",
            TemplateType::Other => "Documento",
        };

        let safe_client_name = client_name.replace(" ", "_").chars().filter(|c| c.is_alphanumeric() || *c == '_').collect::<String>();
        let date_str = Utc::now().format("%Y-%m-%d").to_string();
        let output_format_str = request.output_format.to_string().to_lowercase();

        let generated_file_name = format!(
            "{}_{}_{}.{}",
            template_type_str,
            safe_client_name,
            &date_str,
            output_format_str
        );

        let minio_path = format!("generated_documents/{}/{}/{}", primary_client_id, file_uuid, generated_file_name);
        
        tracing::debug!("Fazendo upload do documento gerado para MinIO: {}", minio_path);
        // Determinar o content-type baseado no formato de saída
        let content_type = match request.output_format {
            OutputFormat::Pdf => "application/pdf",
            OutputFormat::Docx => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            OutputFormat::Odt => "application/vnd.oasis.opendocument.text",
        };
        
        self.storage_service.upload_file(&minio_path, final_document_bytes, content_type).await
            .map_err(|e| {
                tracing::error!("Erro ao fazer upload do documento gerado para '{}': {}", minio_path, e);
                e
            })?;

        // BE_DG_STORE_METADATA_MONGO: Criar e salvar GeneratedDocumentMetadata
        let doc_metadata = GeneratedDocument {
            id: file_uuid, // Usar o mesmo UUID para o ID do metadado e parte do path
            template_id: request.template_id.clone(),
            client_id: primary_client_id.clone(),
            client_name,
            file_path: minio_path.clone(),
            file_name: generated_file_name.clone(),
            output_format: request.output_format,
            generated_by: user_id,
            created_at: Utc::now(),
        };

        tracing::debug!("Salvando metadados do documento gerado: {:?}", doc_metadata);
        self.generated_document_repository.create(&doc_metadata).await
             .map_err(|e| {
                tracing::error!("Erro ao salvar metadados do documento gerado: {}", e);
                // Se o documento foi salvo no MinIO mas os metadados falharam, temos um problema.
                // Considerar lógica de compensação/limpeza se necessário.
                e
            })?;
        
        tracing::info!("Documento gerado e metadados salvos com sucesso. ID do Documento: {}", doc_metadata.id);
        // BE_DG_RETURN_RESPONSE_TO_HANDLER: Retornar informações para o handler
        Ok(doc_metadata)
    }

    /// Busca um documento gerado e seus bytes para download.
    pub async fn get_generated_document_for_download(&self, generated_doc_id: &str) -> Result<(GeneratedDocument, Vec<u8>)> {
        tracing::info!("Buscando documento gerado para download. ID: {}", generated_doc_id);
        
        let metadata = self.generated_document_repository.find_by_id_str(generated_doc_id).await?
            .ok_or_else(|| {
                tracing::warn!("Documento gerado com ID '{}' não encontrado.", generated_doc_id);
                AppError::NotFound(format!("Documento gerado com ID '{}' não encontrado.", generated_doc_id))
            })?;
        
        tracing::debug!("Metadados encontrados: {:?}. Baixando arquivo do MinIO: {}", metadata, metadata.file_path);
        let file_bytes = self.storage_service.download_file(&metadata.file_path).await?;
        
        Ok((metadata, file_bytes))
    }

    /// Lista documentos gerados com paginação e filtros opcionais.
    pub async fn list_generated_documents(
        &self,
        page: u32,
        per_page: u32,
        client_id_filter: Option<String>,
        client_name_filter: Option<String>,
        template_type_filter: Option<String>,
    ) -> Result<(Vec<GeneratedDocument>, u64)> {
        tracing::info!(
            "Listando documentos gerados - página: {}, por página: {}, cliente: {:?}, nome_cliente: {:?}, tipo: {:?}",
            page, per_page, client_id_filter, client_name_filter, template_type_filter
        );
        
        self.generated_document_repository
            .find_all(page, per_page, client_id_filter, client_name_filter, template_type_filter)
            .await
    }

    /// Exclui um documento gerado, removendo tanto o arquivo do MinIO quanto os metadados do banco.
    pub async fn delete_generated_document(&self, generated_doc_id: &str) -> Result<()> {
        tracing::info!("Iniciando exclusão do documento gerado ID: {}", generated_doc_id);
        
        // Primeiro, buscar os metadados para obter o caminho do arquivo no MinIO
        let metadata = self.generated_document_repository.find_by_id_str(generated_doc_id).await?
            .ok_or_else(|| {
                tracing::warn!("Documento gerado com ID '{}' não encontrado para exclusão.", generated_doc_id);
                AppError::NotFound(format!("Documento gerado com ID '{}' não encontrado.", generated_doc_id))
            })?;
        
        tracing::debug!("Metadados encontrados para exclusão: {:?}", metadata);
        
        // Excluir arquivo do MinIO
        tracing::info!("Excluindo arquivo do MinIO: {}", metadata.file_path);
        if let Err(e) = self.storage_service.delete_file(&metadata.file_path).await {
            tracing::error!("Erro ao excluir arquivo do MinIO '{}': {}. Continuando com exclusão dos metadados.", metadata.file_path, e);
            // Não retornamos erro aqui - continuamos para excluir os metadados mesmo se o arquivo não puder ser excluído
        } else {
            tracing::info!("Arquivo excluído do MinIO com sucesso: {}", metadata.file_path);
        }
        
        // Excluir metadados do banco de dados
        tracing::info!("Excluindo metadados do banco de dados para documento ID: {}", generated_doc_id);
        let deleted = self.generated_document_repository.delete(generated_doc_id).await?;
        
        if !deleted {
            tracing::warn!("Nenhum documento foi excluído do banco de dados (ID: {})", generated_doc_id);
            return Err(AppError::NotFound(format!("Documento com ID '{}' não encontrado no banco de dados.", generated_doc_id)));
        }
        
        tracing::info!("Documento gerado excluído com sucesso. ID: {}", generated_doc_id);
        Ok(())
    }

    /// Gera um link de compartilhamento (URL pré-assinada) para um documento.
    pub async fn get_document_share_link(&self, generated_doc_id: &str) -> Result<String> {
        tracing::info!("Gerando link de compartilhamento para o documento ID: {}", generated_doc_id);
        
        let metadata = self.generated_document_repository.find_by_id_str(generated_doc_id).await?
            .ok_or_else(|| {
                tracing::warn!("Documento gerado com ID '{}' não encontrado para gerar link.", generated_doc_id);
                AppError::NotFound(format!("Documento gerado com ID '{}' não encontrado.", generated_doc_id))
            })?;
        
        // O link expira em 1 hora (3600 segundos)
        let expires_in_secs = 3600; 
        
        let presigned_url = self.storage_service.get_presigned_download_url(&metadata.file_path, expires_in_secs).await?;
        
        tracing::info!("Link de compartilhamento gerado com sucesso para o documento ID: {}", generated_doc_id);
        Ok(presigned_url)
    }

    /// Create a new client from form data
    async fn create_client_from_form_data(&self, form_data: &Value, role: &str) -> Result<String> {
        tracing::info!("Criando novo cliente a partir dos dados do formulário para papel: {}", role);
        
        let create_payload = CreateErpClientPayload {
            tipo_pessoa: "PF".to_string(), // Default to Pessoa Física
            nome: form_data.get("nomeCompleto")
                .and_then(|v| v.as_str())
                .map(|s| s.to_string())
                .ok_or_else(|| AppError::ValidationError("Nome completo é obrigatório".to_string()))?,
            razao_social: None,
            cnpj: None,
            inscricao_estadual: None,
            inscricao_municipal: None,
            tipo_contribuinte: None,
            responsavel: None,
            cpf: form_data.get("cpf").and_then(|v| v.as_str()).map(|s| s.to_string()),
            rg: form_data.get("rg").and_then(|v| v.as_str()).map(|s| s.to_string()),
            data_nascimento: None,
            sexo: None,
            loja_virtual_is_ativo: Some(false),
            email_acesso: None,
            telefone: None,
            celular: None,
            fax: None,
            email: None,
            ativo: Some(true),
            vendedor_id: None,
            nome_vendedor: None,
            enderecos: vec![], // Inicializar com vetor vazio
        };

        let created_client = self.erp_service.create_erp_client(&create_payload).await
            .map_err(|e| {
                tracing::error!("Erro ao criar cliente no ERP: {}", e);
                AppError::ValidationError(format!("Falha ao criar cliente: {}", e))
            })?;

        tracing::info!("Cliente criado com sucesso no ERP. ID: {}", created_client.id);
        Ok(created_client.id)
    }

    /// Update an existing client with form data
    async fn update_client_from_form_data(&self, client_id: &str, form_data: &Value) -> Result<()> {
        tracing::info!("Atualizando cliente {} com dados do formulário", client_id);
        
        let mut update_payload = UpdateErpClientPayload {
            tipo_pessoa: None,
            nome: None,
            razao_social: None,
            cnpj: None,
            inscricao_estadual: None,
            inscricao_municipal: None,
            tipo_contribuinte: None,
            responsavel: None,
            cpf: None,
            rg: None,
            data_nascimento: None,
            sexo: None,
            loja_virtual_is_ativo: None,
            email_acesso: None,
            telefone: None,
            celular: None,
            fax: None,
            email: None,
            ativo: None,
            vendedor_id: None,
            nome_vendedor: None,
            enderecos: None,
        };

        let mut has_updates = false;

        // Map form fields to ERP fields
        if let Some(nome) = form_data.get("nomeCompleto").and_then(|v| v.as_str()) {
            if !nome.trim().is_empty() {
                update_payload.nome = Some(nome.to_string());
                has_updates = true;
            }
        }

        if let Some(cpf) = form_data.get("cpf").and_then(|v| v.as_str()) {
            if !cpf.trim().is_empty() {
                update_payload.cpf = Some(cpf.to_string());
                has_updates = true;
            }
        }

        if let Some(rg) = form_data.get("rg").and_then(|v| v.as_str()) {
            if !rg.trim().is_empty() {
                update_payload.rg = Some(rg.to_string());
                has_updates = true;
            }
        }

        if has_updates {
            // Get current client data to ensure required fields
            let current_client = self.erp_service.get_client_by_id(client_id).await?;
            
            if update_payload.tipo_pessoa.is_none() {
                update_payload.tipo_pessoa = current_client.tipo_pessoa.clone();
            }
            if update_payload.nome.is_none() {
                update_payload.nome = Some(current_client.nome.clone());
            }

            self.erp_service.update_erp_client(client_id, &update_payload).await
                .map_err(|e| {
                    tracing::error!("Erro ao atualizar cliente no ERP: {}", e);
                    AppError::ValidationError(format!("Falha ao atualizar cliente: {}", e))
                })?;

            tracing::info!("Cliente {} atualizado com sucesso", client_id);
        } else {
            tracing::info!("Nenhuma atualização necessária para cliente {}", client_id);
        }

        Ok(())
    }

    pub async fn convert_docx_to_pdf(&self, generated_doc_id: &str) -> Result<Vec<u8>> {
        let document = self.generated_document_repository
            .find_by_id_str(generated_doc_id)
            .await?
            .ok_or_else(|| AppError::NotFound("Documento não encontrado".to_string()))?;
        
        let file_extension = document.file_name.split('.').last().unwrap_or("");
        if file_extension.to_lowercase() != "docx" {
            return Err(AppError::BadRequest("O documento não é um arquivo DOCX".to_string()));
        }
        
        let docx_bytes = self.storage_service
            .download_file(&document.file_path)
            .await?;
        
        let pdf_bytes = self.carbone_service
            .generate_document_from_template_bytes(
                &document.file_name,
                docx_bytes,
                json!({}),
                OutputFormat::Pdf
            )
            .await?;
        
        Ok(pdf_bytes)
    }
}