use std::net::SocketAddr;
use dotenvy::dotenv;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};

use infocell_server::{
    config::AppConfig,
    repositories::DatabaseManager,
    services::{storage_service::StorageService, erp_service::ErpService},
    routes::create_routes,
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Carregar variáveis de ambiente
    dotenv().ok();

    // Configurar tracing
    tracing_subscriber::registry()
        .with(EnvFilter::try_from_default_env().unwrap_or_else(|_| "infocell_server=debug,tower_http=debug".into()))
        .with(tracing_subscriber::fmt::layer())
        .init();

    tracing::info!("Iniciando Infocell Server...");

    // Carregar configurações
    let config = AppConfig::from_env()?;
    tracing::info!("Configurações carregadas com sucesso");

    // Conectar ao MongoDB
    let database_manager = DatabaseManager::new(&config).await?;
    tracing::info!("Conectado ao MongoDB");

    // Configurar MinIO
    let storage_service = StorageService::new(&config).await?;
    tracing::info!("Conectado ao MinIO");

    // Configurar ERP Service
    let erp_service = ErpService::new(&config);
    tracing::info!("ERP Service configurado");

    // Criar rotas
    let app = create_routes(database_manager, storage_service, erp_service, config.clone()); // Adicionado config.clone()

    // Configurar servidor
    let addr = SocketAddr::from(([0, 0, 0, 0], config.server_port));
    tracing::info!("Servidor escutando em {}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}