use crate::error::{AppError, Result};
use crate::models::{DocumentTemplateMetadata, TemplateType};
use bson::{doc, oid::ObjectId};
use chrono::Utc;
use mongodb::{Collection, Database};
use serde_json::Value;

#[derive(Clone)]
pub struct TemplateRepository {
    collection: Collection<DocumentTemplateMetadata>,
}

impl TemplateRepository {
    pub fn new(database: &Database) -> Self {
        Self {
            collection: database.collection("templates"),
        }
    }

    pub async fn create(&self, mut template: DocumentTemplateMetadata) -> Result<ObjectId> {
        template.id = None; // Garantir que o MongoDB gere o ID
        template.created_at = Utc::now();
        template.updated_at = Utc::now();
        template.is_active = true;

        let result = self.collection.insert_one(&template).await?;
        
        match result.inserted_id.as_object_id() {
            Some(id) => Ok(id),
            None => Err(AppError::InternalServerError(
                "Falha ao obter ID do documento inserido".to_string(),
            )),
        }
    }

    pub async fn find_by_id(&self, id: &str) -> Result<DocumentTemplateMetadata> {
        let object_id = ObjectId::parse_str(id)?;
        
        match self
            .collection
            .find_one(doc! { "_id": object_id })
            .await?
        {
            Some(template) => Ok(template),
            None => Err(AppError::NotFound("Template não encontrado".to_string())),
        }
    }

    pub async fn find_all(
        &self,
        page: u32,
        per_page: u32,
        template_type: Option<TemplateType>,
        is_active: Option<bool>,
    ) -> Result<(Vec<DocumentTemplateMetadata>, u64)> {
        let mut filter = doc! {};
        
        if let Some(t_type) = template_type {
            filter.insert("template_type", bson::to_bson(&t_type).unwrap());
        }
        
        if let Some(active) = is_active {
            filter.insert("is_active", active);
        }

        let skip = ((page - 1) * per_page) as usize;
        let limit = per_page as usize;

        let total = self.collection.count_documents(filter.clone()).await?;

        let find_options = mongodb::options::FindOptions::builder()
            .skip(Some(skip as u64))
            .limit(Some(limit as i64))
            .build();

        let mut cursor = self
            .collection
            .find(filter)
            .with_options(find_options)
            .await?;

        let mut templates = Vec::new();
        while cursor.advance().await? {
            templates.push(cursor.deserialize_current()?);
        }

        Ok((templates, total))
    }

    pub async fn update(&self, id: &str, updates: Value) -> Result<bool> {
        let object_id = ObjectId::parse_str(id)?;
        
        let mut update_doc = doc! {
            "$set": {
                "updated_at": Utc::now().to_rfc3339()
            }
        };

        // Processar campos específicos de forma segura
        let set_fields = update_doc.get_document_mut("$set").unwrap();
        
        if let Some(name) = updates.get("name").and_then(|v| v.as_str()) {
            set_fields.insert("name", name);
        }
        
        if let Some(description) = updates.get("description") {
            if description.is_null() {
                set_fields.insert("description", bson::Bson::Null);
            } else if let Some(desc_str) = description.as_str() {
                set_fields.insert("description", desc_str);
            }
        }
        
        if let Some(is_active) = updates.get("is_active").and_then(|v| v.as_bool()) {
            set_fields.insert("is_active", is_active);
        }

        let result = self
            .collection
            .update_one(doc! { "_id": object_id }, update_doc)
            .await?;

        Ok(result.matched_count > 0)
    }

    pub async fn delete(&self, id: &str) -> Result<bool> {
        let object_id = ObjectId::parse_str(id)?;
        
        let result = self
            .collection
            .delete_one(doc! { "_id": object_id })
            .await?;

        Ok(result.deleted_count > 0)
    }

    pub async fn find_by_uploader(&self, uploader_id: &str) -> Result<Vec<DocumentTemplateMetadata>> {
        let mut cursor = self
            .collection
            .find(doc! { "uploader_id": uploader_id })
            .await?;

        let mut templates = Vec::new();
        while cursor.advance().await? {
            templates.push(cursor.deserialize_current()?);
        }

        Ok(templates)
    }

    pub async fn search_by_name(&self, name: &str) -> Result<Vec<DocumentTemplateMetadata>> {
        let regex_pattern = format!(".*{}.*", regex::escape(name));
        let filter = doc! {
            "name": {
                "$regex": regex_pattern,
                "$options": "i"
            },
            "is_active": true
        };

        let mut cursor = self.collection.find(filter).await?;

        let mut templates = Vec::new();
        while cursor.advance().await? {
            templates.push(cursor.deserialize_current()?);
        }

        Ok(templates)
    }
}
