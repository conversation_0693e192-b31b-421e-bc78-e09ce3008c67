use mongodb::{
    bson::{doc, oid::ObjectId, Document},
    options::{FindOneOptions, InsertOneOptions},
    Collection, Database,
};
// use chrono::Utc; // Não é usado diretamente aqui, mas pode ser em GeneratedDocument

use crate::error::{AppError, Result};
use crate::models::document::GeneratedDocument;

const COLLECTION_NAME: &str = "generated_documents";

#[derive(Clone)]
pub struct GeneratedDocumentRepository {
    collection: Collection<GeneratedDocument>,
}

impl GeneratedDocumentRepository {
    pub fn new(db: &Database) -> Self {
        Self {
            collection: db.collection(COLLECTION_NAME),
        }
    }

    /// Cria um novo registro de metadados de documento gerado.
    /// A struct `GeneratedDocument` deve ter seu campo `id` (String, representando um UUID)
    /// e `created_at` preenchidos pelo serviço antes de chamar este método.
    /// O MongoDB irá gerar seu próprio `_id` (ObjectId).
    pub async fn create(&self, document_metadata: &GeneratedDocument) -> Result<String> {
        // O `id` na struct `GeneratedDocument` é um UUID string.
        // O MongoDB irá gerar seu próprio `_id` do tipo ObjectId.
        // Não precisamos converter o `id` da struct para ObjectId aqui.
        
        // Validar se o ID já existe pode ser uma boa prática, mas depende dos requisitos.
        // Por agora, vamos assumir que o serviço garante IDs únicos.

        let insert_result = self
            .collection
            .insert_one(document_metadata) // API de 1 argumento
            .await
            .map_err(|e| {
                tracing::error!("Erro ao inserir metadados do documento no MongoDB: {}", e);
                if let mongodb::error::ErrorKind::Write(mongodb::error::WriteFailure::WriteError(ref write_error_detail)) = *e.kind {
                    if write_error_detail.code == 11000 {
                        return AppError::BadRequest(format!(
                            "Documento com ID {} já existe (chave duplicada).",
                            document_metadata.id
                        ));
                    }
                }
                AppError::DatabaseError(e.into())
            })?;

        // Retorna o ID (String UUID) que foi passado na struct, não o _id do MongoDB.
        // O `insert_result.inserted_id` seria o `_id` do MongoDB (Bson::ObjectId).
        Ok(document_metadata.id.clone())
    }

    /// Busca metadados de um documento gerado pelo seu ID (String UUID).
    pub async fn find_by_id_str(&self, doc_id_str: &str) -> Result<Option<GeneratedDocument>> {
        // Busca pelo campo `id` (String) que definimos na struct, não pelo `_id` (ObjectId) do MongoDB.
        let filter = doc! { "id": doc_id_str };
        
        let result = self
            .collection
            .find_one(filter) // API de 1 argumento
            .await
            .map_err(|e| {
                tracing::error!("Erro ao buscar metadados do documento por ID '{}': {}", doc_id_str, e);
                AppError::DatabaseError(e.into())
            })?;

        Ok(result)
    }

    pub async fn delete(&self, doc_id_str: &str) -> Result<bool> {
        let filter = doc! { "id": doc_id_str };
        
        let delete_result = self
            .collection
            .delete_one(filter)
            .await
            .map_err(|e| {
                tracing::error!("Erro ao excluir metadados do documento por ID '{}': {}", doc_id_str, e);
                AppError::DatabaseError(e.into())
            })?;

        Ok(delete_result.deleted_count > 0)
    }

    /// Lista documentos gerados com paginação e filtros opcionais.
    pub async fn find_all(
        &self,
        page: u32,
        per_page: u32,
        client_id_filter: Option<String>,
        client_name_filter: Option<String>,
        template_type_filter: Option<String>,
    ) -> Result<(Vec<GeneratedDocument>, u64)> {
        let mut filter = doc! {};
        
        if let Some(client_id) = client_id_filter {
            filter.insert("client_id", client_id);
        }

        if let Some(client_name) = client_name_filter {
            if !client_name.is_empty() {
                filter.insert("client_name", doc! {
                    "$regex": client_name,
                    "$options": "i"
                });
            }
        }
        
        if let Some(template_type) = template_type_filter {
            filter.insert("template_type", template_type);
        }

        let skip = ((page - 1) * per_page) as u64;
        let limit = per_page as i64;

        // Contar total de documentos que correspondem ao filtro
        let total = self.collection.count_documents(filter.clone()).await
            .map_err(|e| {
                tracing::error!("Erro ao contar documentos gerados: {}", e);
                AppError::DatabaseError(e.into())
            })?;

        // Configurar opções de busca com paginação e ordenação
        let find_options = mongodb::options::FindOptions::builder()
            .skip(Some(skip))
            .limit(Some(limit))
            .sort(doc! { "created_at": -1 }) // Mais recentes primeiro
            .build();

        // Executar a busca
        let mut cursor = self
            .collection
            .find(filter)
            .with_options(find_options)
            .await
            .map_err(|e| {
                tracing::error!("Erro ao buscar documentos gerados: {}", e);
                AppError::DatabaseError(e.into())
            })?;

        let mut documents = Vec::new();
        while cursor.advance().await.map_err(|e| {
            tracing::error!("Erro ao iterar sobre documentos gerados: {}", e);
            AppError::DatabaseError(e.into())
        })? {
            let document = cursor.deserialize_current().map_err(|e| {
                tracing::error!("Erro ao deserializar documento gerado: {}", e);
                AppError::DatabaseError(e.into())
            })?;
            documents.push(document);
        }

        Ok((documents, total))
    }
}