use crate::error::{AppError, Result};
use crate::models::RechargePackage;
use bson::{doc, oid::ObjectId, Bson};
use mongodb::{Collection, Database};
use futures_util::stream::TryStreamExt;

#[derive(Clone)]
pub struct RechargePackageRepository {
    collection: Collection<RechargePackage>,
}

impl RechargePackageRepository {
    pub fn new(database: &Database) -> Self {
        Self {
            collection: database.collection("recharge_packages"),
        }
    }

    pub async fn create(&self, package: &RechargePackage) -> Result<ObjectId> {
        let result = self.collection.insert_one(package).await?;
        result
            .inserted_id
            .as_object_id()
            .ok_or_else(|| AppError::InternalServerError("Failed to get inserted ID".to_string()))
    }

    pub async fn find_by_id(&self, id: &str) -> Result<Option<RechargePackage>> {
        let object_id = ObjectId::parse_str(id)?;
        self.collection.find_one(doc! { "_id": object_id }).await.map_err(Into::into)
    }

    pub async fn find_all(&self) -> Result<Vec<RechargePackage>> {
        let mut cursor = self.collection.find(doc! {}).await?;
        let mut packages = Vec::new();
        while let Some(package) = cursor.try_next().await? {
            packages.push(package);
        }
        Ok(packages)
    }

    pub async fn find_by_package_type(&self, package_type: &str) -> Result<Option<RechargePackage>> {
        self.collection
            .find_one(doc! { "package_type": package_type, "is_active": true })
            .await
            .map_err(Into::into)
    }
    
    pub async fn update(&self, id: &str, updates: &RechargePackage) -> Result<bool> {
        let object_id = ObjectId::parse_str(id)?;
        let filter = doc! { "_id": object_id };
        
        let update_doc = doc! {
            "$set": {
                "package_name": &updates.package_name,
                "package_type": &updates.package_type,
                "sale_price": updates.sale_price,
                "is_active": updates.is_active,
            }
        };

        let result = self.collection.update_one(filter, update_doc).await?;
        Ok(result.matched_count > 0)
    }

    pub async fn delete(&self, id: &str) -> Result<bool> {
        let object_id = ObjectId::parse_str(id)?;
        let result = self.collection.delete_one(doc! { "_id": object_id }).await?;
        Ok(result.deleted_count > 0)
    }
} 