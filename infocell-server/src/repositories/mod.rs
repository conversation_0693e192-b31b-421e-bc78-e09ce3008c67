pub mod template_repository;
pub mod generated_document_repository; // Adicionado novo repositório
pub mod local_client_supplement_repository; // Adicionado novo repositório
pub mod recharge_package_repository;
pub mod recharge_code_repository;
pub mod recharge_sale_repository;

use mongodb::{Client, Database, options::ClientOptions};
use crate::config::AppConfig;
use crate::error::Result;

#[derive(Clone)]
pub struct DatabaseManager {
    pub client: Client,
    pub database: Database,
}

impl DatabaseManager {
    pub async fn new(config: &AppConfig) -> Result<Self> {
        let mut client_options = ClientOptions::parse(&config.mongodb_uri).await?;
        client_options.retry_writes = Some(false);
        client_options.direct_connection = Some(true);

        let client = Client::with_options(client_options)?;
        let database = client.database(&config.mongodb_database);

        client
            .database("admin")
            .run_command(bson::doc! {"ismaster": 1})
            .await?;

        tracing::info!("Conectado ao MongoDB com sucesso (com opções explícitas)");

        Ok(Self { client, database })
    }
}
