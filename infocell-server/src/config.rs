use anyhow::Result;

#[derive(Debug, <PERSON>lone)]
pub struct AppConfig {
    pub jwt_secret: String,
    pub server_port: u16,
    pub mongodb_uri: String,
    pub mongodb_database: String,
    pub minio_endpoint: String,
    pub minio_access_key: String,
    pub minio_secret_key: String,
    pub minio_bucket: String,
    pub minio_region: String,
    pub carbone_api_url: String,
    pub carbone_api_key: String,
    pub erp_api_url: String,
    pub erp_api_key: String,
    pub erp_secret_key: String,
}

impl AppConfig {
    pub fn from_env() -> Result<Self> {
        Ok(Self {
            jwt_secret: std::env::var("JWT_SECRET")
                .expect("JWT_SECRET deve estar definido no .env"),
            server_port: std::env::var("SERVER_PORT")
                .unwrap_or_else(|_| "3001".to_string())
                .parse()
                .expect("SERVER_PORT deve ser um número válido"),
            mongodb_uri: std::env::var("MONGODB_URI")
                .expect("MONGODB_URI deve estar definido no .env"),
            mongodb_database: std::env::var("MONGODB_DATABASE")
                .unwrap_or_else(|_| "infocell_dashboard".to_string()),
            minio_endpoint: std::env::var("MINIO_ENDPOINT")
                .expect("MINIO_ENDPOINT deve estar definido no .env"),
            minio_access_key: std::env::var("MINIO_ACCESS_KEY")
                .expect("MINIO_ACCESS_KEY deve estar definido no .env"),
            minio_secret_key: std::env::var("MINIO_SECRET_KEY")
                .expect("MINIO_SECRET_KEY deve estar definido no .env"),
            minio_bucket: std::env::var("MINIO_BUCKET")
                .unwrap_or_else(|_| "infocell-templates".to_string()),
            minio_region: std::env::var("MINIO_REGION")
                .unwrap_or_else(|_| "us-east-1".to_string()),
            carbone_api_url: std::env::var("CARBONE_API_URL")
                .expect("CARBONE_API_URL deve estar definido no .env"),
            carbone_api_key: std::env::var("CARBONE_API_KEY")
                .expect("CARBONE_API_KEY deve estar definido no .env"),
            erp_api_url: std::env::var("ERP_API_URL")
                .expect("ERP_API_URL deve estar definido no .env"),
            erp_api_key: std::env::var("ERP_API_KEY")
                .expect("ERP_API_KEY deve estar definido no .env"),
            erp_secret_key: std::env::var("ERP_SECRET_KEY")
                .expect("ERP_SECRET_KEY deve estar definido no .env"),
        })
    }
}
