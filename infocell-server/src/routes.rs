use axum::{
    middleware,
    routing::{delete, get, post, put},
    Router, Extension,
};
use tower_http::cors::Cors<PERSON>ayer;
use reqwest::header::{AUTHORIZATION, CONTENT_TYPE};

use crate::handlers::{template_handlers, client_handlers, document_handlers, erp_handlers, recharge_package_handlers, recharge_handlers};
use crate::middleware::jwt_auth_middleware;
use crate::repositories::DatabaseManager;
use crate::repositories::recharge_package_repository::RechargePackageRepository;
use crate::repositories::recharge_code_repository::RechargeCodeRepository;
use crate::repositories::recharge_sale_repository::RechargeSaleRepository;
use crate::services::{
    template_service::TemplateService,
    storage_service::StorageService,
    erp_service::ErpService,
    carbone_service::CarboneService,
    local_client_supplement_service::LocalClientSupplementService,
    document_service::DocumentGenerationService,
    recharge_package_service::RechargePackageService,
    recharge_service::RechargeService,
};
use crate::config::AppConfig;

#[derive(Clone)]
pub struct AppState {
    pub template_service: TemplateService,
    pub storage_service: StorageService,
    pub erp_service: ErpService,
    pub document_generation_service: DocumentGenerationService,
    pub local_client_supplement_service: LocalClientSupplementService,
    pub recharge_package_service: RechargePackageService,
    pub recharge_service: RechargeService,
}

pub fn create_routes(
    database_manager: DatabaseManager,
    storage_service: StorageService,
    erp_service: ErpService,
    app_config: AppConfig,
) -> Router {
    let template_repository = crate::repositories::template_repository::TemplateRepository::new(&database_manager.database);
    let template_service = TemplateService::new(template_repository, storage_service.clone());
    
    let carbone_service = CarboneService::new(&app_config);
    let local_client_supplement_service = LocalClientSupplementService::new(&database_manager);
    let document_generation_service = DocumentGenerationService::new(
        erp_service.clone(),
        template_service.clone(),
        storage_service.clone(),
        carbone_service.clone(),
        local_client_supplement_service.clone(),
        &database_manager,
    );

    let recharge_package_repository = RechargePackageRepository::new(&database_manager.database);
    let recharge_code_repository = RechargeCodeRepository::new(&database_manager.database);
    let recharge_sale_repository = RechargeSaleRepository::new(&database_manager.database);

    let recharge_package_service = RechargePackageService::new(
        recharge_package_repository.clone(),
        recharge_code_repository.clone()
    );

    let recharge_service = RechargeService::new(
        database_manager.clone(),
        recharge_code_repository,
        recharge_sale_repository,
        recharge_package_repository,
    );

    let app_state = AppState {
        template_service,
        storage_service,
        erp_service,
        document_generation_service,
        local_client_supplement_service,
        recharge_package_service,
        recharge_service,
    };

    let public_routes = Router::new()
        .route("/health", get(health_check))
        .route("/api/health", get(health_check))
        .route("/api/v1/clients/{client_id}/profile-picture/{file_name}", get(client_handlers::get_profile_picture_handler));

    let protected_routes = Router::new()
        .route("/api/v1/templates", post(template_handlers::create_template))
        .route("/api/v1/templates", get(template_handlers::list_templates))
        .route("/api/v1/templates/{id}", get(template_handlers::get_template))
        .route("/api/v1/templates/{id}", put(template_handlers::update_template))
        .route("/api/v1/templates/{id}", delete(template_handlers::delete_template))
        .route("/api/v1/templates/{id}/download", get(template_handlers::download_template))
        .route("/api/v1/erp-proxy/suppliers", get(erp_handlers::get_suppliers_handler))
        .route("/api/v1/erp-proxy/employees", get(erp_handlers::get_employees_handler))
        .route("/api/v1/clients", get(client_handlers::list_clients))
        .route("/api/v1/clients", post(client_handlers::create_client_handler))
        .route("/api/v1/clients/{id}", get(client_handlers::get_client))
        .route("/api/v1/clients/{id}", put(client_handlers::update_client_handler))
        .route("/api/v1/clients/{client_id}/profile-picture", post(client_handlers::upload_profile_picture_handler))
        .route("/api/v1/erp/health", get(client_handlers::erp_health_check))
        .route("/api/v1/documents/generate", post(document_handlers::generate_document_handler))
        .route("/api/v1/documents/history", get(document_handlers::list_document_history_handler))
        .route("/api/v1/documents/{generated_doc_id}/download", get(document_handlers::download_document_handler))
        .route("/api/v1/documents/{generated_doc_id}/convert-to-pdf", post(document_handlers::convert_document_to_pdf_handler))
        .route("/api/v1/documents/{generated_doc_id}/share-link", get(document_handlers::get_document_share_link_handler))
        .route("/api/v1/documents/{generated_doc_id}", delete(document_handlers::delete_document_handler))
        .route("/api/v1/local-client-supplement/{erp_client_id}",get(document_handlers::get_local_client_supplement_handler)
                .post(document_handlers::save_local_client_supplement_handler))
        .route("/api/v1/recharge-packages", post(recharge_package_handlers::create_package_handler).get(recharge_package_handlers::list_packages_handler))
        .route("/api/v1/recharge-packages/{id}", get(recharge_package_handlers::get_package_handler).put(recharge_package_handlers::update_package_handler).delete(recharge_package_handlers::delete_package_handler))
        .route("/api/v1/recharge-codes/import", post(recharge_handlers::import_recharge_codes_handler))
        .route("/api/v1/recharge-codes/stock", get(recharge_handlers::get_stock_handler))
        .route("/api/v1/recharge-sales", post(recharge_handlers::perform_sale_handler))
        .route("/api/v1/recharge-sales/report", get(recharge_handlers::get_sales_report_handler))
        .route_layer(middleware::from_fn(jwt_auth_middleware));

    Router::new()
        .merge(public_routes)
        .merge(protected_routes)
        .with_state(app_state)
        .layer(
            CorsLayer::new()
                .allow_origin(tower_http::cors::Any)
                .allow_methods(tower_http::cors::Any)
                .allow_headers([AUTHORIZATION, CONTENT_TYPE])
        )
}

async fn health_check() -> axum::response::Json<serde_json::Value> {
    axum::response::Json(serde_json::json!({
        "status": "ok",
        "service": "infocell-server",
        "timestamp": chrono::Utc::now().to_rfc3339()
    }))
}
