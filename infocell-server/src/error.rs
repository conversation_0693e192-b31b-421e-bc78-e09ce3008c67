use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use std::fmt;

#[derive(Debug)]
pub enum AppError {
    DatabaseError(mongodb::error::Error),
    ValidationError(String),
    NotFound(String),
    Unauthorized,
    InternalServerError(String),
    BadRequest(String),
    ExternalServiceError(String),
    CarboneSdkError(String), // Novo erro para o SDK do Carbone
    DocumentProcessingError(String), // Para erros de processamento de documentos e imagens
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::DatabaseError(err) => write!(f, "Database error: {}", err),
            AppError::ValidationError(msg) => write!(f, "Validation error: {}", msg),
            AppError::NotFound(msg) => write!(f, "Not found: {}", msg),
            AppError::Unauthorized => write!(f, "Unauthorized"),
            AppError::InternalServerError(msg) => write!(f, "Internal server error: {}", msg),
            AppError::BadRequest(msg) => write!(f, "Bad request: {}", msg),
            AppError::ExternalServiceError(msg) => write!(f, "External service error: {}", msg),
            AppError::CarboneSdkError(msg) => write!(f, "Carbone SDK error: {}", msg),
            AppError::DocumentProcessingError(msg) => write!(f, "Document processing error: {}", msg),
        }
    }
}

impl std::error::Error for AppError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            AppError::DatabaseError(err) => Some(err),
            _ => None,
        }
    }
}

impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        let (status, error_message) = match self {
            AppError::DatabaseError(err) => {
                tracing::error!("Database error: {}", err);
                (StatusCode::INTERNAL_SERVER_ERROR, "Erro interno do servidor".to_string())
            }
            AppError::ValidationError(msg) => (StatusCode::BAD_REQUEST, msg),
            AppError::NotFound(msg) => (StatusCode::NOT_FOUND, msg),
            AppError::Unauthorized => (StatusCode::UNAUTHORIZED, "Não autorizado".to_string()),
            AppError::InternalServerError(msg) => {
                tracing::error!("Internal server error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "Erro interno do servidor".to_string())
            }
            AppError::BadRequest(msg) => (StatusCode::BAD_REQUEST, msg),
            AppError::ExternalServiceError(msg) => {
                tracing::error!("External service error: {}", msg);
                (StatusCode::BAD_GATEWAY, "Erro no serviço externo".to_string())
            }
            AppError::CarboneSdkError(msg) => {
                tracing::error!("Carbone SDK error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "Erro ao gerar documento (Carbone SDK)".to_string())
            }
            AppError::DocumentProcessingError(msg) => {
                tracing::error!("Document processing error: {}", msg);
                (StatusCode::INTERNAL_SERVER_ERROR, "Erro ao processar documento".to_string())
            }
        };

        let body = Json(json!({
            "success": false,
            "error": error_message
        }));

        (status, body).into_response()
    }
}

impl From<mongodb::error::Error> for AppError {
    fn from(err: mongodb::error::Error) -> Self {
        AppError::DatabaseError(err)
    }
}

impl From<bson::oid::Error> for AppError {
    fn from(_: bson::oid::Error) -> Self {
        AppError::ValidationError("ID inválido".to_string())
    }
}

impl From<reqwest::Error> for AppError {
    fn from(err: reqwest::Error) -> Self {
        AppError::ExternalServiceError(format!("Erro na requisição HTTP: {}", err))
    }
}

pub type Result<T> = std::result::Result<T, AppError>;
