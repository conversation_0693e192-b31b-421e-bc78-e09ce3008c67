use crate::{
    error::Result,
    models::{ErpEmployeeListResponse, ErpSupplierListResponse},
    routes::AppState,
};
use axum::{
    extract::{Query, State},
    Json,
};
use serde::Deserialize;

#[derive(Debug, Deserialize)]
pub struct ErpProxyQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub nome: Option<String>,
}

pub async fn get_suppliers_handler(
    State(state): State<AppState>,
    Query(query): Query<ErpProxyQuery>,
) -> Result<Json<ErpSupplierListResponse>> {
    let page = query.page.unwrap_or(1);
    let per_page = query.per_page.unwrap_or(30);
    let suppliers = state.erp_service.get_suppliers(page, per_page, query.nome).await?;
    Ok(Json(suppliers))
}

pub async fn get_employees_handler(
    State(state): State<AppState>,
    Query(query): Query<ErpProxyQuery>,
) -> Result<Json<ErpEmployeeListResponse>> {
    let page = query.page.unwrap_or(1);
    let per_page = query.per_page.unwrap_or(30);
    let employees = state.erp_service.get_employees(page, per_page, query.nome).await?;
    Ok(Json(employees))
} 