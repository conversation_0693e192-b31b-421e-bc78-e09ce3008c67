use axum::{
    extract::{Extension, Json, Path, Query, State},
    response::{IntoResponse, Response},
    http::{StatusCode, header},
};
use serde::Serialize;

use crate::error::{AppError, Result};
use crate::models::document::{DocumentGenerationRequest, GeneratedDocument};
use crate::models::ApiResponse; // Supondo que ApiResponse está em crate::models
use crate::middleware::Claims; // Para extrair user_id do JWT
use crate::routes::AppState;

#[derive(Serialize)]
pub struct DocumentGenerationResponse {
    generated_document_id: String,
    // Poderia incluir uma URL de download pré-assinada aqui se o MinIO fosse privado
    // ou outras informações úteis.
}

// BE_DG_ROUTE_HANDLER
pub async fn generate_document_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>, // Extrai o user_id (sub) do JWT
    Json(payload): Json<DocumentGenerationRequest>,
) -> Result<Json<ApiResponse<DocumentGenerationResponse>>> {
    let user_id = claims.sub; // ID do usuário logado
    
    tracing::info!("Handler: Recebida requisição para gerar documento pelo usuário: {}", user_id);

    let generated_metadata = state
        .document_generation_service
        .generate_document(payload, user_id)
        .await?;

    let response_data = DocumentGenerationResponse {
        generated_document_id: generated_metadata.id,
    };
    
    Ok(Json(ApiResponse::success(response_data)))
}

// BE_DG_DOWNLOAD_ENDPOINT
pub async fn download_document_handler(
    State(state): State<AppState>,
    Path(generated_doc_id): Path<String>,
) -> Result<Response> {
    tracing::info!("Handler: Recebida requisição para download do documento ID: {}", generated_doc_id);

    let (metadata, file_bytes) = state
        .document_generation_service
        .get_generated_document_for_download(&generated_doc_id)
        .await?;

    let content_type = match metadata.output_format {
        crate::models::document::OutputFormat::Pdf => "application/pdf",
        crate::models::document::OutputFormat::Docx => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        crate::models::document::OutputFormat::Odt => "application/vnd.oasis.opendocument.text",
    };

    let response = Response::builder()
        .status(StatusCode::OK)
        .header(header::CONTENT_TYPE, content_type)
        .header(
            header::CONTENT_DISPOSITION,
            format!("attachment; filename=\"{}\"", metadata.file_name),
        )
        .body(axum::body::Body::from(file_bytes))
        .map_err(|e| {
            tracing::error!("Erro ao construir resposta de download: {}", e);
            AppError::InternalServerError("Erro ao construir resposta de download".to_string())
        })?;
        
    Ok(response)
}

pub async fn convert_document_to_pdf_handler(
    State(state): State<AppState>,
    Path(generated_doc_id): Path<String>,
) -> Result<Response> {
    tracing::info!("Handler: Recebida requisição para converter documento DOCX para PDF. ID: {}", generated_doc_id);

    let pdf_bytes = state
        .document_generation_service
        .convert_docx_to_pdf(&generated_doc_id)
        .await?;

    let response = Response::builder()
        .status(StatusCode::OK)
        .header(header::CONTENT_TYPE, "application/pdf")
        .header(
            header::CONTENT_DISPOSITION,
            format!("inline; filename=\"document_{}.pdf\"", generated_doc_id),
        )
        .body(axum::body::Body::from(pdf_bytes))
        .map_err(|e| {
            tracing::error!("Erro ao construir resposta de conversão PDF: {}", e);
            AppError::InternalServerError("Erro ao construir resposta de conversão".to_string())
        })?;
        
    Ok(response)
}

#[derive(Serialize)]
pub struct ShareLinkResponse {
    share_url: String,
}

pub async fn get_document_share_link_handler(
    State(state): State<AppState>,
    Path(generated_doc_id): Path<String>,
) -> Result<Json<ApiResponse<ShareLinkResponse>>> {
    tracing::info!("Handler: Recebida requisição para gerar link de compartilhamento para o documento ID: {}", generated_doc_id);

    let share_url = state
        .document_generation_service
        .get_document_share_link(&generated_doc_id)
        .await?;

    let response = ShareLinkResponse { share_url };
    
    Ok(Json(ApiResponse::success(response)))
}

// BE_DG_HISTORY_ENDPOINT: Endpoint para listar histórico de documentos
use serde::Deserialize;

#[derive(Deserialize)]
pub struct DocumentHistoryParams {
    pub client_id: Option<String>,
    pub client_name: Option<String>,
    pub template_type: Option<String>,
    pub page: Option<u32>,
    pub per_page: Option<u32>,
}

#[derive(Serialize)]
pub struct DocumentHistoryResponse {
    pub documents: Vec<GeneratedDocument>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
    pub total_pages: u64,
}

pub async fn list_document_history_handler(
    State(state): State<AppState>,
    Query(params): Query<DocumentHistoryParams>,
) -> Result<Json<ApiResponse<DocumentHistoryResponse>>> {
    tracing::info!("Handler: Listando histórico de documentos");
    
    let page = params.page.unwrap_or(1).max(1);
    let per_page = params.per_page.unwrap_or(10).min(50).max(1);
    
    let (documents, total) = state
        .document_generation_service
        .list_generated_documents(page, per_page, params.client_id, params.client_name, params.template_type)
        .await?;
    
    let total_pages = (total as f64 / per_page as f64).ceil() as u64;
    
    let response = DocumentHistoryResponse {
        documents,
        total,
        page,
        per_page,
        total_pages,
    };
    
    Ok(Json(ApiResponse::success(response)))
}

pub async fn delete_document_handler(
    State(state): State<AppState>,
    Path(generated_doc_id): Path<String>,
) -> Result<Json<ApiResponse<String>>> {
    tracing::info!("Handler: Recebida requisição para excluir documento ID: {}", generated_doc_id);

    state
        .document_generation_service
        .delete_generated_document(&generated_doc_id)
        .await?;

    Ok(Json(ApiResponse::success("Documento excluído com sucesso".to_string())))
}

use crate::models::local_client_data::LocalClientSupplement;

pub async fn get_local_client_supplement_handler(
    State(state): State<AppState>,
    Path(erp_client_id): Path<String>,
) -> Result<Json<ApiResponse<Option<LocalClientSupplement>>>> {
    tracing::info!("Handler: Buscando dados suplementares para ERP Client ID: {}", erp_client_id);
    let supplement_data = state
        .local_client_supplement_service
        .get_by_erp_client_id(&erp_client_id)
        .await?;
    Ok(Json(ApiResponse::success(supplement_data)))
}

pub async fn save_local_client_supplement_handler(
    State(state): State<AppState>,
    Path(erp_client_id): Path<String>,
    Json(payload): Json<serde_json::Value>,
) -> Result<Json<ApiResponse<String>>> {
    tracing::info!("Handler: Recebida requisição POST para salvar dados suplementares para ERP Client ID: {}", erp_client_id);
    let result_id_or_message = state
        .local_client_supplement_service
        .save_supplemental_data(&erp_client_id, payload)
        .await?;
    Ok(Json(ApiResponse::success(result_id_or_message)))
}