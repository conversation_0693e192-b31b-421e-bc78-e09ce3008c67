use crate::{
    error::{AppError, Result},
    middleware::Claims,
    models::{ApiResponse, RechargeCode, RechargeSale},
    routes::AppState,
    services::recharge_service::SaleWithCodes,
};
use axum::{
    extract::{Extension, Query, State},
    Json,
};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, NaiveDate, TimeZone};

#[derive(Deserialize)]
pub struct ImportRechargeCodesPayload {
    pub codes: Vec<String>,
    pub package_type: String,
    pub unit_cost: f64,
    pub erp_supplier_id: String,
    pub validity_days: u32,
}

#[derive(Serialize)]
pub struct ImportRechargeCodesResponse {
    pub success: bool,
    pub imported: u32,
    pub failed: u32,
    pub errors: Vec<String>,
    pub total_processed: u32,
}

pub async fn import_recharge_codes_handler(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Json(payload): <PERSON><PERSON><ImportRechargeCodesPayload>,
) -> Result<Json<ImportRechargeCodesResponse>> {
    let result = state
        .recharge_service
        .import_recharge_codes(
            payload.codes,
            payload.package_type,
            payload.unit_cost,
            payload.erp_supplier_id,
            payload.validity_days,
            claims.sub, // The user ID from JWT
        )
        .await?;

    Ok(Json(result))
}

#[derive(Deserialize)]
pub struct GetStockQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub package_type: Option<String>,
    pub is_available: Option<bool>,
    pub search_code: Option<String>,
    pub erp_supplier_id: Option<String>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}

#[derive(Serialize)]
pub struct StockResponse {
    pub codes: Vec<RechargeCode>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
}

pub async fn get_stock_handler(
    State(state): State<AppState>,
    Query(params): Query<GetStockQuery>,
) -> Result<Json<ApiResponse<StockResponse>>> {
    let page = params.page.unwrap_or(1);
    let per_page = params.per_page.unwrap_or(20);

    let (codes, total) = state
        .recharge_service
        .get_recharge_stock(
            page,
            per_page,
            params.package_type,
            params.is_available,
            params.search_code,
            params.erp_supplier_id,
            params.sort_by,
            params.sort_order
        )
        .await?;

    let response = StockResponse {
        codes,
        total,
        page,
        per_page,
    };

    Ok(Json(ApiResponse::success(response)))
}

#[derive(Serialize)]
pub struct RechargeSaleResponse {
    pub sale: RechargeSale,
    pub codes: Vec<RechargeCode>,
}

#[derive(Deserialize)]
pub struct PerformSalePayload {
    pub erp_client_id: String,
    pub erp_employee_id: String,
    pub package_type: String,
    pub payment_method: String,
    pub quantity: u32,
}

pub async fn perform_sale_handler(
    State(state): State<AppState>,
    Json(payload): Json<PerformSalePayload>,
) -> Result<Json<ApiResponse<RechargeSaleResponse>>> {
    let (sale, codes) = state
        .recharge_service
        .perform_recharge_sale(
            payload.erp_client_id,
            payload.erp_employee_id,
            payload.package_type,
            payload.payment_method,
            payload.quantity,
        )
        .await?;
    
    let response = RechargeSaleResponse { sale, codes };
    Ok(Json(ApiResponse::success(response)))
}

#[derive(Deserialize)]
pub struct GetSalesReportQuery {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub erp_client_id: Option<String>,
    pub erp_employee_id: Option<String>,
    pub start_date: Option<String>,
    pub end_date: Option<String>,
}

#[derive(Serialize)]
pub struct SalesReportResponse {
    pub sales: Vec<SaleWithCodes>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
}

pub async fn get_sales_report_handler(
    State(state): State<AppState>,
    Query(params): Query<GetSalesReportQuery>,
) -> Result<Json<ApiResponse<SalesReportResponse>>> {
    let page = params.page.unwrap_or(1);
    let per_page = params.per_page.unwrap_or(20);

    // Converte a string 'YYYY-MM-DD' para o início do dia em UTC
    let start_date = params
        .start_date
        .and_then(|s| NaiveDate::parse_from_str(&s, "%Y-%m-%d").ok())
        .map(|date| Utc.from_utc_datetime(&date.and_hms_opt(0, 0, 0).unwrap()).into());

    // Converte a string 'YYYY-MM-DD' para o final do dia em UTC
    let end_date = params
        .end_date
        .and_then(|s| NaiveDate::parse_from_str(&s, "%Y-%m-%d").ok())
        .map(|date| Utc.from_utc_datetime(&date.and_hms_opt(23, 59, 59).unwrap()).into());

    let (sales, total) = state
        .recharge_service
        .get_sales_report(
            page,
            per_page,
            params.erp_client_id,
            params.erp_employee_id,
            start_date,
            end_date,
        )
        .await?;

    let response = SalesReportResponse {
        sales,
        total,
        page,
        per_page,
    };

    Ok(Json(ApiResponse::success(response)))
} 