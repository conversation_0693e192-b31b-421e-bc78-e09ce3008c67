use axum::{
    extract::{Path, Query, Multipart, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use serde::Deserialize;

use crate::error::Result;
use crate::middleware::Claims;
use crate::models::{
    ApiResponse, TemplateType,
    DocumentTemplateMetadata, TemplateListResponse,
};
use crate::routes::AppState;
use axum::extract::Extension;

#[derive(Deserialize)]
pub struct TemplateQueryParams {
    pub page: Option<u32>,
    pub per_page: Option<u32>,
    pub template_type: Option<TemplateType>,
    pub is_active: Option<bool>,
    pub search: Option<String>,
}

#[derive(Deserialize)]
pub struct UpdateTemplateRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub is_active: Option<bool>,
}

pub async fn create_template(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    mut multipart: Multipart,
) -> Result<Json<ApiResponse<String>>> {
    let mut name: Option<String> = None;
    let mut description: Option<String> = None;
    let mut template_type: Option<TemplateType> = None;
    let mut file_data: Option<Vec<u8>> = None;
    let mut file_name: Option<String> = None;
    let mut content_type: Option<String> = None;

    // Processar dados do multipart
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        crate::error::AppError::BadRequest(format!("Erro no multipart: {}", e))
    })? {
        let field_name = field.name().unwrap_or("").to_string();
        
        match field_name.as_str() {
            "name" => {
                let data = field.bytes().await.map_err(|e| {
                    crate::error::AppError::BadRequest(format!("Erro ao ler campo name: {}", e))
                })?;
                name = Some(String::from_utf8_lossy(&data).to_string());
            }
            "description" => {
                let data = field.bytes().await.map_err(|e| {
                    crate::error::AppError::BadRequest(format!("Erro ao ler campo description: {}", e))
                })?;
                let desc = String::from_utf8_lossy(&data).to_string();
                if !desc.trim().is_empty() {
                    description = Some(desc);
                }
            }
            "template_type" => {
                let data = field.bytes().await.map_err(|e| {
                    crate::error::AppError::BadRequest(format!("Erro ao ler campo template_type: {}", e))
                })?;
                let type_str = String::from_utf8_lossy(&data);
                template_type = Some(match type_str.as_ref() {
                    "resume" => TemplateType::Resume,
                    "contract" => TemplateType::Contract,
                    "proxy" => TemplateType::Proxy,
                    _ => TemplateType::Other,
                });
            }
            "file" => {
                file_name = field.file_name().map(|s| s.to_string());
                content_type = field.content_type().map(|s| s.to_string());
                file_data = Some(field.bytes().await.map_err(|e| {
                    crate::error::AppError::BadRequest(format!("Erro ao ler arquivo: {}", e))
                })?.to_vec());
            }
            _ => {}
        }
    }

    // Validar campos obrigatórios
    let name = name.ok_or_else(|| {
        crate::error::AppError::ValidationError("Campo 'name' é obrigatório".to_string())
    })?;
    
    let template_type = template_type.unwrap_or(TemplateType::Other);
    
    let file_data = file_data.ok_or_else(|| {
        crate::error::AppError::ValidationError("Arquivo é obrigatório".to_string())
    })?;
    
    let file_name = file_name.ok_or_else(|| {
        crate::error::AppError::ValidationError("Nome do arquivo é obrigatório".to_string())
    })?;
    
    let content_type = content_type.ok_or_else(|| {
        crate::error::AppError::ValidationError("Tipo do arquivo é obrigatório".to_string())
    })?;

    // Criar template
    let template_id = state.template_service
        .create_template(
            name,
            description,
            template_type,
            file_data,
            file_name,
            content_type,
            claims.sub.clone(), // Passar claims.sub como uploader_id
            claims.name.unwrap_or_else(|| claims.sub.clone()), // Usar sub se name for None
        )
        .await?;

    Ok(Json(ApiResponse::success(template_id)))
}

pub async fn get_template(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<DocumentTemplateMetadata>>> {
    let template = state.template_service.get_template(&id).await?;
    Ok(Json(ApiResponse::success(template)))
}

pub async fn list_templates(
    State(state): State<AppState>,
    Query(params): Query<TemplateQueryParams>,
) -> Result<Json<ApiResponse<TemplateListResponse>>> {
    let page = params.page.unwrap_or(1);
    let per_page = params.per_page.unwrap_or(10).min(100); // Limitar a 100 por página

    if let Some(search) = params.search {
        // Se há busca, usar busca por nome
        let templates = state.template_service.search_templates(&search).await?;
        let response = TemplateListResponse {
            total: templates.len() as u64,
            templates,
            page,
            per_page,
        };
        return Ok(Json(ApiResponse::success(response)));
    }

    let response = state.template_service
        .list_templates(page, per_page, params.template_type, params.is_active)
        .await?;

    Ok(Json(ApiResponse::success(response)))
}

pub async fn delete_template(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<()>>> {
    // Verificar se o usuário pode excluir o template
    let _template = state.template_service.get_template(&id).await?;
    
    // Por enquanto, qualquer usuário autenticado pode excluir
    // Futuramente pode ser implementada lógica de permissões
    tracing::info!(
        "Usuário (sub: {}, email: {:?}) excluindo template {}",
        claims.sub,
        claims.email, // Usar {:?} para Option<String> ou tratar o None
        id
    );

    state.template_service.delete_template(&id).await?;
    Ok(Json(ApiResponse::<()>::message("Template excluído com sucesso".to_string())))
}

pub async fn update_template(
    State(state): State<AppState>,
    Extension(claims): Extension<Claims>,
    Path(id): Path<String>,
    Json(update_request): Json<UpdateTemplateRequest>,
) -> Result<Json<ApiResponse<()>>> {
    // Verificar se o template existe
    let _template = state.template_service.get_template(&id).await?;
    
    tracing::info!(
        "Usuário (sub: {}, email: {:?}) atualizando template {}",
        claims.sub,
        claims.email,
        id
    );

    state.template_service.update_template(&id, update_request).await?;
    Ok(Json(ApiResponse::<()>::message("Template atualizado com sucesso".to_string())))
}

pub async fn download_template(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> Result<axum::response::Response> {
    let (file_data, file_name, content_type) = state.template_service.get_template_file(&id).await?;
    
    let response = axum::response::Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", content_type)
        .header("Content-Disposition", format!("attachment; filename=\"{}\"", file_name))
        .body(axum::body::Body::from(file_data))
        .map_err(|e| crate::error::AppError::InternalServerError(format!("Erro ao criar resposta: {}", e)))?;

    Ok(response)
}
