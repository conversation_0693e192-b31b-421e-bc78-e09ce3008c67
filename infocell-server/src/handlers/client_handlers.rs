use axum::{
    extract::{Path, Query, Multipart, State},
    response::{Json, Response},
    http::{StatusCode, header},
};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use serde_json::{json, Value};

use crate::error::{Result, AppError};
use crate::models::{ApiResponse, ErpCliente, ClientListResponse, CreateErpClientPayload, UpdateErpClientPayload, ProfilePictureUploadResponse};
use crate::routes::AppState;

#[derive(Debug, Deserialize)]
pub struct ClientsQuery {
    pub nome: Option<String>,
    pub page: Option<u32>,
    pub per_page: Option<u32>,
}

#[derive(Debug, Serialize)]
pub struct ClientResponse {
    pub success: bool,
    pub data: Option<ErpCliente>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ClientsResponse {
    pub success: bool,
    pub data: Option<ClientListResponse>,
    pub error: Option<String>,
}

pub async fn list_clients(
    Query(params): Query<ClientsQuery>,
    State(state): State<AppState>,
) -> Result<Json<ClientsResponse>> {
    let page = params.page.unwrap_or(1);
    let per_page = params.per_page.unwrap_or(10);

    match state.erp_service.get_clients(page, per_page, params.nome).await {
        Ok(clients_response) => Ok(Json(ClientsResponse {
            success: true,
            data: Some(clients_response),
            error: None,
        })),
        Err(e) => Ok(Json(ClientsResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

pub async fn get_client(
    Path(client_id): Path<String>,
    State(state): State<AppState>,
) -> Result<Json<ClientResponse>> {
    match state.erp_service.get_client_by_id(&client_id).await {
        Ok(client) => Ok(Json(ClientResponse {
            success: true,
            data: Some(client),
            error: None,
        })),
        Err(e) => Ok(Json(ClientResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

pub async fn create_client_handler(
    State(state): State<AppState>,
    Json(payload): Json<CreateErpClientPayload>,
) -> Result<Json<ClientResponse>> {
    match state.erp_service.create_erp_client(&payload).await {
        Ok(client) => Ok(Json(ClientResponse {
            success: true,
            data: Some(client),
            error: None,
        })),
        Err(e) => Ok(Json(ClientResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

pub async fn update_client_handler(
    Path(client_id): Path<String>,
    State(state): State<AppState>,
    Json(payload): Json<UpdateErpClientPayload>,
) -> Result<Json<ClientResponse>> {
    match state.erp_service.update_erp_client(&client_id, &payload).await {
        Ok(client) => Ok(Json(ClientResponse {
            success: true,
            data: Some(client),
            error: None,
        })),
        Err(e) => Ok(Json(ClientResponse {
            success: false,
            data: None,
            error: Some(e.to_string()),
        })),
    }
}

pub async fn erp_health_check(
    State(state): State<AppState>,
) -> Result<Json<Value>> {
    match state.erp_service.health_check().await {
        Ok(is_healthy) => Ok(Json(json!({
            "success": true,
            "healthy": is_healthy,
            "message": if is_healthy { "ERP está acessível" } else { "ERP não está acessível" }
        }))),
        Err(e) => Ok(Json(json!({
            "success": false,
            "healthy": false,
            "error": e.to_string()
        }))),
    }
}



pub async fn upload_profile_picture_handler(
    State(state): State<AppState>,
    Path(client_id): Path<String>,
    mut multipart: Multipart,
) -> Result<Json<ApiResponse<ProfilePictureUploadResponse>>> {
    // Validar se o client_id é válido (opcional - apenas para log)
    tracing::info!("Upload de foto de perfil para cliente: {}", client_id);

    let mut profile_picture_file: Option<(String, Vec<u8>)> = None;

    // Processar multipart data
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        AppError::ValidationError(format!("Erro ao processar multipart: {}", e))
    })? {
        let field_name = field.name().unwrap_or("").to_string();
        
        if field_name == "profilePictureFile" {
            let file_name = field.file_name().unwrap_or("image").to_string();
            let data = field.bytes().await.map_err(|e| {
                AppError::ValidationError(format!("Erro ao ler arquivo: {}", e))
            })?;
            

            
            profile_picture_file = Some((file_name, data.to_vec()));
            break;
        }
    }

    let (original_file_name, file_data) = profile_picture_file
        .ok_or_else(|| AppError::ValidationError("Campo 'profilePictureFile' não encontrado".to_string()))?;

    // Validar se é uma imagem
    let content_type = match original_file_name.split('.').last() {
        Some("jpg") | Some("jpeg") => "image/jpeg",
        Some("png") => "image/png",
        Some("gif") => "image/gif",
        Some("webp") => "image/webp",
        _ => return Err(AppError::ValidationError("Formato de arquivo não suportado. Use JPG, PNG, GIF ou WebP".to_string())),
    };

    // Validar tamanho do arquivo (5MB max)
    const MAX_FILE_SIZE: usize = 5 * 1024 * 1024; // 5MB
    if file_data.len() > MAX_FILE_SIZE {
        return Err(AppError::ValidationError("Arquivo muito grande. Tamanho máximo: 5MB".to_string()));
    }

    // Gerar nome único para o arquivo
    let file_extension = original_file_name.split('.').last().unwrap_or("jpg");
    let unique_file_name = format!("{}.{}", Uuid::new_v4(), file_extension);
    
    // Construir path no MinIO
    let minio_path = format!("profile-pictures/{}/{}", client_id, unique_file_name);

    // Upload para MinIO
    state.storage_service.upload_file(&minio_path, file_data, content_type).await?;

    let response = ProfilePictureUploadResponse {
        file_path: minio_path.clone(),
        file_name: unique_file_name,
    };

    tracing::info!("Foto de perfil enviada com sucesso: {}", minio_path);
    Ok(Json(ApiResponse::success(response)))
}

pub async fn get_profile_picture_handler(
    State(state): State<AppState>,
    Path((client_id, file_name)): Path<(String, String)>,
) -> Result<Response> {
    tracing::info!("Servindo imagem de perfil para cliente {} - arquivo: {}", client_id, file_name);

    // Construir o path no MinIO
    let minio_path = format!("profile-pictures/{}/{}", client_id, file_name);
    
    // Baixar arquivo do MinIO
    let image_data = state.storage_service.download_file(&minio_path).await
        .map_err(|_| AppError::NotFound("Imagem não encontrada".to_string()))?;

    // Determinar content-type baseado na extensão
    let content_type = match file_name.split('.').last() {
        Some("jpg") | Some("jpeg") => "image/jpeg",
        Some("png") => "image/png",
        Some("gif") => "image/gif",
        Some("webp") => "image/webp",
        _ => "image/jpeg", // fallback
    };

    // Construir resposta HTTP
    let response = Response::builder()
        .status(StatusCode::OK)
        .header(header::CONTENT_TYPE, content_type)
        .header(header::CACHE_CONTROL, "public, max-age=31536000") // Cache por 1 ano
        .body(axum::body::Body::from(image_data))
        .map_err(|e| {
            tracing::error!("Erro ao construir resposta de imagem: {}", e);
            AppError::InternalServerError("Erro ao servir imagem".to_string())
        })?;

    Ok(response)
}
