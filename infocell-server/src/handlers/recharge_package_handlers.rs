use crate::{
    error::{AppError, Result},
    models::{ApiResponse, RechargePackage},
    routes::AppState,
};
use axum::{
    extract::{Path, State},
    http::<PERSON>Code,
    J<PERSON>,
};

pub async fn create_package_handler(
    State(state): State<AppState>,
    Json(payload): Json<RechargePackage>,
) -> Result<Json<ApiResponse<String>>> {
    let new_id = state.recharge_package_service.create_package(&payload).await?;
    Ok(Json(ApiResponse::success(new_id.to_string())))
}

pub async fn get_package_handler(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> Result<Json<ApiResponse<RechargePackage>>> {
    let package = state
        .recharge_package_service
        .get_package(&id)
        .await?
        .ok_or_else(|| AppError::NotFound("Package not found".to_string()))?;
    Ok(<PERSON><PERSON>(ApiResponse::success(package)))
}

pub async fn list_packages_handler(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<RechargePackage>>>> {
    let packages = state.recharge_package_service.list_packages().await?;
    Ok(Json(ApiResponse::success(packages)))
}

pub async fn update_package_handler(
    State(state): State<AppState>,
    Path(id): Path<String>,
    Json(payload): Json<RechargePackage>,
) -> Result<Json<ApiResponse<bool>>> {
    let success = state
        .recharge_package_service
        .update_package(&id, &payload)
        .await?;
    if success {
        Ok(Json(ApiResponse::success(true)))
    } else {
        Err(AppError::NotFound("Package not found".to_string()))
    }
}

pub async fn delete_package_handler(
    State(state): State<AppState>,
    Path(id): Path<String>,
) -> Result<StatusCode> {
    state
        .recharge_package_service
        .delete_package(&id)
        .await?;
    Ok(StatusCode::NO_CONTENT)
} 