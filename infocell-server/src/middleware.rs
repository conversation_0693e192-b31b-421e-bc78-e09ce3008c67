use axum::{
    extract::Request,
    http::{header::AUTH<PERSON><PERSON>ZATION, StatusCode},
    middleware::Next,
    response::Response,
};
use axum_extra::headers::{authorization::Bearer, Authorization};
use axum_extra::TypedHeader;
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,
    pub name: Option<String>,
    pub email: Option<String>,
    pub exp: usize,
}

pub async fn jwt_auth_middleware(
    TypedHeader(authorization): TypedHeader<Authorization<Bearer>>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let jwt_secret = std::env::var("JWT_SECRET")
        .map_err(|e| {
            tracing::error!("Falha ao carregar JWT_SECRET: {:?}", e);
            StatusCode::INTERNAL_SERVER_ERROR
        })?;

    let token = authorization.token();
    
    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(jwt_secret.as_ref()),
        &Validation::new(Algorithm::HS256),
    ).map_err(|_| StatusCode::UNAUTHORIZED)?;

    // Adiciona as claims do usuário à request
    request.extensions_mut().insert(token_data.claims);
    
    Ok(next.run(request).await)
}

// Middleware alternativo que tenta extrair o token do header Authorization
pub async fn jwt_auth_middleware_optional(
    request: Request,
    next: Next,
) -> Response {
    let auth_header = request.headers().get(AUTHORIZATION);
    
    if let Some(auth_value) = auth_header {
        if let Ok(auth_str) = auth_value.to_str() {
            if auth_str.starts_with("Bearer ") {
                let token = &auth_str[7..];

                if let Ok(jwt_secret) = std::env::var("JWT_SECRET") {
                    if let Ok(token_data) = decode::<Claims>(
                        token,
                        &DecodingKey::from_secret(jwt_secret.as_ref()),
                        &Validation::new(Algorithm::HS256),
                    ) {
                        let mut request = request;
                        request.extensions_mut().insert(token_data.claims);
                        return next.run(request).await;
                    }
                }
            }
        }
    }
    
    next.run(request).await
}
