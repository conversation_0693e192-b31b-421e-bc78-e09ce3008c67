use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize};



#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RechargeSale {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub erp_client_id: String,
    pub erp_employee_id: String,
    pub package_type: String,
    pub quantity: u32,
    pub recharge_code_ids: Vec<ObjectId>,
    pub total_sale_price: f64,
    pub payment_method: String,
    #[serde(with = "bson::serde_helpers::chrono_datetime_as_bson_datetime")]
    pub sale_timestamp: DateTime<Utc>,
}