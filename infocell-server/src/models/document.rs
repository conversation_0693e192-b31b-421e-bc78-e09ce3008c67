use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;

// Modelos para geração de documentos
#[derive(Debug, Serialize, Deserialize)]
pub struct DocumentGenerationRequest {
    pub template_id: String,
    // Mantido para compatibilidade com currículos
    pub client_id: Option<String>,
    // Novo campo para múltiplos clientes por papel
    pub client_ids_by_role: Option<HashMap<String, String>>,
    pub output_format: OutputFormat,
    pub additional_data: Option<serde_json::Value>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum OutputFormat {
    Pdf,
    Docx,
    Odt,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GeneratedDocument {
    pub id: String,
    pub template_id: String,
    pub client_id: String,
    #[serde(default)]
    pub client_name: String,
    pub file_path: String, 
    pub file_name: String,
    pub output_format: OutputFormat,
    pub generated_by: String, 
    pub created_at: DateTime<Utc>,
}