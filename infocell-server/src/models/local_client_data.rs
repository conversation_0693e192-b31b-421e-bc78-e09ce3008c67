use serde::{Deserialize, Serialize, Deserializer};
use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;

// Função helper para deserializar datas de forma robusta
fn deserialize_optional_datetime<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
where
    D: Deserializer<'de>,
{
    use serde::de::Error;
    use serde_json::Value;
    
    let value: Option<Value> = Option::deserialize(deserializer)?;
    
    match value {
        None => Ok(None),
        Some(Value::String(s)) => {
            DateTime::parse_from_rfc3339(&s)
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S%.fZ"))
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S"))
                .map(|dt| Some(dt.with_timezone(&Utc)))
                .map_err(|e| Error::custom(format!("Erro ao parsear data '{}': {}", s, e)))
        }
        Some(_) => {
            Ok(None)
        }
    }
}

fn deserialize_datetime<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
where
    D: Deserializer<'de>,
{
    use serde::de::Error;
    use serde_json::Value;
    
    let value: Value = Value::deserialize(deserializer)?;
    
    match value {
        Value::String(s) => {
            DateTime::parse_from_rfc3339(&s)
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S%.fZ"))
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S"))
                .map(|dt| dt.with_timezone(&Utc))
                .map_err(|e| Error::custom(format!("Erro ao parsear data '{}': {}", s, e)))
        }
        _ => {
            Ok(Utc::now())
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalEducationEntry {
    pub institution: Option<String>,
    pub course: Option<String>, // Anteriormente degree, alinhado com ResumeForm.js
    pub level: Option<String>, // Novo campo para nível do curso (graduação, pós, etc.)
    #[serde(deserialize_with = "deserialize_optional_datetime")]
    pub start_date: Option<DateTime<Utc>>,
    #[serde(deserialize_with = "deserialize_optional_datetime")]
    pub end_date: Option<DateTime<Utc>>,
    pub additional_info: Option<String>, // Anteriormente description, alinhado com ResumeForm.js
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalExperienceEntry {
    pub company: Option<String>,
    pub role: Option<String>, // Anteriormente position, alinhado com ResumeForm.js
    pub localidade: Option<String>, // Novo campo para localização
    #[serde(deserialize_with = "deserialize_optional_datetime")]
    pub start_date: Option<DateTime<Utc>>,
    #[serde(deserialize_with = "deserialize_optional_datetime")]
    pub end_date: Option<DateTime<Utc>>,
    pub activities: Option<String>, // Anteriormente description, alinhado com ResumeForm.js
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalClientSupplement {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub erp_client_id: String,
    pub personalInfo: Option<serde_json::Value>, // Dados pessoais do formulário
    pub objective: Option<String>,
    pub education_history: Option<Vec<LocalEducationEntry>>,
    pub professional_experience: Option<Vec<LocalExperienceEntry>>,
    pub qualifications_summary: Option<String>, // Novo campo para alinhar com ResumeForm.js e key_data_models
    pub additional_notes: Option<String>,     // Novo campo para alinhar com ResumeForm.js e key_data_models
    #[serde(deserialize_with = "deserialize_datetime")]
    pub created_at: DateTime<Utc>,
    #[serde(deserialize_with = "deserialize_datetime")]
    pub updated_at: DateTime<Utc>,
}