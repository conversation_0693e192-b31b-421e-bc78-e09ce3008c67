use mongodb::bson::oid::ObjectId;
use serde::{Serialize, Deserialize};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RechargePackage {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub package_name: String, // Ex: "TV Express - Monthly"
    pub package_type: String, // Ex: "tv", "movies", "combo"
    pub sale_price: f64,
    #[serde(default)]
    pub is_active: bool,
} 