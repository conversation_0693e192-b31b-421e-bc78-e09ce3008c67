use serde::{Deserialize, Serialize};
use bson::oid::ObjectId;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentTemplateMetadata {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub name: String,
    pub description: Option<String>,
    pub file_path: String, // Caminho no MinIO
    pub file_name: String, // Nome original do arquivo
    pub file_size: u64,
    pub content_type: String,
    pub template_type: TemplateType,
    pub uploader_id: String, // ID do funcionário que fez upload
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "snake_case")]
pub enum TemplateType {
    Resume,
    Contract,
    Proxy,
    Other,
}

impl Default for TemplateType {
    fn default() -> Self {
        Self::Other
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateTemplateRequest {
    pub name: String,
    pub description: Option<String>,
    pub template_type: TemplateType,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateTemplateRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub template_type: Option<TemplateType>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TemplateListResponse {
    pub templates: Vec<DocumentTemplateMetadata>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
}