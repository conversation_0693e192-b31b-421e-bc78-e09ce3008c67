use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationMeta {
    pub total_registros: Option<u64>,
    pub total_paginas: Option<u32>,
    pub pagina_atual: Option<u32>,
    pub registros_por_pagina: Option<u32>,
    pub total_registros_pagina: Option<u32>,
}

// Resposta padrão da API
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<String>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
        }
    }

    pub fn error(error: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: None,
            error: Some(error),
        }
    }

    pub fn message(message: String) -> ApiResponse<()> {
        ApiResponse {
            success: true,
            data: None,
            message: Some(message),
            error: None,
        }
    }
}

// Resposta para upload de foto de perfil
#[derive(Debug, Serialize, Deserialize)]
pub struct ProfilePictureUploadResponse {
    pub file_path: String,
    pub file_name: String,
}
