use chrono::{DateTime, Utc};
use mongodb::bson::oid::ObjectId;
use serde::{Deserialize, Serialize, Deserializer};

// Função helper para deserializar datas de forma robusta
fn deserialize_optional_datetime<'de, D>(deserializer: D) -> Result<Option<DateTime<Utc>>, D::Error>
where
    D: Deserializer<'de>,
{
    use serde::de::Error;
    use serde_json::Value;

    let value: Option<Value> = Option::deserialize(deserializer)?;

    match value {
        None => Ok(None),
        Some(Value::String(s)) => {
            DateTime::parse_from_rfc3339(&s)
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S%.fZ"))
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S"))
                .map(|dt| Some(dt.with_timezone(&Utc)))
                .map_err(|e| Error::custom(format!("Erro ao parsear data '{}': {}", s, e)))
        }
        Some(_) => {
            Ok(None)
        }
    }
}

fn deserialize_datetime<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
where
    D: Deserializer<'de>,
{
    use serde::de::Error;
    use serde_json::Value;

    let value: Value = Value::deserialize(deserializer)?;

    match value {
        Value::String(s) => {
            DateTime::parse_from_rfc3339(&s)
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S%.fZ"))
                .or_else(|_| DateTime::parse_from_str(&s, "%Y-%m-%d %H:%M:%S"))
                .map(|dt| dt.with_timezone(&Utc))
                .map_err(|e| Error::custom(format!("Erro ao parsear data '{}': {}", s, e)))
        }
        _ => {
            Ok(Utc::now())
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RechargeCode {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub code: String, // O código de 16 dígitos
    pub package_type: String, // FK para RechargePackage.package_type
    pub unit_cost: f64,
    pub erp_supplier_id: String, // ID do fornecedor no ERP
    pub validity_days: u32,
    #[serde(deserialize_with = "deserialize_datetime")]
    pub imported_at: DateTime<Utc>,
    pub imported_by_user_id: String,
    #[serde(default = "default_available")]
    pub is_available: bool,
    pub sale_id: Option<ObjectId>,
    #[serde(deserialize_with = "deserialize_optional_datetime")]
    pub sold_at: Option<DateTime<Utc>>,
}
fn default_available() -> bool {
    true
} 