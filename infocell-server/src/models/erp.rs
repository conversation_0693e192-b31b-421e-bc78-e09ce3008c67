use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

// Modelos para integração com ERP
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErpCliente {
    pub id: String,
    pub tipo_pessoa: Option<String>, // "PF" para física, "PJ" para jurídica, "ES" para estrangeiro
    pub nome: String,
    pub razao_social: Option<String>,
    pub cnpj: Option<String>,
    pub inscricao_estadual: Option<String>,
    pub inscricao_municipal: Option<String>,
    pub tipo_contribuinte: Option<String>,
    pub responsavel: Option<String>,
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub data_nascimento: Option<String>,
    pub sexo: Option<String>,
    pub loja_virtual_is_ativo: Option<bool>,
    pub email_acesso: Option<String>,
    pub telefone: Option<String>,
    pub celular: Option<String>,
    pub fax: Option<String>,
    pub email: Option<String>,
    pub ativo: Option<bool>,
    pub vendedor_id: Option<String>,
    pub nome_vendedor: Option<String>,
    pub cadastrado_em: Option<DateTime<Utc>>,
    pub modificado_em: Option<DateTime<Utc>>,
    pub enderecos: Vec<ErpEndereco>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErpDetalhesEndereco {
    pub tipo_id: Option<String>,
    pub nome_tipo: Option<String>,
    pub cep: Option<String>,
    pub logradouro: Option<String>,
    pub numero: Option<String>,
    pub complemento: Option<String>,
    pub bairro: Option<String>,
    pub pais: Option<String>,
    pub cidade_id: Option<String>,
    pub nome_cidade: Option<String>,
    pub estado: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErpEndereco {
    pub endereco: ErpDetalhesEndereco,
}

// Alias para compatibilidade com código existente
pub type ErpClient = ErpCliente;
pub type ErpAddress = ErpEndereco;

#[derive(Debug, Serialize, Deserialize)]
pub struct ClientListResponse {
    pub clients: Vec<ErpCliente>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
    pub meta: Option<crate::models::common::PaginationMeta>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateErpClientPayload {
    pub tipo_pessoa: String, // "PF" para física, "PJ" para jurídica, "ES" para estrangeiro
    pub nome: String,
    pub razao_social: Option<String>,
    pub cnpj: Option<String>,
    pub inscricao_estadual: Option<String>,
    pub inscricao_municipal: Option<String>,
    pub tipo_contribuinte: Option<String>,
    pub responsavel: Option<String>,
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub data_nascimento: Option<String>,
    pub sexo: Option<String>,
    pub loja_virtual_is_ativo: Option<bool>,
    pub email_acesso: Option<String>,
    pub telefone: Option<String>,
    pub celular: Option<String>,
    pub fax: Option<String>,
    pub email: Option<String>,
    pub ativo: Option<bool>,
    pub vendedor_id: Option<String>,
    pub nome_vendedor: Option<String>,
    pub enderecos: Vec<ErpDetalhesEndereco>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateErpClientPayload {
    pub tipo_pessoa: Option<String>, // "PF" para física, "PJ" para jurídica, "ES" para estrangeiro
    pub nome: Option<String>,
    pub razao_social: Option<String>,
    pub cnpj: Option<String>,
    pub inscricao_estadual: Option<String>,
    pub inscricao_municipal: Option<String>,
    pub tipo_contribuinte: Option<String>,
    pub responsavel: Option<String>,
    pub cpf: Option<String>,
    pub rg: Option<String>,
    pub data_nascimento: Option<String>,
    pub sexo: Option<String>,
    pub loja_virtual_is_ativo: Option<bool>,
    pub email_acesso: Option<String>,
    pub telefone: Option<String>,
    pub celular: Option<String>,
    pub fax: Option<String>,
    pub email: Option<String>,
    pub ativo: Option<bool>,
    pub vendedor_id: Option<String>,
    pub nome_vendedor: Option<String>,
    pub enderecos: Option<Vec<ErpDetalhesEndereco>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErpSupplier {
    pub id: String,
    pub nome: String, // Campo em português, como na API
    pub razao_social: Option<String>,
    pub cnpj: Option<String>,
    // ... outros campos necessários
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ErpSupplierListResponse {
    pub data: Vec<ErpSupplier>,
    pub meta: super::common::PaginationMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErpEmployee {
    pub id: String,
    pub nome: String,
    #[serde(rename = "email_secundario")]
    pub email: Option<String>,
    pub ativo: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ErpEmployeeListResponse {
    pub data: Vec<ErpEmployee>,
    pub meta: super::common::PaginationMeta,
}
