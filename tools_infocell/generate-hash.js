const bcrypt = require('bcryptjs');
const readline = require('readline').createInterface({
  input: process.stdin,
  output: process.stdout
});

readline.question('Digite a senha para gerar o hash: ', (password) => {
  const saltRounds = 10; 
  bcrypt.hash(password, saltRounds, function(err, hash) {
    if (err) {
      console.error('Erro ao gerar o hash:', err);
    } else {
      console.log('Senha original:', password); 
      console.log('Hash gerado:', hash);
      console.log('\nCopie o hash gerado e adicione ao seu arquivo .env como ADMIN_PASSWORD_HASH="SEU_HASH_AQUI"');
    }
    readline.close();
  });
});