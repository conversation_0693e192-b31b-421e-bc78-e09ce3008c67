// src/modules/resume-generator/utils/file-utils.js
import fs from 'fs/promises';
import path from 'path';

// NOTA: Este arquivo está mantido apenas para referência histórica e possível compatibilidade.
// IMPORTANTE: Todos os PDFs agora são armazenados exclusivamente no Vercel Blob e os dados no Redis.
// AVISO DE REMOÇÃO: Este arquivo deve ser removido completamente após validação do deploy na Vercel.

// Estas constantes são mantidas como legado, mas não são mais usadas para novos PDFs
// que agora são armazenados no Vercel Blob
// Caminho para o diretório onde os PDFs serão salvos (dentro de public)
export const resumesDir = path.resolve(process.cwd(), 'public/resumes');

// URL base para acessar os PDFs salvos
export const publicResumesUrl = '/resumes';

/**
 * Garante que o diretório de resumes exista.
 * Cria o diretório recursivamente se não existir.
 * @throws {Error} Se não for possível criar o diretório.
 * @deprecated Não é mais necessário para o Vercel Blob, mantido para compatibilidade
 */
export async function ensureResumesDir() {
  try {
    await fs.mkdir(resumesDir, { recursive: true });
  } catch (error) {
    console.error('Erro ao criar diretório de resumes:', error);
    // Lançar o erro para ser pego pelo handler que chamar esta função
    throw new Error('Não foi possível criar o diretório para salvar os PDFs.');
  }
}