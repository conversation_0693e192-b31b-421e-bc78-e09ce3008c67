// src/modules/resume-generator/utils/html-utils.js

/**
 * Escapa caracteres HTML para prevenir XSS básico.
 * @param {*} unsafe A string ou valor a ser escapado.
 * @returns {string} A string escapada.
 */
export const escapeHtml = (unsafe) => {
  if (unsafe === null || typeof unsafe === 'undefined') {
    return '';
  }
  // Assegura que 'unsafe' seja uma string antes de chamar 'replace'
  const strUnsafe = String(unsafe);
  return strUnsafe
    .replace(/&/g, "&") // Corrigido: substituir & por &
    .replace(/</g, "<")
    .replace(/>/g, ">")
    .replace(/"/g, '"') // Corrigido: usar aspas simples para a string de substituição
    .replace(/'/g, "&#039;");
};