// src/modules/resume-generator/templates/resume-html-template.js
import { escapeHtml } from '../utils/html-utils.js';

// Função para formatar datas YYYY-MM-DD para exibição
function formatDateForDisplay(dateStringYYYYMMDD, formatType = 'DD/MM/YYYY') {
  // Verificação robusta para valores vazios ou nulos
  if (!dateStringYYYYMMDD || dateStringYYYYMMDD === 'undefined' || dateStringYYYYMMDD === 'null') {
    return ''; // Retorna string vazia se o valor for vazio ou nulo
  }
  
  // Verificar se a string está no formato esperado YYYY-MM-DD
  if (!dateStringYYYYMMDD.includes('-') || !/^\d{4}-\d{1,2}-?\d{0,2}$/.test(dateStringYYYYMMDD)) {
    // Se não estiver no formato YYYY-MM-DD, tenta retornar como está (pode ser algo como "Atual" ou "Presente")
    console.warn(`Data em formato inválido: ${dateStringYYYYMMDD}`);
    return dateStringYYYYMMDD;
  }
  
  try {
    // Parsing manual da data para evitar problemas de timezone
    const [year, month, day] = dateStringYYYYMMDD.split('-').map(part => parseInt(part, 10));
    
    // Cria a data usando componentes individuais (mês é 0-indexed no JavaScript)
    const date = new Date(year, month - 1, day || 1);
    
    // Verificação se a data é válida
    if (isNaN(date.getTime())) {
      console.warn(`Data inválida após parsing: ${dateStringYYYYMMDD}`);
      return ''; // Retorna string vazia se a data for inválida
    }
    
    const dayStr = day ? date.getDate().toString().padStart(2, '0') : '01';
    const monthStr = (date.getMonth() + 1).toString().padStart(2, '0');
    const yearStr = date.getFullYear();
    
    if (formatType === 'MM/YYYY') {
      return `${monthStr}/${yearStr}`;
    } else if (formatType === 'DD/MM/YYYY') {
      return `${dayStr}/${monthStr}/${yearStr}`;
    } else {
      return `${dayStr}/${monthStr}/${yearStr}`;
    }
  } catch (e) {
    console.error('Erro ao formatar data:', e);
    return ''; // Retorna string vazia em caso de erro
  }
}

// CSS embutido diretamente no código para evitar problemas de leitura de arquivo em produção
const styles = `@import url('https://fonts.googleapis.com/css2?family=Times+New+Roman&family=Calibri:wght@400;700&display=swap');

body {
  font-family: 'Calibri', 'Times New Roman', serif;
  line-height: 1.4;
  margin: 0;
  padding: 5px 15px; /* Reduzido ainda mais para maximizar espaço útil */
  background-color: #FFFFFF;
  color: #000000;
  font-size: 10.5pt; /* Ligeiramente menor para caber mais conteúdo */
}

.container {
  max-width: 95%; /* Quase toda a largura da página */
  margin: 0 auto;
}

/* Cabeçalho elegante ajustado */
.header {
  text-align: center;
  margin-bottom: 12px;
  border-bottom: 1px solid #000;
  padding-bottom: 8px;
}

.header-name {
  font-size: 16pt;
  font-weight: bold;
  margin-top: 0px; /* Elimina margem superior */
  margin-bottom: 6px;
  font-family: 'Times New Roman', serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #000; /* Garantir cor sólida */
}

.personal-details {
  font-size: 11pt;
  line-height: 1.4;
  text-align: left; /* Alterado de right para left */
}

.personal-details p {
  margin: 3px 0;
  color: #000; /* Garantir que não fique apagado */
}

/* Layout da página do currículo */
.section {
  margin-bottom: 10px;
  clear: both;
}

.section-title {
  font-size: 12pt;
  font-weight: bold;
  text-transform: uppercase;
  padding: 3px 0;
  margin-bottom: 6px;
  border-bottom: 1px solid #000;
  color: #000;
}

/* Estilos específicos para items de formação e experiência */
.item-header {
  font-weight: bold;
  font-size: 11pt;
  margin-bottom: 4px;
  color: #000; /* Garantir cor sólida para o título */
  padding-left: 8px; /* Pequena indentação */
}

.item-header span.date-period {
  font-weight: normal;
  font-style: italic;
  color: #333;
}

.item-detail-row {
  display: flex;
  justify-content: space-between;
  padding-left: 15px;
  margin-bottom: 3px;
  font-size: 10.5pt;
}

.item-location {
  font-style: italic;
  font-size: 10.5pt;
  color: #333;
}

/* Período colocado ao lado do título
.item-period {
  font-style: italic;
  font-size: 10.5pt;
  color: #444;
} */

.item-description {
  font-size: 10.5pt;
  padding-left: 15px;
  margin-top: 2px;
  margin-bottom: 5px;
}

/* Itens de experiência e educação com espaçamento ajustado */
.experience-item, .education-item {
  margin-bottom: 12px;
}

/* Atividades em lista com formatting */
.activities-list {
  padding-left: 30px;
  margin: 5px 0;
  font-size: 10.5pt;
}

.activities-list li {
  margin-bottom: 2px;
}

/* Layout em colunas opcional para educação e qualificações */
.multi-column-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.column {
  width: 48%;
}

/* Estilos responsivos para atividades */
.activities-columns {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.activities-column {
  margin-right: 10px;
}

.activities-column:last-child {
  margin-right: 0;
}

/* Layout específico para educação em colunas */
.education-columns {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.education-column {
  padding-right: 5px;
}

.education-item {
  margin-bottom: 15px;
}

/* Mensagem para campos não preenchidos */
.no-data {
  font-style: italic;
  color: #777;
  padding-left: 15px;
}

/* Estilos para qualificações e informações adicionais */
.qualifications-list {
  padding-left: 30px;
  margin: 5px 0;
}`;

// Função para gerar o conteúdo HTML do currículo (VERSÃO COM MENOS ESPAÇAMENTO)
// Esta função é uma adaptação da original em route.js
export function generateResumeHtml(data) {
  // Validação básica dos dados de entrada
  if (!data || typeof data !== 'object') {
    console.error("generateResumeHtml: Dados inválidos ou ausentes.");
    return '<!DOCTYPE html><html><head><title>Erro</title></head><body><h1>Erro ao gerar currículo: Dados não fornecidos ou em formato incorreto.</h1></body></html>';
  }

  const personalInfo = data.personalInfo || {};
  const educationList = Array.isArray(data.education) ? data.education : [];
  const experienceList = Array.isArray(data.experience) ? data.experience : [];
  
  // Log para depuração dos valores de datas
  console.log('=== DEPURAÇÃO DE DATAS ===');
  educationList.forEach((edu, index) => {
    console.log(`Educação #${index + 1}:`, {
      startYear: edu.startYear,
      endYearOrStatus: edu.endYearOrStatus,
      startFormatted: edu.startYear ? formatDateForDisplay(edu.startYear, 'MM/YYYY') : 'vazio',
      endFormatted: edu.endYearOrStatus ? formatDateForDisplay(edu.endYearOrStatus, 'MM/YYYY') : 'vazio'
    });
  });
  
  experienceList.forEach((exp, index) => {
    console.log(`Experiência #${index + 1}:`, {
      startYear: exp.startYear,
      endYear: exp.endYear,
      startFormatted: exp.startYear ? formatDateForDisplay(exp.startYear, 'MM/YYYY') : 'vazio',
      endFormatted: exp.endYear ? formatDateForDisplay(exp.endYear, 'MM/YYYY') : 'vazio'
    });
  });

  // A função escapeHtml é importada do módulo html-utils.js

  // --- ESTILOS SÃO EMBUTIDOS DIRETAMENTE NO CÓDIGO ---

  let html = `
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Currículo - ${escapeHtml(personalInfo.fullName)}</title>
      <style>${styles}</style>
    </head>
    <body>
      <div class="container">
  `;

  // Cabeçalho com estilo Word - Nome centralizado e informações de contato
  html += `<header class="header" style="position: relative;">`;
  
  // Nome do candidato em destaque
  if (personalInfo.fullName) {
    html += `<div class="header-name">${escapeHtml(personalInfo.fullName)}</div>`;
  }
  
  // Foto de perfil no canto superior direito
  if (personalInfo.profilePictureUrl) {
    html += `<div style="position: absolute; top: 0; right: 0;">
      <img src="${escapeHtml(personalInfo.profilePictureUrl)}" alt="Foto de Perfil" 
           style="width: 70px; height: 70px; border-radius: 50%; object-fit: cover; 
                  border: 1px solid #ccc; display: block;">
    </div>`;
  }

  // Detalhes Pessoais em formato compacto e centralizado
  html += `<div class="personal-details">`;
  
  // Linha 1: Data de Nascimento, Estado Civil, Nível de Formação
  let detailsLine1 = [];
  if (personalInfo.dateOfBirth) {
    const formattedDateOfBirth = formatDateForDisplay(personalInfo.dateOfBirth, 'DD/MM/YYYY');
    if (formattedDateOfBirth) {
      detailsLine1.push(`Data de Nasc.: ${formattedDateOfBirth}`);
    }
  }
  if (personalInfo.civilStatus) {
    detailsLine1.push(`Estado Civil: ${escapeHtml(personalInfo.civilStatus)}`);
  }
  if (personalInfo.levelOfEducation) {
    const levelOfEducationMap = {
      'fundamental_incompleto': 'Ensino Fundamental Incompleto',
      'fundamental_completo': 'Ensino Fundamental Completo',
      'medio_incompleto': 'Ensino Médio Incompleto',
      'medio_completo': 'Ensino Médio Completo',
      'superior_incompleto': 'Ensino Superior Incompleto',
      'superior_completo': 'Ensino Superior Completo',
      'pos_graduacao': 'Pós-graduação',
      'mestrado': 'Mestrado',
      'doutorado': 'Doutorado'
    };
    if (levelOfEducationMap[personalInfo.levelOfEducation]) {
      detailsLine1.push(`Nível de Formação: ${escapeHtml(levelOfEducationMap[personalInfo.levelOfEducation])}`);
    }
  }
  if (detailsLine1.length > 0) {
    html += `<p>${detailsLine1.join(' | ')}</p>`;
  }

  // Linha 2: CPF e RG
  let detailsLineDocs = [];
  if (personalInfo.cpf) {
    detailsLineDocs.push(`CPF: ${escapeHtml(personalInfo.cpf)}`);
  }
  if (personalInfo.rg) {
    detailsLineDocs.push(`RG: ${escapeHtml(personalInfo.rg)}`);
  }
  if (detailsLineDocs.length > 0) {
    html += `<p>${detailsLineDocs.join(' | ')}</p>`;
  }
  
  // Linha 3: Endereço completo (Lógica existente ajustada)
  let addressLine = [];
  if (personalInfo.address) {
    addressLine.push(`${escapeHtml(personalInfo.address)}${personalInfo.city ? `, ${escapeHtml(personalInfo.city)}` : ''}${personalInfo.state ? ` - ${escapeHtml(personalInfo.state)}` : ''}`);
  }
  // Removida a idade daqui, pois foi substituída por data de nascimento
  if (addressLine.length > 0) {
    html += `<p>${addressLine.join(' | ')}</p>`;
  }
  
  // Removidas informações de sexo e estado civil daqui (estado civil movido para detailsLine1)
  
  // Linha 4: Contatos (telefones e email) - Lógica existente mantida
  let contactLine = [];
  if (personalInfo.phone1) {
    contactLine.push(`Tel: ${escapeHtml(personalInfo.phone1)}`);
  }
  if (personalInfo.phone2) {
    contactLine.push(`Recados: ${escapeHtml(personalInfo.phone2)}`);
  }
  if (personalInfo.email) {
    contactLine.push(`E-mail: ${escapeHtml(personalInfo.email)}`);
  }
  if (contactLine.length > 0) {
    html += `<p>${contactLine.join(' | ')}</p>`;
  }

  // Linha 5: CNH e Filhos
  let detailsLineMisc = [];
  if (personalInfo.driverLicenseType && personalInfo.driverLicenseType !== 'Não possui' && personalInfo.driverLicenseType !== 'Nenhuma') {
    detailsLineMisc.push(`CNH: ${escapeHtml(personalInfo.driverLicenseType)}`);
  }
  if (personalInfo.hasChildren && personalInfo.hasChildren !== 'Selecione') {
    detailsLineMisc.push(`Filhos: ${escapeHtml(personalInfo.hasChildren)}`);
  }
  if (detailsLineMisc.length > 0) {
    html += `<p>${detailsLineMisc.join(' | ')}</p>`;
  }
  
  // Não vamos usar mais LinkedIn e GitHub
  
  html += `</div>`;
  html += `</header>`;

  // Seção de Objetivo - Estilo Word
  html += `<div class="section">
    <div class="section-title">OBJETIVO PROFISSIONAL</div>`;
  if (data.objective) {
    html += `<p style="padding-left: 15px;">${escapeHtml(data.objective)}</p>`;
  } else {
    html += `<p class="no-data" style="padding-left: 15px;">Não informado</p>`;
  }
  html += `</div>`;

  // Seção de Formação Acadêmica - Layout otimizado
  html += `<div class="section">
    <div class="section-title">FORMAÇÃO ACADÊMICA</div>`;
  
  if (educationList.length > 0 && educationList.some(edu => edu.course || edu.institution || edu.startYear || edu.endYearOrStatus)) {
    const useColumns = educationList.length > 3;
    
    if (useColumns) {
      let columnsCount = educationList.length > 6 ? 3 : 2;
      const itemsPerColumn = Math.ceil(educationList.length / columnsCount);
      
      html += `<div class="education-columns">`;
      
      for (let i = 0; i < columnsCount; i++) {
        html += `<div class="education-column" style="width: ${Math.floor(98/columnsCount)}%">`;
        
        const startIdx = i * itemsPerColumn;
        const endIdx = Math.min((i + 1) * itemsPerColumn, educationList.length);
        
        for (let j = startIdx; j < endIdx; j++) {
          const edu = educationList[j];            if (edu.course || edu.institution || edu.startYear || edu.endYearOrStatus) {
              html += `<div class="education-item">`;
              
              // Gerar o período educacional formatado
              let educationPeriod = '';
              if (edu.startYear && edu.endYearOrStatus) {
                const formattedStart = formatDateForDisplay(edu.startYear, 'MM/YYYY');
                const formattedEnd = formatDateForDisplay(edu.endYearOrStatus, 'MM/YYYY');
                
                if (formattedStart && formattedEnd) {
                  educationPeriod = ` - ${formattedStart} a ${formattedEnd}`;
                } else if (formattedStart) {
                  educationPeriod = ` - ${formattedStart}`;
                } else if (formattedEnd) {
                  educationPeriod = ` - ${formattedEnd}`;
                }
              } else if (edu.startYear) {
                const formattedStart = formatDateForDisplay(edu.startYear, 'MM/YYYY');
                if (formattedStart) {
                  educationPeriod = ` - ${formattedStart}`;
                }
              } else if (edu.endYearOrStatus) {
                const formattedEnd = formatDateForDisplay(edu.endYearOrStatus, 'MM/YYYY');
                if (formattedEnd) {
                  educationPeriod = ` - ${formattedEnd}`;
                }
              }
              
              // Adicionar o curso com o período
              if (edu.course) {
                html += `<div class="item-header">${escapeHtml(edu.course)}${educationPeriod}</div>`;
              }
              
              html += `<div class="item-detail-row">`;
              if (edu.institution) {
                html += `<span class="item-location">${escapeHtml(edu.institution)}</span>`;
              } else {
                html += `<span></span>`;
              }
              
              // Removendo a duplicação do período, pois já está incluído no cabeçalho do item
              html += `</div>`;
              
              if (edu.additionalInfo) {
                html += `<div class="item-description">${escapeHtml(edu.additionalInfo)}</div>`;
              }
              html += `</div>`;
            }
        }
        html += `</div>`;
      }
      html += `</div>`;
    } else {
      educationList.forEach(edu => {
        if (edu.course || edu.institution || edu.startYear || edu.endYearOrStatus) {
          html += `<div class="education-item">`;
          
          // Gerar o período educacional formatado
          let educationPeriod = '';
          if (edu.startYear && edu.endYearOrStatus) {
            const formattedStart = formatDateForDisplay(edu.startYear, 'MM/YYYY');
            const formattedEnd = formatDateForDisplay(edu.endYearOrStatus, 'MM/YYYY');
            
            if (formattedStart && formattedEnd) {
              educationPeriod = ` - ${formattedStart} a ${formattedEnd}`;
            } else if (formattedStart) {
              educationPeriod = ` - ${formattedStart}`;
            } else if (formattedEnd) {
              educationPeriod = ` - ${formattedEnd}`;
            }
          } else if (edu.startYear) {
            const formattedStart = formatDateForDisplay(edu.startYear, 'MM/YYYY');
            if (formattedStart) {
              educationPeriod = ` - ${formattedStart}`;
              // Se quiser adicionar " até o momento" quando não há endYearOrStatus
              // educationPeriod += ` até o momento`;
            }
          } else if (edu.endYearOrStatus) {
            const formattedEnd = formatDateForDisplay(edu.endYearOrStatus, 'MM/YYYY');
            if (formattedEnd) {
              educationPeriod = ` - ${formattedEnd}`;
            }
          }
          
          // Adicionar o curso com o período
          if (edu.course) {
            html += `<div class="item-header">${escapeHtml(edu.course)}${educationPeriod}</div>`;
          }
          
          html += `<div class="item-detail-row">`;
          if (edu.institution) {
            html += `<span class="item-location">${escapeHtml(edu.institution)}</span>`;
          } else {
            html += `<span></span>`;
          }
          
          html += `</div>`;
          
          if (edu.additionalInfo) {
            html += `<div class="item-description">${escapeHtml(edu.additionalInfo)}</div>`;
          }
          html += `</div>`;
        }
      });
    }
  } else {
    html += `<p class="no-data">Nenhuma formação acadêmica informada</p>`;
  }
  html += `</div>`;

  // Experiência Profissional - Estilo Word elegante
  html += `<div class="section">
    <div class="section-title">EXPERIÊNCIA PROFISSIONAL</div>`;
    
  if (experienceList.length > 0 && experienceList.some(exp => exp.company || exp.role || exp.activities)) {
    experienceList.forEach((exp, index) => { // Adicionado index para o separador
      if (exp.company || exp.role || exp.activities) {
        html += `<div class="experience-item">`;
        
        let period = '';
        if (exp.startYear && exp.endYear) {
          const formattedStart = formatDateForDisplay(exp.startYear, 'MM/YYYY');
          const formattedEnd = formatDateForDisplay(exp.endYear, 'MM/YYYY');
          
          if (formattedStart && formattedEnd) {
            period = ` - ${formattedStart} a ${formattedEnd}`;
          } else if (formattedStart) {
            period = ` - ${formattedStart}`;
          } else if (formattedEnd) {
            period = ` - ${formattedEnd}`;
          }
        }
        else if (exp.startYear) {
          const formattedStart = formatDateForDisplay(exp.startYear, 'MM/YYYY');
          if (formattedStart) {
            period = ` - ${formattedStart} até o momento`;
          }
        }
        else if (exp.endYear) {
          const formattedEnd = formatDateForDisplay(exp.endYear, 'MM/YYYY');
          if (formattedEnd) {
            period = ` - até ${formattedEnd}`;
          }
        }
        
        if (exp.role) {
          html += `<div class="item-header">${escapeHtml(exp.role)}${period}</div>`;
        }
        
        html += `<div class="item-detail-row">`;
        if (exp.company) {
          html += `<span class="item-location">${escapeHtml(exp.company)}</span>`;
        } else {
          html += `<span></span>`; 
        }
        
        // Período já está incorporado no título do cargo
        html += `</div>`;

        if (exp.activities) {
          const rawActivities = escapeHtml(exp.activities);
          const lineItems = rawActivities.split('\n').filter(activity => activity.trim() !== '');
          let activitiesArray = [];
          lineItems.forEach(line => {
            const commaItems = line.split(',').map(item => item.trim()).filter(item => item !== '');
            activitiesArray = [...activitiesArray, ...commaItems];
          });
          
          if (activitiesArray.length > 0) {
            if (activitiesArray.length > 5) {
              const columnCount = activitiesArray.length >= 8 ? 2 : 1; // Ajustado para 1 se menos de 8
              const itemsPerColumn = Math.ceil(activitiesArray.length / columnCount);
              const columns = [];
              for (let i = 0; i < columnCount; i++) {
                const start = i * itemsPerColumn;
                const end = Math.min(start + itemsPerColumn, activitiesArray.length);
                columns.push(activitiesArray.slice(start, end));
              }
              html += `<div style="display: flex; justify-content: space-between; margin-top: 2px; margin-left: 10px;">`;
              columns.forEach((columnItems) => { // Removido index não utilizado
                const width = 100 / columnCount - 2;
                html += `<div style="width: ${width}%;">`;
                html += `<ul class="activities-list" style="column-count: 1; margin-left: 5px;">`;
                columnItems.forEach(activity => {
                  html += `<li>${activity}</li>`;
                });
                html += `</ul>`;
                html += `</div>`;
              });
              html += `</div>`;
            } else {
              html += `<ul class="activities-list">`;
              activitiesArray.forEach(activity => {
                html += `<li>${activity}</li>`;
              });
              html += `</ul>`;
            }
          }
        }
        html += `</div>`;
        
        if (index < experienceList.length - 1) { // Corrigido para usar o index
          html += `<div class="separator"></div>`;
        }
      }
    });
  } else {
    html += `<p class="no-data">Nenhuma experiência profissional informada</p>`;
  }
  html += `</div>`;

  // Qualificações e Atividades Complementares
  html += `<div class="section">
    <div class="section-title">HABILIDADES E COMPETÊNCIAS</div>`;
  
  if (data.qualifications) {
    const rawQualifications = escapeHtml(data.qualifications);
    const lineItems = rawQualifications.split('\n').filter(q => q.trim() !== '');
    let qualificationsArray = [];
    lineItems.forEach(line => {
      const commaItems = line.split(',').map(item => item.trim()).filter(item => item !== '');
      qualificationsArray = [...qualificationsArray, ...commaItems];
    });
    
    if (qualificationsArray.length > 0) {
      const columnCount = qualificationsArray.length >= 9 ? 3 : (qualificationsArray.length > 0 ? 2 : 1); // Ajustado para 1 se vazio, 2 se poucos
      const itemsPerColumn = Math.ceil(qualificationsArray.length / columnCount);
      const columns = [];
      for (let i = 0; i < columnCount; i++) {
        const start = i * itemsPerColumn;
        const end = Math.min(start + itemsPerColumn, qualificationsArray.length);
        if (start < end) { // Adicionado para evitar colunas vazias
            columns.push(qualificationsArray.slice(start, end));
        }
      }
      
      if (columns.length > 0) { // Adicionado para evitar div vazia
        html += `<div style="display: flex; justify-content: space-between; margin-top: 3px;">`;
        columns.forEach((columnItems) => { // Removido index não utilizado
            const width = 100 / columns.length - 2; // Usar columns.length
            html += `<div style="width: ${width}%;">`;
            html += `<ul class="skills-list" style="column-count: 1;">`;
            columnItems.forEach(qualification => {
            html += `<li>${qualification}</li>`;
            });
            html += `</ul>`;
            html += `</div>`;
        });
        html += `</div>`;
      }
    }
  } else {
    html += `<p class="no-data">Não informado</p>`;
  }
  html += `</div>`;

  // Informações Adicionais
  if (data.additionalInfo) {
    html += `<div class="section">
      <div class="section-title">INFORMAÇÕES COMPLEMENTARES</div>`;
      
    const rawInfo = escapeHtml(data.additionalInfo);
    const lineItems = rawInfo.split('\n').filter(info => info.trim() !== '');
    let additionalInfoArray = [];
    lineItems.forEach(line => {
      const commaItems = line.split(',').map(item => item.trim()).filter(item => item !== '');
      additionalInfoArray = [...additionalInfoArray, ...commaItems];
    });
    
    if (additionalInfoArray.length > 0) {
      if (additionalInfoArray.length > 5) {
        const columnCount = additionalInfoArray.length >= 8 ? 2 : 1; // Ajustado
        const itemsPerColumn = Math.ceil(additionalInfoArray.length / columnCount);
        const columns = [];
        for (let i = 0; i < columnCount; i++) {
          const start = i * itemsPerColumn;
          const end = Math.min(start + itemsPerColumn, additionalInfoArray.length);
           if (start < end) { // Adicionado para evitar colunas vazias
             columns.push(additionalInfoArray.slice(start, end));
           }
        }
        
        if (columns.length > 0) { // Adicionado para evitar div vazia
            html += `<div style="display: flex; justify-content: space-between; margin-top: 2px; margin-left: 10px;">`;
            columns.forEach((columnItems) => { // Removido index não utilizado
                const width = 100 / columns.length - 2; // Usar columns.length
                html += `<div style="width: ${width}%;">`;
                html += `<ul class="activities-list" style="column-count: 1; margin-left: 5px;">`;
                columnItems.forEach(info => {
                html += `<li>${info}</li>`;
                });
                html += `</ul>`;
                html += `</div>`;
            });
            html += `</div>`;
        }

      } else {
        html += `<ul class="activities-list">`;
        additionalInfoArray.forEach(info => {
          html += `<li>${info}</li>`;
        });
        html += `</ul>`;
      }
    }
    html += `</div>`;
  }
  
  html += `
      </div>
    </body>
  </html>
  `;
  return html;
}