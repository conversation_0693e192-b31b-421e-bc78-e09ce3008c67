@import url('https://fonts.googleapis.com/css2?family=Times+New+Roman&family=Calibri:wght@400;700&display=swap');

body {
  font-family: 'Calibri', 'Times New Roman', serif;
  line-height: 1.4;
  margin: 0;
  padding: 5px 15px; /* Reduzido ainda mais para maximizar espaço útil */
  background-color: #FFFFFF;
  color: #000000;
  font-size: 10.5pt; /* Ligeiramente menor para caber mais conteúdo */
}

.container {
  max-width: 95%; /* Quase toda a largura da página */
  margin: 0 auto;
}

/* Cabeçalho elegante ajustado */
.header {
  text-align: center;
  margin-bottom: 12px;
  border-bottom: 1px solid #000;
  padding-bottom: 8px;
}

.header-name {
  font-size: 16pt;
  font-weight: bold;
  margin-top: 0px; /* Elimina margem superior */
  margin-bottom: 6px;
  font-family: 'Times New Roman', serif;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #000; /* Garantir cor sólida */
}

.personal-details {
  font-size: 11pt;
  line-height: 1.4;
  text-align: center;
}

.personal-details p {
  margin: 3px 0;
  color: #000; /* Garantir que não fique apagado */
}

/* Seções elegantes com mais destaque e menos espaçamento */
.section {
  margin-bottom: 10px;
  clear: both;
}

.section-title {
  font-size: 11pt;
  font-weight: bold;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid #000; /* Mais escuro para melhor visibilidade */
  padding-bottom: 1px;
  color: #000; /* Garantir cor totalmente sólida */
}

/* Listas elegantes com indentação reduzida para economizar espaço */
ul {
  padding-left: 12px;
  list-style-type: none;
  margin: 3px 0 6px 0;
}

ul li {
  margin-bottom: 3px;
  position: relative;
  padding-left: 12px;
  text-align: justify;
  color: #000; /* Garantir cor preta para todos os itens */
}

ul li::before {
  content: "•";
  position: absolute;
  left: 0;
  top: 0;
  font-weight: bold; /* Bullet mais visível */
}

/* Listas de habilidades organizadas em duas colunas */
.skills-list {
  column-count: 2;
  column-gap: 20px;
}

.skills-list li {
  break-inside: avoid;
  padding-left: 15px;
  margin-bottom: 4px;
}

/* Itens compactos para maximizar o espaço */
.experience-item, .education-item {
  margin-bottom: 7px;
  page-break-inside: avoid;
  padding-left: 0; /* Reset para o padding externo */
}

.item-header {
  font-weight: bold;
  font-size: 10.8pt;
  margin-bottom: 1px;
  padding-top: 1px; /* Pequeno espaçamento superior */
  color: #000; /* Cor sólida para títulos */
}

.item-detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
  font-style: italic;
  margin-left: 10px; /* Indentação reduzida para economizar espaço */
  margin-right: 3px; /* Espaço à direita para não colar na borda */
  color: #000; /* Evitar texto apagado */
}

.item-location {
  font-weight: normal;
  font-style: italic;
  color: #000; /* Garantir visibilidade */
}

.item-period {
  white-space: nowrap;
  font-weight: 600; /* Um pouco mais bold para datas */
  color: #000; /* Garantir visibilidade */
}

.item-description {
  font-size: 10pt;
  margin-top: 2px;
  margin-left: 10px; /* Indentação reduzida */
  margin-bottom: 3px; /* Espaço após a descrição para separar itens */
  text-align: justify;
  line-height: 1.3; /* Mais compacto */
  color: #000; /* Garantir visibilidade */
}

/* Parágrafos */
p {
  margin: 3px 0;
  text-align: justify;
  color: #000; /* Garantir cor preta sólida */
}

.no-data {
  font-style: italic;
  color: #333; /* Mais escuro para não ficar apagado */
  font-size: 10pt;
}

/* Separadores */
.separator {
  height: 1px;
  background-color: #aaa; /* Mais visível */
  margin: 10px 0;
}

/* Estilo para as atividades indentadas - otimizado para listas longas */
.activities-list {
  margin-left: 12px;
}

/* Estilos para listas de atividades em múltiplas colunas */
.activities-columns {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-left: 10px;
}

.activities-column {
  padding-right: 10px;
}

/* Estilos para formação acadêmica em múltiplas colunas */
.education-columns {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.education-column {
  padding-right: 8px;
}

.education-item {
  margin-bottom: 6px;
  page-break-inside: avoid;
}

@media print {
  body {
    padding: 5px 10px;
    font-size: 10.5pt; /* Fonte menor para impressão */
  }
  .container {
    width: 100%;
    max-width: none;
  }
  .section-title {
    break-after: avoid;
  }
  .section {
    break-inside: avoid-page;
  }
  .page-break {
    page-break-before: always;
  }
}