// src/modules/resume-generator/services/resume.service.js
import { put } from '@vercel/blob';
import { readResumeData, writeResumeData } from './resume-storage.service.js';
import { generateResumeHtml } from '../templates/resume-html-template.js';
import { generatePdfFromHtml } from './pdf-generation.service.js';

/**
 * Orquestra a criação completa de um currículo, desde o recebimento dos dados
 * até o salvamento do PDF e retorno da URL.
 * @param {object} formData Os dados do formulário do currículo.
 * @returns {Promise<object>} Um objeto indicando sucesso ou falha, com mensagem e URL do currículo se sucesso.
 */
export async function createResume(formData) {
  try {
    // 1. Gerar ID único para o currículo
    const resumeId = Date.now();

    // 2. Gerar o conteúdo HTML do currículo
    const htmlContent = generateResumeHtml(formData);

    // 3. Gerar o buffer do PDF a partir do HTML
    const pdfBuffer = await generatePdfFromHtml(htmlContent);

    // 4. Upload do buffer para Vercel Blob
    const blobFileName = `resumes/${resumeId}.pdf`;
    const blob = await put(blobFileName, pdfBuffer, {
      access: 'public', // Torna o arquivo publicamente acessível via URL
      contentType: 'application/pdf', // Define o tipo de conteúdo
    });
    const resumeBlobUrl = blob.url;
    
    // 5. Ler dados existentes do Redis
    const allResumes = await readResumeData();
    
    // 6. Adicionar novo currículo COM a URL do Blob já obtida
    // Certifique-se de que formData já contém createdAt
    const newResumeEntry = { id: resumeId, ...formData, resumeUrl: resumeBlobUrl };
    allResumes.push(newResumeEntry);
    
    // 7. Salvar no Redis com a URL do Blob
    await writeResumeData(allResumes);

    // 8. Retornar sucesso e a URL do Blob
    return {
      success: true,
      message: 'Currículo gerado e salvo com sucesso no Vercel Blob e dados no Redis!',
      resumeUrl: resumeBlobUrl,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao processar a geração do currículo.';
    return {
      success: false,
      message: errorMessage,
    };
  }
}