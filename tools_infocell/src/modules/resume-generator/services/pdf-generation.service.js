// src/modules/resume-generator/services/pdf-generation.service.js
import core from 'puppeteer-core';
import chromium from '@sparticuz/chromium';
import fs from 'fs';
import path from 'path';
import os from 'os';

/**
 * Encontra o executável do Chrome/Chromium no sistema operacional.
 * @returns {string|null} O caminho para o executável do Chrome/Chromium ou null se não for encontrado.
 */
function findChromeExecutable() {
  const platform = os.platform();
  let chromePaths = [];

  if (platform === 'linux') {
    chromePaths = [
      '/usr/bin/google-chrome',
      '/usr/bin/chromium',
      '/usr/bin/chromium-browser',
      '/snap/bin/chromium',
    ];
  } else if (platform === 'darwin') { // macOS
    chromePaths = [
      '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
      '/Applications/Chromium.app/Contents/MacOS/Chromium',
    ];
  } else if (platform === 'win32') { // Windows
    chromePaths = [
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
      path.join(os.homedir(), 'AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'),
    ];
  }

  // Verifica cada caminho para ver se o arquivo existe
  for (const chromePath of chromePaths) {
    try {
      if (fs.existsSync(chromePath)) {
        console.log(`Chrome/Chromium encontrado em: ${chromePath}`);
        return chromePath;
      }
    } catch (error) {
      console.error(`Erro ao verificar caminho do Chrome: ${chromePath}`, error);
    }
  }

  console.error('Chrome/Chromium não encontrado no sistema. Por favor, instale o navegador.');
  return null;
}

/**
 * Gera um buffer de PDF a partir de um conteúdo HTML usando Puppeteer.
 * @param {string} htmlContent O conteúdo HTML a ser convertido em PDF.
 * @returns {Promise<Buffer>} Uma promessa que resolve para o buffer do PDF gerado.
 * @throws {Error} Se ocorrer um erro durante a geração do PDF.
 */
export async function generatePdfFromHtml(htmlContent) {
  let browser = null;
  try {
    // Determina o executável com base no ambiente
    let executablePath;
    let options = {};
    
    if (process.env.NODE_ENV === 'production') {
      // Na Vercel (ambiente de produção), use as opções do @sparticuz/chromium
      try {
        console.log("Configurando Puppeteer para ambiente de produção na Vercel");
        
        // Para produção, usar o Chromium do @sparticuz/chromium
        executablePath = await chromium.executablePath();
        
        console.log("Chromium executablePath:", executablePath);
        
        options = {
          args: chromium.args,
          executablePath: executablePath,
          headless: chromium.headless,
          ignoreHTTPSErrors: true
        };
        
        console.log("Opções do Puppeteer configuradas:", JSON.stringify({
          executablePath: options.executablePath,
          argsCount: options.args.length,
          headless: options.headless,
        }));
      } catch (error) {
        console.error("Erro ao configurar Chromium:", error);
        throw new Error(`Falha na configuração do navegador Chromium: ${error.message}`);
      }
    } else {
      // Em desenvolvimento, tentamos encontrar o Chrome/Chromium no sistema
      executablePath = findChromeExecutable();
      if (!executablePath) {
        throw new Error('Não foi possível encontrar o executável do Chrome/Chromium. Verifique se ele está instalado.');
      }
      options = {
        args: ['--font-render-hinting=none', '--disable-gpu', '--single-process'],
        executablePath,
        headless: 'new',
        ignoreHTTPSErrors: true
      };
    }

    console.log("Iniciando navegador com as opções configuradas");
    try {
      browser = await core.launch(options);
      console.log("Navegador iniciado com sucesso");
    } catch (browserError) {
      console.error("Erro ao iniciar o navegador:", browserError);
      console.log("Tentando novamente com opções alternativas...");
      
      // Tente com opções mais simples se falhar na primeira tentativa
      options = {
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-gpu',
          '--disable-dev-shm-usage',
          '--single-process'
        ],
        executablePath: executablePath,
        headless: true,
        ignoreHTTPSErrors: true
      };
      
      browser = await core.launch(options);
      console.log("Navegador iniciado com sucesso na segunda tentativa");
    }
    
    const page = await browser.newPage();

    // Define o conteúdo da página. 'networkidle0' espera até que não haja mais de 0 conexões de rede por pelo menos 500 ms.
    // Isso é útil para garantir que fontes externas (como Google Fonts) sejam carregadas.
    await page.setContent(htmlContent, { waitUntil: 'networkidle0', timeout: 30000 });

    // Emula o tipo de mídia 'print' para aplicar estilos de impressão CSS.
    await page.emulateMediaType('print');
    
    // Configurações para a geração do PDF.
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '12mm', right: '10mm', bottom: '12mm', left: '10mm' },
      preferCSSPageSize: true,
      displayHeaderFooter: false,
      scale: 1,
      landscape: false,
    });

    return pdfBuffer;
  } catch (error) {
    console.error('Erro ao gerar PDF com Puppeteer:', error);
    console.error('Stack trace:', error.stack);
    throw new Error(`Falha ao gerar o arquivo PDF do currículo: ${error.message}`);
  } finally {
    if (browser) {
      try {
        await browser.close();
      } catch (closeError) {
        console.error("Erro ao fechar o browser do Puppeteer:", closeError);
      }
    }
  }
}
