// src/modules/resume-generator/services/resume-storage.service.js
import { createClient } from 'redis';

const redisUrl = process.env.REDIS_URL;
if (!redisUrl) {
  console.error('CRÍTICO: A variável de ambiente REDIS_URL não está definida.');
  // Lançar um erro aqui ou ter um fallback pode ser necessário dependendo da estratégia de erro.
  // Por enquanto, logamos e as operações do Redis provavelmente falharão.
}

// Cria e conecta o cliente Redis no escopo do módulo.
// O 'await' aqui no top-level é suportado em módulos ES.
const redisClient = await createClient({
  url: redisUrl,
  // Adicionar socket options para TLS se necessário para Redis Cloud:
  socket: {
    // tls: true,
    rejectUnauthorized: false // Use com cautela, idealmente configure CAs corretamente
  }
})
.on('error', (err) => {
  console.error('Erro de Cliente Redis (node-redis):', err);
  // Em um ambiente serverless, a reconexão automática do node-redis pode ser suficiente.
  // Se a conexão for perdida permanentemente, as próximas operações falharão.
})
.on('connect', () => console.log('Conectado ao Redis com sucesso (node-redis).'))
.on('reconnecting', () => console.log('Redis cliente reconectando...'))
.on('end', () => console.log('Conexão Redis encerrada (node-redis).'))
.connect()
.catch(err => { // Adicionado catch para a promessa de conexão inicial
  console.error('Falha CRÍTICA ao conectar ao Redis na inicialização do módulo:', err);
  // Se a conexão falhar na inicialização, o redisClient pode não ser utilizável.
  // As funções read/write dependerão do estado do redisClient.
  // Pode ser preferível lançar um erro aqui para impedir a inicialização da aplicação se o Redis for essencial.
});

const RESUMES_DB_KEY = 'infocell_app_all_resumes_data_v3'; // Nova chave para esta versão

// --- INÍCIO DO DADO DE EXEMPLO ---
const exampleResumeData = [
  {
    id: 1700000000001, // Fixo e único para o exemplo
    personalInfo: {
      fullName: "Maria Exemplo da Costa",
      nationality: "Brasileira",
      gender: "Feminino",
      age: "27",
      civilStatus: "Solteiro(a)",
      hasChildren: "Não",
      address: "Avenida das Amostras, 456, Bairro Piloto",
      state: "PR",
      city: "Curitiba",
      phone1: "(41) 97777-6666",
      phone2: "(41) 3333-4444",
      email: "<EMAIL>"
    },
    objective: "Atuar na área de design gráfico e UX/UI Design, desenvolvendo projetos criativos e inovadores que agreguem valor à marca e proporcionem uma experiência de usuário excepcional, contribuindo para o crescimento e destaque da empresa no mercado digital.",
    education: [
      {
        course: "Design Gráfico",
        institution: "Universidade Federal de Testes",
        startYear: "2016",
        endYearOrStatus: "2020",
        additionalInfo: "Prêmio de melhor portfólio da turma. TCC sobre design responsivo e acessibilidade."
      },
      {
        course: "Especialização em UX/UI Design",
        institution: "Instituto de Design Digital",
        startYear: "2021",
        endYearOrStatus: "2022",
        additionalInfo: "Projeto final implementado em startup de tecnologia."
      },
      {
        course: "Certificação em Design Thinking",
        institution: "Innovation Academy",
        startYear: "2022",
        endYearOrStatus: "2022",
        additionalInfo: "Formação intensiva de 120 horas."
      }
    ],
    experience: [
      {
        company: "Criativa Design Studio",
        startYear: "03/2021",
        endYear: "Atual",
        role: "Designer Gráfico Pleno",
        activities: "Criação de identidades visuais, material de marketing digital e impresso, interfaces para websites e aplicativos. Liderança de equipe de 3 designers júnior. Gerenciamento de projetos para clientes de médio e grande porte. Implementação de metodologias ágeis no departamento de design."
      },
      {
        company: "Agência Digital Inovare",
        startYear: "06/2020",
        endYear: "02/2021",
        role: "Designer Gráfico Júnior",
        activities: "Desenvolvimento de peças para redes sociais, criação de landing pages, apoio na criação de identidade visual para clientes da agência. Participação em brainstormings para campanhas digitais."
      },
      {
        company: "TechWeb Solutions",
        startYear: "01/2019",
        endYear: "12/2019",
        role: "Estagiária de Design",
        activities: "Suporte na criação de layouts para websites e aplicativos, desenvolvimento de banners para marketing digital, edição de imagens para conteúdos de redes sociais. Participação em workshops internos de UX."
      }
    ],
    qualifications: "Domínio avançado de Adobe Photoshop, Illustrator, InDesign, XD e Figma. Conhecimentos em Principle e After Effects para motion design. Experiência em UI/UX Design e prototipação. Inglês avançado (TOEFL iBT 105) e espanhol intermediário. Conhecimento básico de HTML, CSS e JavaScript para comunicação eficiente com desenvolvedores.",
    additionalInfo: "Portfólio online disponível em: www.mariaexemplo-portfolio.com.br. Participação como palestrante em eventos regionais de design. Mentora voluntária no programa 'Design para Todos' que oferece capacitação gratuita para estudantes de baixa renda. Organização de workshops mensais sobre tendências de design na comunidade local.",
    currentSituation: {
      employed: true,
      employmentType: "CLT",
      salary: "R$ 6.500,00",
      seekingNewOpportunities: true,
      availableForInterviews: "Horários flexíveis após 18h",
      availableImmediately: false,
      noticePeriod: "30 dias"
    },
    // Substitua '_URL_DO_PDF_DE_EXEMPLO_NO_BLOB' pela URL real de um PDF de exemplo
    // já carregado para o Vercel Blob para este currículo.
    // Pode ser deixado como null ou uma string vazia, mas o download não funcionará.
    resumeUrl: null, // ou 'https://infocell-storage.com/resumes/1700000000001.pdf'
    createdAt: new Date("2024-02-01T11:00:00.000Z").toISOString()
  }
];
// --- FIM DO DADO DE EXEMPLO ---

/**
 * Lê os dados do Redis para currículos.
 * @returns {Promise<Array<Object>>} Uma promessa que resolve para um array de objetos de currículo.
 * @throws {Error} Se não for possível ler os dados.
 */
export async function readResumeData() {
  if (!redisClient || !redisClient.isReady) { // Verifica se o cliente está pronto
    console.warn('Cliente Redis não conectado ou não pronto para ler dados. Retornando dados de exemplo.');
    return JSON.parse(JSON.stringify(exampleResumeData)); // Retorna uma cópia para evitar mutação do original
  }
  try {
    const resumesJsonString = await redisClient.get(RESUMES_DB_KEY);
    if (resumesJsonString === null || resumesJsonString === undefined) {
      console.log(`Chave '${RESUMES_DB_KEY}' não encontrada no Redis. Populando com dados de exemplo.`);
      await writeResumeData(exampleResumeData); // Usa a função writeResumeData para garantir consistência
      return JSON.parse(JSON.stringify(exampleResumeData));
    }
    const parsedData = JSON.parse(resumesJsonString);
    if (Array.isArray(parsedData) && parsedData.length === 0) {
      console.log(`Chave '${RESUMES_DB_KEY}' encontrada vazia no Redis. Populando com dados de exemplo.`);
      await writeResumeData(exampleResumeData);
      return JSON.parse(JSON.stringify(exampleResumeData));
    }
    return parsedData;
  } catch (error) {
    console.error(`Erro ao ler dados do Redis com node-redis (chave: ${RESUMES_DB_KEY}):`, error);
    console.warn("Retornando dados de exemplo devido a erro na leitura do Redis.");
    return JSON.parse(JSON.stringify(exampleResumeData));
  }
}

/**
 * Escreve os dados no Redis para currículos.
 * @param {Array<Object>} data O array de objetos de currículo a ser salvo.
 * @throws {Error} Se não for possível escrever os dados.
 */
export async function writeResumeData(data) {
  if (!redisClient || !redisClient.isReady) {
    console.error('Cliente Redis não conectado ou não pronto para escrever dados.');
    throw new Error('Não foi possível salvar os dados dos currículos - Cliente Redis não pronto.');
  }
  try {
    await redisClient.set(RESUMES_DB_KEY, JSON.stringify(data));
  } catch (error) {
    console.error(`Erro ao escrever dados no Redis com node-redis (chave: ${RESUMES_DB_KEY}):`, error);
    throw new Error('Não foi possível salvar os dados dos currículos.');
  }
}