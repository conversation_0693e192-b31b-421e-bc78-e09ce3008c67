import { useCallback } from 'react';

export const useDownload = () => {
  const downloadFile = useCallback(async (blob, filename) => {
    // Verificar se estamos no cliente
    if (typeof window === 'undefined') {
      throw new Error('Download não disponível no servidor');
    }

    try {
      const url = window.URL.createObjectURL(blob);
      
      // Criar link temporário para download
      const link = window.document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';
      
      // Adicionar ao DOM, clicar e remover
      window.document.body.appendChild(link);
      link.click();
      
      // Cleanup
      window.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
    } catch (error) {
      throw new Error(`Erro ao fazer download: ${error.message}`);
    }
  }, []);

  return { downloadFile };
};

export default useDownload; 