import { useCallback } from 'react';

const usePrint = () => {
  /**
   * Abre um blob de arquivo (PDF) em uma nova aba e aciona o diálogo de impressão.
   * Esta é uma abordagem mais robusta do que usar um iframe oculto.
   * @param {Blob} blob - O conteúdo do arquivo como um objeto Blob.
   * @param {string} fileType - O tipo MIME do arquivo (ex: 'application/pdf').
   * @returns {Promise<void>} Uma promessa que resolve quando a impressão é acionada.
   */
  const printFile = useCallback((blob, fileType = 'application/pdf') => {
    return new Promise((resolve, reject) => {
      if (!blob) {
        return reject(new Error('Blob de arquivo é necessário para impressão.'));
      }

      try {
        // Cria uma URL temporária para o blob
        const blobUrl = URL.createObjectURL(new Blob([blob], { type: fileType }));

        // Abre a URL em uma nova aba
        const printWindow = window.open(blobUrl, '_blank');

        if (!printWindow) {
          // Isso acontece se o navegador bloquear o pop-up
          URL.revokeObjectURL(blobUrl); // Limpar a URL do blob
          return reject(new Error('Não foi possível abrir a janela de impressão. Verifique se os pop-ups estão bloqueados.'));
        }

        // Quando a nova janela carregar seu conteúdo, aciona a impressão
        printWindow.onload = function() {
          // Um pequeno timeout aqui pode ajudar a garantir que o PDF renderize completamente
          // na nova aba antes do diálogo de impressão aparecer.
          setTimeout(() => {
            printWindow.focus();
            printWindow.print();
            // A URL do blob será revogada automaticamente quando a aba for fechada.
          }, 200);
        };

        // Listener de erro caso a nova janela falhe ao carregar
        printWindow.onerror = function() {
          URL.revokeObjectURL(blobUrl); // Limpar a URL do blob em caso de erro
          reject(new Error('Falha ao carregar o documento na nova aba para impressão.'));
        };

        // Resolve a promessa imediatamente após abrir a janela com sucesso
        resolve();

      } catch (error) {
        console.error("Erro na função printFile:", error);
        reject(error);
      }
    });
  }, []);

  return { printFile };
};

export default usePrint;