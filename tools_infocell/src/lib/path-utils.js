
const isProd = process.env.NODE_ENV === 'production';

export const APP_PREFIX = isProd ? '/dashboard-infocell' : '/dashboard-infocell';


export const ASSET_PREFIX = isProd ? 'https://tools-infocell.vercel.app/dashboard-infocell' : '';


export const BASE_URL = isProd ? 'https://tools-infocell.vercel.app' : '';

/**
 * Retorna o caminho correto para um asset na pasta public
 * @param {string} path - Caminho relativo ao asset na pasta public
 * @returns {string} Caminho completo para o asset
 */
export function getAssetPath(path) {
  // Remove qualquer barra inicial para evitar duplo slash
  const cleanPath = path.startsWith('/') ? path.substring(1) : path;
  
  if (isProd) {
    // Em produção, retorna a URL absoluta completa
    return `${ASSET_PREFIX}/${cleanPath}`;
  }
  
  // Em desenvolvimento, apenas adiciona a barra inicial
  return `/${cleanPath}`;
}
