// filepath: /home/<USER>/Projetos/Infocell/tools_infocell/src/lib/infocellApi.js
/**
 * Cliente API centralizado para comunicação com o infocell-server (Axum)
 * Integrado com o sistema de autenticação existente do dashboard
 */

class InfocellApiClient {
  constructor() {
    // Em produção, usar o proxy do Next.js. Em desenvolvimento, chamar diretamente
      // Em produção, usar o proxy do Next.js. Em desenvolvimento, chamar diretamente
      const isProduction = process.env.NODE_ENV === 'production';
    
      // Forçar conexão direta para teste em produção
      this.baseURL = process.env.NEXT_PUBLIC_INFOCELL_SERVER_API_URL || 'http://localhost:3001';
      this.apiVersion = '/api/v1';
    }

  /**
   * Obtém o token JWT do cookie (compatível com o sistema existente)
   */
  getAuthToken() {
    if (typeof document !== 'undefined') {
      const cookies = document.cookie.split(';');
      const authCookie = cookies.find(cookie => 
        cookie.trim().startsWith('authToken=')
      );
      return authCookie ? authCookie.split('=')[1] : null;
    }
    return null;
  }

  /**
   * Cria os headers padrão para requisições
   */
  getHeaders(includeAuth = true) {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (includeAuth) {
      const token = this.getAuthToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  /**
   * Método genérico para fazer requisições HTTP
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${this.apiVersion}${endpoint}`;
    
    const config = {
      method: 'GET',
      headers: this.getHeaders(options.includeAuth !== false),
      ...options,
    };

    // Para FormData, remove o Content-Type para permitir que o browser defina automaticamente
    if (config.body instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    try {
      console.log(`[InfocellAPI] ${config.method} ${url}`);
      
      const response = await fetch(url, config);
      
      // Se não autorizado, redirecionar para login
      if (response.status === 401) {
        console.warn('Token expirado ou inválido, redirecionando para login...');
        window.location.href = '/dashboard-infocell/auth/login?error=session_expired';
        return;
      }

      // Se não for JSON, retornar o response para permitir download de arquivos
      const contentType = response.headers.get('content-type');
      console.log('Content-Type da resposta:', contentType);
      
      if (!contentType || !contentType.includes('application/json')) {
        if (!response.ok) {
          throw new Error(`Erro HTTP ${response.status}: ${response.statusText}`);
        }
        return response;
      }

      const data = await response.json();

      if (!response.ok) {
        // Prioriza a mensagem de erro do campo "error", depois "message", depois um padrão.
        const errorMessage = data.error || data.message || `Erro HTTP ${response.status}: ${response.statusText}`;
        throw new Error(errorMessage);
      }

      return data;
    } catch (error) {
      console.error(`[InfocellAPI] Erro na requisição para ${endpoint}:`, error);
      throw error;
    }
  }

  // Métodos HTTP convenientes
  async get(endpoint, options = {}) {
    return this.request(endpoint, { method: 'GET', ...options });
  }

  async post(endpoint, data, options = {}) {
    const body = data instanceof FormData ? data : JSON.stringify(data);
    return this.request(endpoint, { method: 'POST', body, ...options });
  }

  async put(endpoint, data, options = {}) {
    return this.request(endpoint, { 
      method: 'PUT', 
      body: JSON.stringify(data), 
      ...options 
    });
  }

  async delete(endpoint, options = {}) {
    return this.request(endpoint, { method: 'DELETE', ...options });
  }

  // === TEMPLATES API ===

  /**
   * Lista todos os templates
   */
  async getTemplates(params = {}) {
    const searchParams = new URLSearchParams();
    
    if (params.template_type) searchParams.append('template_type', params.template_type);
    if (params.is_active !== undefined) searchParams.append('is_active', params.is_active);
    if (params.page) searchParams.append('page', params.page);
    if (params.per_page) searchParams.append('per_page', params.per_page);

    const query = searchParams.toString();
    const endpoint = query ? `/templates?${query}` : '/templates';
    
    return this.get(endpoint);
  }

  /**
   * Faz upload de um novo template
   */
  async uploadTemplate(templateData) {
    const formData = new FormData();
    
    formData.append('name', templateData.name);
    formData.append('template_type', templateData.template_type);
    if (templateData.description) {
      formData.append('description', templateData.description);
    }
    formData.append('file', templateData.file);

    return this.post('/templates', formData);
  }

  /**
   * Atualiza um template existente
   */
  async updateTemplate(templateId, updateData) {
    return this.put(`/templates/${templateId}`, updateData);
  }

  /**
   * Exclui um template
   */
  async deleteTemplate(templateId) {
    return this.delete(`/templates/${templateId}`);
  }

  /**
   * Obtém um template específico
   */
  async getTemplate(templateId) {
    return this.get(`/templates/${templateId}`);
  }

  /**
   * Faz download de um template
   */
  async downloadTemplate(templateId) {
    const response = await this.request(`/templates/${templateId}/download`, {
      method: 'GET',
      responseType: 'blob'
    });
    return response;
  }

  // === CLIENTS API ===

  /**
   * Busca clientes no ERP, opcionalmente filtrando por nome.
   */
  async getClients(params = {}) {
    const searchParams = new URLSearchParams();
    
    if (params.nome) searchParams.append('nome', params.nome);
    if (params.page) searchParams.append('page', params.page);
    if (params.per_page) searchParams.append('per_page', params.per_page);

    const queryString = searchParams.toString();
    const endpoint = queryString ? `/clients?${queryString}` : '/clients';
    
    return this.get(endpoint);
  }

  /**
   * Obtém um cliente específico
   */
  async getClient(clientId) {
    return this.get(`/clients/${clientId}`);
  }

  /**
   * Cria um novo cliente no ERP
   */
  async createClient(clientData) {
    return this.post('/clients', clientData);
  }

  /**
   * Faz upload de foto de perfil para um cliente
   */
  async uploadProfilePicture(clientId, file) {
    const formData = new FormData();
    formData.append('profilePictureFile', file);
    
    return this.post(`/clients/${clientId}/profile-picture`, formData);
  }

  /**
   * Obtém a URL da foto de perfil de um cliente
   */
  getProfilePictureUrl(clientId, fileName) {
    if (!clientId || !fileName) return null;
    return `${this.baseURL}/api/v1/clients/${clientId}/profile-picture/${fileName}`;
  }

  // === LOCAL CLIENT SUPPLEMENT API ===

  /**
   * Obtém dados suplementares de um cliente local
   */
  async getLocalClientSupplement(erpClientId) {
    return this.get(`/local-client-supplement/${erpClientId}`);
  }

  /**
   * Salva dados suplementares de um cliente local
   */
  async saveLocalClientSupplement(erpClientId, supplementData) {
    return this.post(`/local-client-supplement/${erpClientId}`, supplementData);
  }

  /**
   * Atualiza dados suplementares de um cliente local
   */
  async updateLocalClientSupplement(erpClientId, supplementData) {
    return this.put(`/local-client-supplement/${erpClientId}`, supplementData);
  }

  // === DOCUMENT GENERATION API ===

  /**
   * Gera um documento
   */
  async generateDocument(generateData) {
    return this.post('/documents/generate', generateData);
  }

  /**
   * Faz download de um documento gerado
   */
  async downloadDocument(documentId) {
    const response = await this.request(`/documents/${documentId}/download`, {
      method: 'GET',
      responseType: 'blob'
    });
    return response;
  }

  /**
   * Converte um documento DOCX para PDF (usado para impressão)
   */
  async convertDocxToPdf(documentId) {
    const response = await this.request(`/documents/${documentId}/convert-to-pdf`, {
      method: 'POST',
      responseType: 'blob'
    });
    return response;
  }

  /**
   * Lista histórico de documentos gerados
   */
  async listDocumentHistory(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = queryString ? `/documents/history?${queryString}` : '/documents/history';
    return this.get(endpoint);
  }

  /**
   * Exclui um documento gerado
   */
  async deleteDocument(documentId) {
    return this.delete(`/documents/${documentId}`);
  }

  /**
   * Obtém um link de compartilhamento para um documento gerado
   */
  async getDocumentShareLink(documentId) {
    return this.get(`/documents/${documentId}/share-link`);
  }

  // === RECHARGE PACKAGES API ===

  /**
   * Lista todos os pacotes de recarga
   */
  async getRechargePackages() {
    return this.get('/recharge-packages');
  }

  /**
   * Obtém um pacote de recarga específico
   */
  async getRechargePackage(packageId) {
    return this.get(`/recharge-packages/${packageId}`);
  }

  /**
   * Cria um novo pacote de recarga
   */
  async createRechargePackage(packageData) {
    return this.post('/recharge-packages', packageData);
  }

  /**
   * Atualiza um pacote de recarga existente
   */
  async updateRechargePackage(packageId, updateData) {
    return this.put(`/recharge-packages/${packageId}`, updateData);
  }

  /**
   * Exclui um pacote de recarga
   */
  async deleteRechargePackage(packageId) {
    return this.delete(`/recharge-packages/${packageId}`);
  }

  // === ERP PROXY API ===

  /**
   * Lista os fornecedores do ERP
   */
  async getSuppliers(params = {}) {
    const searchParams = new URLSearchParams(params);
    return this.get(`/erp-proxy/suppliers?${searchParams.toString()}`);
  }

  /**
   * Lista os funcionários do ERP
   */
  async getEmployees(params = {}) {
    const searchParams = new URLSearchParams(params);
    return this.get(`/erp-proxy/employees?${searchParams.toString()}`);
  }

  // === RECHARGE CODES API ===

  /**
   * Importa um lote de códigos de recarga
   */
  async importRechargeCodes(importData) {
    return this.post('/recharge-codes/import', importData);
  }

  /**
   * Lista o estoque de códigos de recarga
   */
  async getRechargeStock(params = {}) {
    const searchParams = new URLSearchParams(params);
    const endpoint = `/recharge-codes/stock?${searchParams.toString()}`;
    return this.get(endpoint);
  }

  // === RECHARGE SALES API ===

  /**
   * Cria um novo registro de venda de recarga
   */
  async createRechargeSale(saleData) {
    return this.post('/recharge-sales', saleData);
  }

  /**
   * Lista os registros de venda (relatório)
   */
  async getSalesReports(params = {}) {
    const searchParams = new URLSearchParams(params);
    const endpoint = `/recharge-sales/report?${searchParams.toString()}`;
    return this.get(endpoint);
  }
}

// Exporta uma instância singleton
const infocellApi = new InfocellApiClient();
export default infocellApi;