import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

/**
 * Formata uma data que pode vir no formato BSON/JSON do MongoDB.
 * @param {object|string} timestamp - O objeto de data (ex: {$date: "..."}) ou uma string de data.
 * @param {object} options - Opções para toLocaleString (opcional).
 * @returns {string} A data formatada ou uma string vazia.
 */
export function formatBsonDate(timestamp, options = {
  day: '2-digit',
  month: '2-digit',
  year: 'numeric',
  hour: '2-digit',
  minute: '2-digit'
}) {
  if (!timestamp) return '';

  try {
    // Verifica se é o objeto BSON/JSON
    if (typeof timestamp === 'object' && timestamp !== null && timestamp.$date) {
      const dateValue = timestamp.$date.$numberLong 
        ? parseInt(timestamp.$date.$numberLong, 10) 
        : timestamp.$date;
      return new Date(dateValue).toLocaleString('pt-BR', options);
    }
    
    // Fallback para strings de data normais
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return "Data Inválida";
    
    return date.toLocaleString('pt-BR', options);
  } catch (error) {
    console.error("Erro ao formatar data:", error);
    return "Data Inválida";
  }
}
