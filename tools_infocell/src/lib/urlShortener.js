/**
 * Utilitário para encurtar URLs usando o serviço TinyURL
 * 
 * Alternativas para considerar no futuro:
 * 1. Bitly API (requer autenticação): https://dev.bitly.com/
 * 2. Rebrandly (requer autenticação): https://developers.rebrandly.com/
 * 3. Implementar encurtador próprio no backend para mais controle
 * 4. Usar is.gd API: https://is.gd/api.php
 */

export async function shortenUrl(longUrl) {
  try {
    const response = await fetch(`https://tinyurl.com/api-create.php?url=${encodeURIComponent(longUrl)}`);
    
    if (!response.ok) {
      throw new Error('Falha ao encurtar URL');
    }
    
    const shortUrl = await response.text();
    return shortUrl;
  } catch (error) {
    console.error('Erro ao encurtar URL:', error);
    // Em caso de erro, retorna a URL original
    return longUrl;
  }
} 