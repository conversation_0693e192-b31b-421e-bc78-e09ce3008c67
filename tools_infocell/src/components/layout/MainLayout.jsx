"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Sidebar from "./Sidebar";
import Breadcrumbs from "./Breadcrumbs";
import Header from "@/components/custom/Header";
import { Button } from "@/components/ui/button";
import { Menu, X } from "lucide-react";

export default function MainLayout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const checkAuth = () => {
      // Se está na página de login, não mostrar sidebar
      if (pathname.includes('/auth/login')) {
        setIsAuthenticated(false);
        return;
      }

      // Verifica se existe token no cookie
      const cookies = document.cookie.split(';');
      const authCookie = cookies.find(cookie => 
        cookie.trim().startsWith('authToken=')
      );
      
      setIsAuthenticated(authCookie && authCookie.split('=')[1]);
    };

    checkAuth();
  }, [pathname]);

  // Se está na página de login, renderiza só o conteúdo
  if (pathname.includes('/auth/login')) {
    return children;
  }

  // Se não está autenticado, renderiza só o conteúdo
  if (!isAuthenticated) {
    return children;
  }

  // Se está autenticado, renderiza o layout completo com sidebar
  return (
    <div className="flex min-h-screen bg-background">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
      
      {/* Sidebar */}
      <aside className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-card border-r border-border transform transition-transform duration-200 ease-in-out
        md:relative md:translate-x-0 md:block
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
      `}>
        <Sidebar />
      </aside>
      
      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Header */}
        <Header>
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </Header>
        
        {/* Page Content */}
        <main className="flex-1 p-4 md:p-6">
          <Breadcrumbs />
          {children}
        </main>
      </div>
    </div>
  );
} 