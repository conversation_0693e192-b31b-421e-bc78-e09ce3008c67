"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronRight, Home } from "lucide-react";
import { cn } from "@/lib/utils";

const routeMap = {
  "/dashboard-infocell": "Dashboard",
  "/dashboard-infocell/gerar-documento": "Gerar Documento",
  "/dashboard-infocell/templates": "Templates",
  "/dashboard-infocell/documentos": "Documentos",
  "/dashboard-infocell/resume-generator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Legado)",
  "/dashboard-infocell/auth": "Autenticação",
};

export default function Breadcrumbs() {
  const pathname = usePathname();
  
  // Não mostrar breadcrumbs na página inicial
  if (pathname === "/dashboard-infocell") {
    return null;
  }
  
  const currentPageTitle = routeMap[pathname] || "Página";
  
  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
      <Link 
        href="/dashboard-infocell"
        className="flex items-center hover:text-foreground transition-colors"
      >
        <Home className="h-4 w-4" />
      </Link>
      
      <ChevronRight className="h-4 w-4" />
      
      <span className="text-foreground font-medium">
        {currentPageTitle}
      </span>
    </nav>
  );
} 