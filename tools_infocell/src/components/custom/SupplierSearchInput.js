'use client';

import { useState, useEffect, useRef } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Search, Truck, Check } from "lucide-react";
import infocell<PERSON>pi from "@/lib/infocellApi";

export default function SupplierSearchInput({ onSelect, selectedSupplier, className = "" }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState(null);
  
  const searchTimeoutRef = useRef(null);
  const containerRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (searchTimeoutRef.current) clearTimeout(searchTimeoutRef.current);

    if (searchTerm.trim().length >= 2) {
      searchTimeoutRef.current = setTimeout(async () => {
        await searchSuppliers(searchTerm.trim());
      }, 300);
    } else {
      setSearchResults([]);
      setShowResults(false);
    }

    return () => {
      if (searchTimeoutRef.current) clearTimeout(searchTimeoutRef.current);
    };
  }, [searchTerm]);

  const searchSuppliers = async (term) => {
    try {
      setIsSearching(true);
      setError(null);
      
      const response = await infocellApi.getSuppliers({ nome: term, per_page: 10 });
      setSearchResults(response.data || []);
      setShowResults(true);
    } catch (err) {
      console.error('Erro ao buscar fornecedores:', err);
      setError('Erro ao buscar fornecedores: ' + err.message);
      setSearchResults([]);
      setShowResults(false);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectSupplier = (supplier) => {
    onSelect(supplier);
    setSearchTerm('');
    setShowResults(false);
  };

  const handleClearSelection = () => {
    onSelect(null);
    setSearchTerm('');
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {selectedSupplier ? (
        <div className="flex items-center justify-between p-3 border rounded-md bg-green-50 border-green-200">
          <div className="flex items-center gap-3">
            <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
              <Check className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <p className="font-medium text-green-900">{selectedSupplier.nome}</p>
              {selectedSupplier.cnpj && <p className="text-sm text-green-700">{selectedSupplier.cnpj}</p>}
            </div>
          </div>
          <Button variant="outline" size="sm" onClick={handleClearSelection}>
            Alterar
          </Button>
        </div>
      ) : (
        <>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Buscar Fornecedor por Nome..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onFocus={() => searchResults.length > 0 && setShowResults(true)}
              className="pl-10"
            />
          </div>

          {showResults && (
            <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-80 overflow-y-auto">
              <CardContent className="p-0">
                {isSearching ? (
                  <div className="p-3 text-center text-muted-foreground">Buscando...</div>
                ) : searchResults.length > 0 ? (
                  <div className="divide-y">
                    {searchResults.map((supplier) => (
                      <button
                        key={supplier.id}
                        onClick={() => handleSelectSupplier(supplier)}
                        className="w-full p-3 text-left hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-8 h-8 bg-muted rounded-full">
                            <Truck className="h-4 w-4 text-muted-foreground" />
                          </div>
                          <div>
                            <p className="font-medium">{supplier.nome}</p>
                            {supplier.cnpj && <p className="text-sm text-muted-foreground">{supplier.cnpj}</p>}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                ) : (
                  <div className="p-3 text-center text-muted-foreground">Nenhum fornecedor encontrado</div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}

      {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
    </div>
  );
} 