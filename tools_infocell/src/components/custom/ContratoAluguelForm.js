'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Zap, Calendar, RefreshCw, Loader2 } from "lucide-react";
import infocellApi from "@/lib/infocellApi";

export default function ContratoAluguelForm({ 
  initialLocadorData, 
  initialLocatarioData,
  onSubmit, 
  isGenerating = false 
}) {
  // Função para gerar data e local de assinatura padrão
  const gerarDataLocalAssinatura = () => {
    const hoje = new Date();
    const meses = [
      'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
      'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
    ];
    
    const dia = hoje.getDate();
    const mes = meses[hoje.getMonth()];
    const ano = hoje.getFullYear();
    
    return `Pratânia, ${dia} de ${mes} de ${ano}`;
  };

  const [formData, setFormData] = useState({
    locador: {
      nomeCompleto: '',
      nacionalidade: 'Brasileiro(a)', // Valor padrão
      estadoCivil: '',
      rg: '',
      cpf: ''
    },
    locatario: {
      nomeCompleto: '',
      nacionalidade: 'Brasileiro(a)', // Valor padrão
      estadoCivil: '',
      rg: '',
      cpf: ''
    },
    imovel: {
      endereco: '',
      cidade: 'Pratânia', // Valor padrão
      estado: 'SP' // Valor padrão
    },
    contrato: {
      prazoVigencia: '12 meses', // Valor padrão comum
      valorMensal: '',
      valorMensalExtenso: '',
      diaVencimento: '10', // Valor padrão comum
      dataAssinaturaExtenso: gerarDataLocalAssinatura() // Preenchimento automático
    }
  });

  // Função para carregar dados suplementares e extrair estado civil
  const loadSupplementalData = async (clientId) => {
    try {
      const response = await infocellApi.getLocalClientSupplement(clientId);
      const localData = response?.data;
      
      if (localData?.personalInfo?.civilStatus) {
        return localData.personalInfo.civilStatus;
      }
      
      return null;
    } catch (err) {
      console.warn('Erro ao carregar dados suplementares:', err);
      return null;
    }
  };

  // Função para converter formato do estado civil
  const convertCivilStatusFormat = (civilStatus) => {
    if (!civilStatus) return '';
    
    // Mapear formato do sistema de currículos para formato do contrato (com parênteses e formatação correta)
    const mapping = {
      'Solteiro(a)': 'solteiro(a)',
      'Casado(a)': 'casado(a)',
      'Separado(a)': 'divorciado(a)',
      'União Estável': 'união estável',
      'Divorciado(a)': 'divorciado(a)',
      'Viúvo(a)': 'viúvo(a)'
    };
    
    return mapping[civilStatus] || civilStatus.toLowerCase();
  };

  // Preencher dados iniciais do locador quando disponíveis
  useEffect(() => {
    const loadLocadorData = async () => {
      if (initialLocadorData) {
        let estadoCivil = initialLocadorData.estadoCivil || initialLocadorData.civilStatus || '';
        
        // Se não temos estado civil direto, tentar buscar nos dados suplementares
        if (!estadoCivil && initialLocadorData.id) {
          const supplementalCivilStatus = await loadSupplementalData(initialLocadorData.id);
          if (supplementalCivilStatus) {
            estadoCivil = convertCivilStatusFormat(supplementalCivilStatus);
          }
        }
        
        setFormData(prev => ({
          ...prev,
          locador: {
            ...prev.locador,
            nomeCompleto: initialLocadorData.nome || '',
            nacionalidade: prev.locador.nacionalidade || 'Brasileiro(a)', // Manter valor padrão
            rg: initialLocadorData.rg || '',
            cpf: initialLocadorData.cpf || '',
            estadoCivil: estadoCivil
          }
        }));
      }
    };
    
    loadLocadorData();
  }, [initialLocadorData]);

  // Preencher dados iniciais do locatário quando disponíveis
  useEffect(() => {
    const loadLocatarioData = async () => {
      if (initialLocatarioData) {
        let estadoCivil = initialLocatarioData.estadoCivil || initialLocatarioData.civilStatus || '';
        
        // Se não temos estado civil direto, tentar buscar nos dados suplementares
        if (!estadoCivil && initialLocatarioData.id) {
          const supplementalCivilStatus = await loadSupplementalData(initialLocatarioData.id);
          if (supplementalCivilStatus) {
            estadoCivil = convertCivilStatusFormat(supplementalCivilStatus);
          }
        }
        
        setFormData(prev => ({
          ...prev,
          locatario: {
            ...prev.locatario,
            nomeCompleto: initialLocatarioData.nome || '',
            nacionalidade: prev.locatario.nacionalidade || 'Brasileiro(a)', // Manter nacionalidade padrão
            rg: initialLocatarioData.rg || '',
            cpf: initialLocatarioData.cpf || '',
            estadoCivil: estadoCivil
          }
        }));
      }
    };
    
    loadLocatarioData();
  }, [initialLocatarioData]);

  const handleInputChange = (section, field, value) => {
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  // Converter número para extenso (função simplificada)
  const numeroParaExtenso = (valor) => {
    if (!valor) return '';
    
    const num = parseFloat(valor.replace(/[^\d,]/g, '').replace(',', '.'));
    if (isNaN(num)) return '';
    
    // Esta é uma implementação simplificada. Em produção, 
    // seria recomendado usar uma biblioteca completa para conversão
    const unidades = ['', 'um', 'dois', 'três', 'quatro', 'cinco', 'seis', 'sete', 'oito', 'nove'];
    const dezenas = ['', '', 'vinte', 'trinta', 'quarenta', 'cinquenta', 'sessenta', 'setenta', 'oitenta', 'noventa'];
    const especiais = ['dez', 'onze', 'doze', 'treze', 'quatorze', 'quinze', 'dezesseis', 'dezessete', 'dezoito', 'dezenove'];
    const centenas = ['', 'cem', 'duzentos', 'trezentos', 'quatrocentos', 'quinhentos', 'seiscentos', 'setecentos', 'oitocentos', 'novecentos'];
    
    if (num === 0) return 'zero reais';
    if (num === 1) return 'um real';
    
    const inteiro = Math.floor(num);
    
    if (inteiro < 10) {
      return `${unidades[inteiro]} reais`;
    } else if (inteiro < 100) {
      const dez = Math.floor(inteiro / 10);
      const uni = inteiro % 10;
      if (inteiro >= 10 && inteiro <= 19) {
        return `${especiais[inteiro - 10]} reais`;
      }
      return `${dezenas[dez]}${uni > 0 ? ` e ${unidades[uni]}` : ''} reais`;
    } else if (inteiro < 1000) {
      const cen = Math.floor(inteiro / 100);
      const resto = inteiro % 100;
      let resultado = centenas[cen];
      if (resto > 0) {
        if (resto < 10) {
          resultado += ` e ${unidades[resto]}`;
        } else if (resto < 20) {
          resultado += ` e ${especiais[resto - 10]}`;
        } else {
          const dez = Math.floor(resto / 10);
          const uni = resto % 10;
          resultado += ` e ${dezenas[dez]}${uni > 0 ? ` e ${unidades[uni]}` : ''}`;
        }
      }
      return `${resultado} reais`;
    }
    
    // Para valores maiores, retorna uma aproximação
    if (inteiro >= 1000 && inteiro < 2000) {
      return `mil${inteiro > 1000 ? ` e ${numeroParaExtenso((inteiro - 1000).toString())}`.replace(' reais', '') : ''} reais`;
    }
    
    return `${Math.floor(inteiro / 1000)} mil reais (aproximado)`;
  };

  const handleValorChange = (value) => {
    handleInputChange('contrato', 'valorMensal', value);
    const extenso = numeroParaExtenso(value);
    handleInputChange('contrato', 'valorMensalExtenso', extenso);
  };

  const atualizarDataAssinatura = () => {
    const novaData = gerarDataLocalAssinatura();
    handleInputChange('contrato', 'dataAssinaturaExtenso', novaData);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Informações Automáticas */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
        <div className="flex items-start gap-3">
          <Zap className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-blue-900 mb-1">Formulário Inteligente</h3>
            <p className="text-sm text-blue-700 mb-2">
              Este formulário foi otimizado para agilizar o preenchimento. Os campos abaixo são preenchidos automaticamente com valores comuns:
            </p>
                        <ul className="text-xs text-blue-600 space-y-1">
              <li>• <strong>Estado Civil:</strong> Carregado automaticamente se disponível</li>
              <li>• <strong>Nacionalidade:</strong> {`Brasileiro(a)`} (mais comum)</li>
              <li>• <strong>Cidade/Estado:</strong> Pratânia, SP (localização padrão)</li>
              <li>• <strong>Prazo:</strong> 12 meses (duração padrão)</li>
              <li>• <strong>Vencimento:</strong> Dia 10 (comum para pagamentos)</li>
              <li>• <strong>Data de Assinatura:</strong> Data e local atuais</li>
            </ul>
                         <p className="text-xs text-blue-600 mt-2">
               💡 Todos os valores podem ser editados conforme necessário.
             </p>
             <p className="text-xs text-blue-600 mt-1">
               📝 <strong>Nota:</strong> As testemunhas serão preenchidas à mão no documento impresso.
             </p>
          </div>
        </div>
      </div>

      {/* Dados do Locador */}
      <Card>
        <CardHeader>
          <CardTitle>Dados do Locador</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="locador-nome">Nome Completo *</Label>
              <Input
                id="locador-nome"
                value={formData.locador.nomeCompleto}
                onChange={(e) => handleInputChange('locador', 'nomeCompleto', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="locador-nacionalidade">Nacionalidade *</Label>
              <Input
                id="locador-nacionalidade"
                value={formData.locador.nacionalidade}
                onChange={(e) => handleInputChange('locador', 'nacionalidade', e.target.value)}
                placeholder="Brasileiro(a)"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                💡 Preenchido automaticamente com {`Brasileiro(a)`}
              </p>
            </div>
            <div>
              <Label htmlFor="locador-estado-civil">Estado Civil *</Label>
              <select
                id="locador-estado-civil"
                value={formData.locador.estadoCivil}
                onChange={(e) => handleInputChange('locador', 'estadoCivil', e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                required
              >
                <option value="">Selecione...</option>
                <option value="solteiro(a)">Solteiro(a)</option>
                <option value="casado(a)">Casado(a)</option>
                <option value="divorciado(a)">Divorciado(a)</option>
                <option value="viúvo(a)">Viúvo(a)</option>
                <option value="união estável">União Estável</option>
              </select>
            </div>
            <div>
              <Label htmlFor="locador-rg">RG *</Label>
              <Input
                id="locador-rg"
                value={formData.locador.rg}
                onChange={(e) => handleInputChange('locador', 'rg', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="locador-cpf">CPF *</Label>
              <Input
                id="locador-cpf"
                value={formData.locador.cpf}
                onChange={(e) => handleInputChange('locador', 'cpf', e.target.value)}
                placeholder="000.000.000-00"
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dados do Locatário */}
      <Card>
        <CardHeader>
          <CardTitle>Dados do Locatário</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="locatario-nome">Nome Completo *</Label>
              <Input
                id="locatario-nome"
                value={formData.locatario.nomeCompleto}
                onChange={(e) => handleInputChange('locatario', 'nomeCompleto', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="locatario-nacionalidade">Nacionalidade *</Label>
              <Input
                id="locatario-nacionalidade"
                value={formData.locatario.nacionalidade}
                onChange={(e) => handleInputChange('locatario', 'nacionalidade', e.target.value)}
                placeholder="Brasileiro(a)"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                💡 Preenchido automaticamente com {`Brasileiro(a)`}
              </p>
            </div>
            <div>
              <Label htmlFor="locatario-estado-civil">Estado Civil *</Label>
              <select
                id="locatario-estado-civil"
                value={formData.locatario.estadoCivil}
                onChange={(e) => handleInputChange('locatario', 'estadoCivil', e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                required
              >
                <option value="">Selecione...</option>
                <option value="solteiro(a)">Solteiro(a)</option>
                <option value="casado(a)">Casado(a)</option>
                <option value="divorciado(a)">Divorciado(a)</option>
                <option value="viúvo(a)">Viúvo(a)</option>
                <option value="união estável">União Estável</option>
              </select>
            </div>
            <div>
              <Label htmlFor="locatario-rg">RG *</Label>
              <Input
                id="locatario-rg"
                value={formData.locatario.rg}
                onChange={(e) => handleInputChange('locatario', 'rg', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="locatario-cpf">CPF *</Label>
              <Input
                id="locatario-cpf"
                value={formData.locatario.cpf}
                onChange={(e) => handleInputChange('locatario', 'cpf', e.target.value)}
                placeholder="000.000.000-00"
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dados do Imóvel */}
      <Card>
        <CardHeader>
          <CardTitle>Dados do Imóvel</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="imovel-endereco">Endereço Completo *</Label>
            <Input
              id="imovel-endereco"
              value={formData.imovel.endereco}
              onChange={(e) => handleInputChange('imovel', 'endereco', e.target.value)}
              placeholder="Rua, número, bairro, complemento"
              required
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="imovel-cidade">Cidade *</Label>
              <Input
                id="imovel-cidade"
                value={formData.imovel.cidade}
                onChange={(e) => handleInputChange('imovel', 'cidade', e.target.value)}
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                💡 Padrão: Pratânia
              </p>
            </div>
            <div>
              <Label htmlFor="imovel-estado">Estado *</Label>
              <Input
                id="imovel-estado"
                value={formData.imovel.estado}
                onChange={(e) => handleInputChange('imovel', 'estado', e.target.value)}
                placeholder="SP"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                💡 Padrão: SP
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Termos do Contrato */}
      <Card>
        <CardHeader>
          <CardTitle>Termos do Contrato</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="contrato-prazo">Prazo de Vigência *</Label>
              <Input
                id="contrato-prazo"
                value={formData.contrato.prazoVigencia}
                onChange={(e) => handleInputChange('contrato', 'prazoVigencia', e.target.value)}
                placeholder="12 meses"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                💡 Padrão: 12 meses (mais comum)
              </p>
            </div>
            <div>
              <Label htmlFor="contrato-dia-vencimento">Dia do Vencimento *</Label>
              <Input
                id="contrato-dia-vencimento"
                type="number"
                min="1"
                max="31"
                value={formData.contrato.diaVencimento}
                onChange={(e) => handleInputChange('contrato', 'diaVencimento', e.target.value)}
                placeholder="10"
                required
              />
              <p className="text-xs text-muted-foreground mt-1">
                💡 Padrão: dia 10 (comum para aluguel)
              </p>
            </div>
          </div>
          <div>
            <Label htmlFor="contrato-valor">Valor Mensal* (Inserir apenas números)</Label>
            <Input
              type="number"
              id="contrato-valor"
              value={formData.contrato.valorMensal}
              onChange={(e) => handleValorChange(e.target.value)}
              placeholder="1.500,00"
              required
            />
            {formData.contrato.valorMensalExtenso && (
              <p className="text-sm text-muted-foreground mt-1">
                Por extenso: {formData.contrato.valorMensalExtenso}
              </p>
            )}
          </div>
          <div>
            <div className="flex items-center justify-between mb-2">
              <Label htmlFor="contrato-data-assinatura">Data e Local de Assinatura *</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={atualizarDataAssinatura}
                className="text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Atualizar para hoje
              </Button>
            </div>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="contrato-data-assinatura"
                value={formData.contrato.dataAssinaturaExtenso}
                onChange={(e) => handleInputChange('contrato', 'dataAssinaturaExtenso', e.target.value)}
                placeholder="Pratânia, 04 de junho de 2025"
                className="pl-10"
                required
              />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              💡 Dica: Este campo é preenchido automaticamente com a data atual e localização padrão
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Botão de Submit */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isGenerating} className="w-full md:w-auto">
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Gerando Contrato...
            </>
          ) : (
            <>
              <Zap className="mr-2 h-4 w-4" />
              Gerar Contrato de Aluguel
            </>
          )}
        </Button>
      </div>
    </form>
  );
} 