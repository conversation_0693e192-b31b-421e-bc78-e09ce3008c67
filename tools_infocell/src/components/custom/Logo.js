import Image from 'next/image';

const Logo = ({ width = 150, height = 100, className }) => {
  // A URL direta para a imagem do Imgur
  // Para imagens do Imgur, o formato correto é https://i.imgur.com/CODIGO.extensão
  // Por exemplo: https://i.imgur.com/mnp8Icm.png
  
  // Certifique-se de que o domínio i.imgur.com está configurado em next.config.mjs
  // na seção images.remotePatterns
  const logoUrl = 'https://i.imgur.com/mnp8Icm.png'; // Substitua pela URL correta com extensão
  
  return (
    <div className={`relative flex items-center justify-center ${className || ''}`} style={{ width, height }}>
      <Image
        src={logoUrl}
        alt="Infocell Logo"
        width={width}
        height={height}
        priority
        className="relative object-contain w-auto h-full"
      />
    </div>
  );
};

export default Logo;