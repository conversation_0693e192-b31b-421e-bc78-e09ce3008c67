"use client";

import Logo from './Logo';
import { ThemeToggle } from './ThemeToggle';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { LogOut } from 'lucide-react';
import { APP_PREFIX } from '@/lib/path-utils';

const Header = ({ children }) => {
  const router = useRouter();

  const handleLogout = () => {
    // Remover o cookie de autenticação
    document.cookie = 'authToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
    // Opcional: Limpar localStorage se também for usado para token
    localStorage.removeItem('authToken');

    // Redirecionar para a página de login usando APP_PREFIX para compatibilidade entre ambientes
    router.push(`${APP_PREFIX}/auth/login`);
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur-sm header-gradient">
      <div className="flex h-16 items-center justify-between px-6">
        {/* Menu mobile */}
        <div className="flex items-center md:w-64">
          {children}
        </div>
        
        {/* Logo centralizada */}
        <div className="flex-1 flex justify-center">
          <Logo width={160} height={50} className="py-1" />
        </div>
        
        {/* Controles direita */}
        <div className="flex items-center space-x-3 md:w-64 justify-end">
          <ThemeToggle />
          <Button
            variant="outline"
            size="icon"
            onClick={handleLogout}
            type="button"
            aria-label="Sair do sistema"
            className="border-primary/20 hover:border-primary/40 hover:bg-primary/5"
          >
            <LogOut className="h-[1.2rem] w-[1.2rem] text-primary" />
            <span className="sr-only">Sair</span>
          </Button>
        </div>
      </div>
    </header>
  );
};

export default Header;