'use client';

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Zap, Calendar, Loader2 } from "lucide-react";
import infocellApi from "@/lib/infocellApi";

export default function ProcuracaoForm({ 
  initialOutorganteData, 
  initialOutorgadoData, 
  onSubmit, 
  isGenerating = false 
}) {
  // Função para gerar data e local de assinatura padrão
  const gerarDataLocalAssinatura = () => {
    const hoje = new Date();
    const meses = [
      'janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho',
      'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'
    ];
    
    const dia = hoje.getDate();
    const mes = meses[hoje.getMonth()];
    const ano = hoje.getFullYear();
    
    return `Pratânia, ${dia} de ${mes} de ${ano}`;
  };

  // Função para gerar data de validade padrão (1 ano a partir de hoje)
  const gerarDataValidadePadrao = () => {
    const hoje = new Date();
    const proximoAno = new Date(hoje.getFullYear() + 1, hoje.getMonth(), hoje.getDate());
    return proximoAno.toISOString().split('T')[0];
  };

  const [formData, setFormData] = useState({
    outorgante: {
      nomeCompleto: '',
      cpf: '',
      rg: '',
      orgaoEmissor: 'SSP-SP', // Valor padrão comum
      endereco: '',
      municipio: 'Pratânia', // Valor padrão
      estado: 'SP', // Valor padrão
      cep: '',
      telefone: ''
    },
    outorgado: {
      nomeCompleto: '',
      cpf: '',
      rg: '',
      orgaoEmissor: 'SSP-SP', // Valor padrão comum
      endereco: '',
      municipio: 'Pratânia', // Valor padrão
      estado: 'SP', // Valor padrão
      cep: ''
    },
    procuracao: {
      representarPerante: '',
      poderes: '',
      dataValidade: gerarDataValidadePadrao(),
      localEData: gerarDataLocalAssinatura()
    }
  });

  // Função para carregar dados suplementares e extrair informações
  const loadSupplementalData = async (clientId) => {
    try {
      const response = await infocellApi.getLocalClientSupplement(clientId);
      const localData = response?.data;
      
      return localData?.personalInfo || null;
    } catch (err) {
      console.warn('Erro ao carregar dados suplementares:', err);
      return null;
    }
  };

  // Função para aplicar máscara de CPF
  const applyCpfMask = (value) => {
    const numbers = value.replace(/\D/g, '');
    return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  };

  // Função para aplicar máscara de telefone
  const applyPhoneMask = (value) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 10) {
      return numbers.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    } else {
      return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    }
  };

  // Função para aplicar máscara de CEP
  const applyCepMask = (value) => {
    const numbers = value.replace(/\D/g, '');
    return numbers.replace(/(\d{5})(\d{3})/, '$1-$2');
  };

  // Preencher dados iniciais do outorgante quando disponíveis
  useEffect(() => {
    const loadOutorganteData = async () => {
      if (initialOutorganteData) {
        const address = initialOutorganteData.enderecos?.[0]?.endereco;
        
        // Tentar buscar dados suplementares se disponível
        let supplementalData = null;
        if (initialOutorganteData.id) {
          supplementalData = await loadSupplementalData(initialOutorganteData.id);
        }
        
        // Construir dados finais com fallbacks
        const erpAddressStr = address ? `${address.logradouro || ''} ${address.numero || ''} ${address.bairro || ''}`.trim() : '';
        const erpCep = address?.cep || '';
        const erpPhone = initialOutorganteData.telefone || initialOutorganteData.celular || '';
        
        const finalAddress = supplementalData?.address || erpAddressStr || '';
        const finalCep = supplementalData?.cep || erpCep || '';
        const finalPhone = supplementalData?.phone1 || supplementalData?.phone2 || erpPhone || '';
        
        setFormData(prev => ({
          ...prev,
          outorgante: {
            ...prev.outorgante,
            nomeCompleto: initialOutorganteData.nome || '',
            cpf: initialOutorganteData.cpf || '',
            rg: initialOutorganteData.rg || '',
            endereco: finalAddress,
            municipio: address?.nome_cidade || prev.outorgante.municipio || 'Pratânia',
            estado: address?.estado || prev.outorgante.estado || 'SP',
            cep: finalCep,
            telefone: finalPhone,
            orgaoEmissor: prev.outorgante.orgaoEmissor || 'SSP-SP'
          }
        }));
      }
    };
    
    loadOutorganteData();
  }, [initialOutorganteData]);

  // Preencher dados iniciais do outorgado quando disponíveis
  useEffect(() => {
    const loadOutorgadoData = async () => {
      if (initialOutorgadoData) {
        const address = initialOutorgadoData.enderecos?.[0]?.endereco;
        
        // Tentar buscar dados suplementares se disponível
        let supplementalData = null;
        if (initialOutorgadoData.id) {
          supplementalData = await loadSupplementalData(initialOutorgadoData.id);
        }
        
        // Construir dados finais com fallbacks
        const erpAddressStr = address ? `${address.logradouro || ''} ${address.numero || ''} ${address.bairro || ''}`.trim() : '';
        const erpCep = address?.cep || '';
        const erpPhone = initialOutorgadoData.telefone || initialOutorgadoData.celular || '';
        
        const finalAddress = supplementalData?.address || erpAddressStr || '';
        const finalCep = supplementalData?.cep || erpCep || '';
        const finalPhone = supplementalData?.phone1 || supplementalData?.phone2 || erpPhone || '';
        
        setFormData(prev => ({
          ...prev,
          outorgado: {
            ...prev.outorgado,
            nomeCompleto: initialOutorgadoData.nome || '',
            cpf: initialOutorgadoData.cpf || '',
            rg: initialOutorgadoData.rg || '',
            endereco: finalAddress,
            municipio: address?.nome_cidade || prev.outorgado.municipio || 'Pratânia',
            estado: address?.estado || prev.outorgado.estado || 'SP',
            cep: finalCep,
            telefone: finalPhone,
            orgaoEmissor: prev.outorgado.orgaoEmissor || 'SSP-SP'
          }
        }));
      }
    };
    
    loadOutorgadoData();
  }, [initialOutorgadoData]);

  const handleInputChange = (section, field, value) => {
    // Aplicar máscaras conforme o campo
    let maskedValue = value;
    
    if (field === 'cpf') {
      maskedValue = applyCpfMask(value);
    } else if (field === 'telefone') {
      maskedValue = applyPhoneMask(value);
    } else if (field === 'cep') {
      maskedValue = applyCepMask(value);
    }
    
    setFormData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: maskedValue
      }
    }));
  };

  // Função para atualizar data e local de assinatura
  const atualizarDataLocal = () => {
    const novaData = gerarDataLocalAssinatura();
    handleInputChange('procuracao', 'localEData', novaData);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Mapear dados do formulário para o formato esperado pelo backend
    const mappedData = {
      outorgante: {
        nomeCompleto: formData.outorgante.nomeCompleto || '',
        cpf: formData.outorgante.cpf || '',
        rg: formData.outorgante.rg || '',
        orgaoEmissor: formData.outorgante.orgaoEmissor || '',
        endereco: formData.outorgante.endereco || '',
        municipio: formData.outorgante.municipio || '',
        estado: formData.outorgante.estado || '',
        cep: formData.outorgante.cep || '',
        telefone: formData.outorgante.telefone || ''
      },
      outorgado: {
        nomeCompleto: formData.outorgado.nomeCompleto,
        cpf: formData.outorgado.cpf,
        rg: formData.outorgado.rg,
        orgaoEmissor: formData.outorgado.orgaoEmissor,
        enderecoCompleto: `${formData.outorgado.endereco}, ${formData.outorgado.municipio} - ${formData.outorgado.estado}, CEP: ${formData.outorgado.cep}`.replace(/^,\s*|,\s*$/, '').trim(),
        municipio: formData.outorgado.municipio,
        estado: formData.outorgado.estado,
        cep: formData.outorgado.cep,
        telefonePrincipal: formData.outorgado.telefone || '',
        celularPrincipal: '',
        emailPrincipal: ''
      },
      procuracao: formData.procuracao
    };
    
    onSubmit(mappedData);
  };

  const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    return date.toISOString().split('T')[0];
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Dados do Outorgante */}
      <Card>
        <CardHeader>
          <CardTitle>Dados do Outorgante</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="outorgante-nome">Nome Completo *</Label>
              <Input
                id="outorgante-nome"
                value={formData.outorgante.nomeCompleto}
                onChange={(e) => handleInputChange('outorgante', 'nomeCompleto', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgante-cpf">CPF *</Label>
              <Input
                id="outorgante-cpf"
                value={formData.outorgante.cpf}
                onChange={(e) => handleInputChange('outorgante', 'cpf', e.target.value)}
                placeholder="000.000.000-00"
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgante-rg">RG *</Label>
              <Input
                id="outorgante-rg"
                value={formData.outorgante.rg}
                onChange={(e) => handleInputChange('outorgante', 'rg', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgante-orgao">Órgão Emissor *</Label>
              <Input
                id="outorgante-orgao"
                value={formData.outorgante.orgaoEmissor}
                onChange={(e) => handleInputChange('outorgante', 'orgaoEmissor', e.target.value)}
                placeholder="SSP-SP"
                required
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="outorgante-endereco">Endereço Completo *</Label>
              <Input
                id="outorgante-endereco"
                value={formData.outorgante.endereco}
                onChange={(e) => handleInputChange('outorgante', 'endereco', e.target.value)}
                placeholder="Rua, número, bairro"
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgante-municipio">Município *</Label>
              <Input
                id="outorgante-municipio"
                value={formData.outorgante.municipio}
                onChange={(e) => handleInputChange('outorgante', 'municipio', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgante-estado">Estado *</Label>
              <Input
                id="outorgante-estado"
                value={formData.outorgante.estado}
                onChange={(e) => handleInputChange('outorgante', 'estado', e.target.value)}
                placeholder="SP"
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgante-cep">CEP *</Label>
              <Input
                id="outorgante-cep"
                value={formData.outorgante.cep}
                onChange={(e) => handleInputChange('outorgante', 'cep', e.target.value)}
                placeholder="00000-000"
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgante-telefone">Telefone *</Label>
              <Input
                id="outorgante-telefone"
                value={formData.outorgante.telefone}
                onChange={(e) => handleInputChange('outorgante', 'telefone', e.target.value)}
                placeholder="(14) 99999-9999"
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dados do Outorgado */}
      <Card>
        <CardHeader>
          <CardTitle>Dados do Outorgado</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="outorgado-nome">Nome Completo *</Label>
              <Input
                id="outorgado-nome"
                value={formData.outorgado.nomeCompleto}
                onChange={(e) => handleInputChange('outorgado', 'nomeCompleto', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgado-cpf">CPF *</Label>
              <Input
                id="outorgado-cpf"
                value={formData.outorgado.cpf}
                onChange={(e) => handleInputChange('outorgado', 'cpf', e.target.value)}
                placeholder="000.000.000-00"
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgado-rg">RG *</Label>
              <Input
                id="outorgado-rg"
                value={formData.outorgado.rg}
                onChange={(e) => handleInputChange('outorgado', 'rg', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgado-orgao">Órgão Emissor *</Label>
              <Input
                id="outorgado-orgao"
                value={formData.outorgado.orgaoEmissor}
                onChange={(e) => handleInputChange('outorgado', 'orgaoEmissor', e.target.value)}
                placeholder="SSP-SP"
                required
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="outorgado-endereco">Endereço Completo *</Label>
              <Input
                id="outorgado-endereco"
                value={formData.outorgado.endereco}
                onChange={(e) => handleInputChange('outorgado', 'endereco', e.target.value)}
                placeholder="Rua, número, bairro"
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgado-municipio">Município *</Label>
              <Input
                id="outorgado-municipio"
                value={formData.outorgado.municipio}
                onChange={(e) => handleInputChange('outorgado', 'municipio', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgado-estado">Estado *</Label>
              <Input
                id="outorgado-estado"
                value={formData.outorgado.estado}
                onChange={(e) => handleInputChange('outorgado', 'estado', e.target.value)}
                placeholder="SP"
                required
              />
            </div>
            <div>
              <Label htmlFor="outorgado-cep">CEP *</Label>
              <Input
                id="outorgado-cep"
                value={formData.outorgado.cep}
                onChange={(e) => handleInputChange('outorgado', 'cep', e.target.value)}
                placeholder="00000-000"
                required
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detalhes da Procuração */}
      <Card>
        <CardHeader>
          <CardTitle>Detalhes da Procuração</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="representar-perante">Representar Perante *</Label>
            <Input
              id="representar-perante"
              value={formData.procuracao.representarPerante}
              onChange={(e) => handleInputChange('procuracao', 'representarPerante', e.target.value)}
              placeholder="Ex: Receita Federal, INSS, Bancos em geral"
              required
            />
          </div>
          <div>
            <Label htmlFor="poderes">Poderes Outorgados *</Label>
            <Textarea
              id="poderes"
              value={formData.procuracao.poderes}
              onChange={(e) => handleInputChange('procuracao', 'poderes', e.target.value)}
              placeholder="Descreva os poderes específicos que estão sendo outorgados..."
              rows={4}
              required
            />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="data-validade">Data de Validade *</Label>
              <Input
                id="data-validade"
                type="date"
                value={formData.procuracao.dataValidade}
                onChange={(e) => handleInputChange('procuracao', 'dataValidade', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="local-data">Local e Data de Assinatura *</Label>
              <div className="flex gap-2">
                <Input
                  id="local-data"
                  value={formData.procuracao.localEData}
                  onChange={(e) => handleInputChange('procuracao', 'localEData', e.target.value)}
                  placeholder="Pratânia, 04 de junho de 2025"
                  required
                  className="flex-1"
                />
                <Button
                  type="button"
                  onClick={atualizarDataLocal}
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                >
                  <Calendar className="h-4 w-4" />
                  Hoje  
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Botão de Submit */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isGenerating} className="w-full md:w-auto">
          {isGenerating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Gerando Procuração...
            </>
          ) : (
            <>
              <Zap className="mr-2 h-4 w-4" />
              Gerar Procuração
            </>
          )}
        </Button>
      </div>
    </form>
  );
} 