"use client";

import React, { useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardHeader, CardTitle } from "@/components/ui/card";
import { Check<PERSON>ircle, ArrowLeft, Copy, Printer } from 'lucide-react';
import { toast } from "sonner";
import { formatBsonDate } from '@/lib/utils';

const RechargeReceipt = ({ saleReceipt, selectedPackage, selectedClient, selectedEmployee, onNewSale }) => {
    const receiptRef = useRef(null);

    const generateReceiptText = React.useCallback(() => {
        if (!saleReceipt || !saleReceipt.sale || !selectedPackage || !selectedClient || !selectedEmployee) return '';
        const { sale, codes } = saleReceipt;

        const validityDays = codes && codes.length > 0 && codes[0].validity_days
            ? codes[0].validity_days
            : selectedPackage?.validity_days || 30;

        let text = `INFOCELL - RECIBO\n`;
        text += `================================\n\n`;
        text += `${formatBsonDate(sale.sale_timestamp, { day: '2-digit', month: '2-digit', year: 'numeric' })}\n`;
        text += `${selectedClient?.nome || 'Cliente'}\n\n`;
        text += `RECARGAS\n`;

        const tvCodes = codes.filter(c => c.package_type === 'tv');
        const filmesCodes = codes.filter(c => c.package_type === 'filmes');
        const isCombo = sale.package_type === 'combo';

        if (isCombo) {
            for (let i = 0; i < sale.quantity; i++) {
                text += `\nCombo ${i + 1}:\n`;
                text += `  TV: ${tvCodes[i]?.code.replace(/(.{4})/g, '$1 ').trim() || 'N/A'}\n`;
                text += `  Filmes: ${filmesCodes[i]?.code.replace(/(.{4})/g, '$1 ').trim() || 'N/A'}\n`;
            }
        } else {
            text += `${sale.package_type.toUpperCase()}:\n`;
            codes.forEach(c => { text += `${c.code.replace(/(.{4})/g, '$1 ').trim()}\n`; });
        }

        text += `\nValidade: ${validityDays} dias após recarregar na tv\n\n`;
        text += `R$ ${sale.total_sale_price.toFixed(2)}\n`;
        text += `${sale.payment_method}\n\n`;
        text += `Vendedor: ${selectedEmployee.nome}\n`;

        return text;
    }, [saleReceipt, selectedPackage, selectedClient, selectedEmployee]);

    const handleCopy = () => {
        navigator.clipboard.writeText(generateReceiptText()).then(() => {
            toast.success("Recibo copiado para a área de transferência.");
        }, () => {
            toast.error("Falha ao copiar o recibo.");
        });
    };

    const handlePrint = () => {
        if (!receiptRef.current) {
            toast.error("Erro: Não foi possível encontrar o conteúdo do recibo para imprimir.");
            return;
        }

        const contentToPrint = receiptRef.current.innerHTML;

        // Estilos essenciais para a impressão. Copie os estilos do seu recibo para cá.
        const styles = `
            body { font-family: sans-serif; margin: 20mm; }
            .text-lg { font-size: 1.125rem; }
            .font-bold { font-weight: 700; }
            .mb-2 { margin-bottom: 0.5rem; }
            .mb-4 { margin-bottom: 1rem; }
            .mb-8 { margin-bottom: 2rem; }
            .text-center { text-align: center; }
            .text-sm { font-size: 0.875rem; }
            .space-y-3 > * + * { margin-top: 0.75rem; }
            .font-mono { font-family: monospace; }
            .combo-group { page-break-inside: avoid; }
            .text-base { font-size: 1rem; }
            .font-semibold { font-weight: 600; }
            .text-xs { font-size: 0.75rem; }
            .text-gray-600 { color: #4b5563; }
            .p-2 { padding: 0.5rem; }
            .border { border: 1px solid #d1d5db; }
            .rounded { border-radius: 0.25rem; }
            .bg-gray-50 { background-color: #f9fafb; }
            .space-y-1 > * + * { margin-top: 0.25rem; }
            .absolute { position: absolute; }
            .bottom-8 { bottom: 2rem; }
            .left-8 { left: 2rem; }
        `;

        const iframe = document.createElement('iframe');
        iframe.style.position = 'absolute';
        iframe.style.width = '0';
        iframe.style.height = '0';
        iframe.style.border = '0';

        document.body.appendChild(iframe);

        const iframeDoc = iframe.contentWindow.document;
        iframeDoc.open();
        iframeDoc.write(`<html><head><title>Recibo - Infocell</title><style>${styles}</style></head><body>${contentToPrint}</body></html>`);
        iframeDoc.close();

        const onIframeLoad = () => {
            try {
                iframe.contentWindow.focus();
                iframe.contentWindow.print();
            } catch (e) {
                toast.error("Erro ao tentar abrir o diálogo de impressão.");
                console.error("Erro na impressão do iframe:", e);
            } finally {
                // Remover o iframe após um tempo para garantir que a impressão foi enviada
                setTimeout(() => {
                    document.body.removeChild(iframe);
                }, 1000);
            }
        };

        // Verificar se o iframe carregou para chamar a impressão
        if (iframe.contentWindow.document.readyState === 'complete') {
            onIframeLoad();
        } else {
            iframe.onload = onIframeLoad;
        }
    };

    if (!saleReceipt || !saleReceipt.sale) {
        return <p>Carregando recibo...</p>;
    }

    const { sale, codes } = saleReceipt;

    const validityDays = codes && codes.length > 0 && codes[0].validity_days
        ? codes[0].validity_days
        : selectedPackage?.validity_days || 30;

    return (
        <div className="flex flex-col items-center w-full">
            <style jsx global>{`
                @media print {
                    body * {
                        visibility: hidden;
                    }
                    #printable-receipt, #printable-receipt * {
                        visibility: visible;
                    }
                    #printable-receipt {
                        position: absolute;
                        left: 0;
                        top: 0;
                        margin: 0;
                        padding: 0;
                        width: 100%;
                    }
                }
                @page { 
                    size: a4;
                    margin: 20mm;
                }
            `}</style>
            
            <Card className="w-full max-w-4xl no-print mb-4">
                 <CardHeader className="text-center">
                    <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                    <CardTitle className="mt-4">Venda Realizada com Sucesso!</CardTitle>
                </CardHeader>
            </Card>

            {/* A4 Preview - hidden on main screen, used for PDF generation and print */}
            <div className="absolute top-0 opacity-0 h-0 overflow-hidden -z-10">
                <div id="printable-receipt" ref={receiptRef} className="bg-white text-black p-8 shadow-lg w-[210mm] h-[297mm] relative">
                    {/* Watermark */}
                    <div className="absolute inset-0 flex items-center justify-center opacity-10 pointer-events-none">
                        <div className="text-6xl font-bold text-gray-400 transform -rotate-45">
                            INFOCELL
                        </div>
                    </div>

                    {/* Header - Data e Nome */}
                    <div className="text-center mb-8">
                        <div className="text-lg font-bold mb-2">
                            {formatBsonDate(sale.sale_timestamp, { day: '2-digit', month: '2-digit', year: 'numeric' })}
                        </div>
                        <div className="text-base">
                            {selectedClient?.nome || 'Cliente'}
                        </div>
                    </div>

                    {/* Códigos de Recarga - Centralizados */}
                    <div className="text-center mb-8">
                        <div className="text-lg font-bold mb-4">recargas</div>
                        {(() => {
                            // Lógica para renderizar os códigos no recibo
                            const tvCodes = codes.filter(c => c.package_type === 'tv');
                            const filmesCodes = codes.filter(c => c.package_type === 'filmes');
                            const isCombo = sale.package_type === 'combo';

                            return (
                                <div className="space-y-3">
                                    {isCombo ? (
                                        // Renderizar combos agrupados
                                        Array.from({ length: sale.quantity }).map((_, index) => (
                                            <div key={index} className="combo-group mb-4 p-2 border rounded bg-gray-50">
                                                <div className="text-sm font-semibold mb-2">Combo {index + 1}</div>
                                                <div className="space-y-1">
                                                    <div className="font-mono text-sm">
                                                        <span className="text-xs text-gray-600">tv: </span>
                                                        {tvCodes[index]?.code.replace(/(.{4})/g, '$1 ').trim() || 'N/A'}
                                                    </div>
                                                    <div className="font-mono text-sm">
                                                        <span className="text-xs text-gray-600">filmes: </span>
                                                        {filmesCodes[index]?.code.replace(/(.{4})/g, '$1 ').trim() || 'N/A'}
                                                    </div>
                                                </div>
                                            </div>
                                        ))
                                    ) : (
                                        // Lógica original para pacotes simples
                                        <div>
                                            <div className="text-sm font-semibold mb-2">{sale.package_type}</div>
                                            {codes.map((code, index) => (
                                                <div key={code.id?.$oid || code._id?.$oid || index} className="font-mono text-sm">
                                                    {code.code.replace(/(.{4})/g, '$1 ').trim()}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            );
                        })()}
                    </div>

                    {/* Validade */}
                    <div className="text-center mb-8">
                        <div className="text-sm">
                            validade {validityDays} dias após recarregar na tv
                        </div>
                    </div>

                    {/* Valor e Forma de Pagamento */}
                    <div className="text-center mb-8">
                        <div className="text-lg font-bold mb-2">
                            R$ {sale.total_sale_price.toFixed(2)}
                        </div>
                        <div className="text-sm">
                            {sale.payment_method}
                        </div>
                    </div>

                    {/* Vendedor - No final */}
                    <div className="absolute bottom-8 left-8">
                        <div className="text-sm">
                            vendedor: {selectedEmployee?.nome || 'N/A'}
                        </div>
                    </div>
                </div>
            </div>

            {/* Ações */}
            <div className="flex flex-col gap-2 mt-4 w-full max-w-md no-print">
                <Button onClick={handleCopy} variant="outline" className="w-full">
                    <Copy className="mr-2 h-4 w-4"/> Copiar Texto
                </Button>
                <Button onClick={handlePrint} variant="outline" className="w-full">
                    <Printer className="mr-2 h-4 w-4"/> Imprimir
                </Button>

            </div>
            <Button onClick={onNewSale} className="w-full mt-2 max-w-md no-print">
                <ArrowLeft className="mr-2 h-4 w-4"/> Nova Venda
            </Button>
        </div>
    );
};

export default RechargeReceipt; 

