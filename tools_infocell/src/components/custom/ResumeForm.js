'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Trash2, Loader2 } from "lucide-react";
import { APP_PREFIX } from "@/lib/path-utils";
import infocellApi from "@/lib/infocellApi";

import PersonalInfoSection from "../resume/PersonalInfoSection";
import ObjectiveSection from "../resume/ObjectiveSection";
import EducationSection from "../resume/EducationSection";
import ExperienceSection from "../resume/ExperienceSection";
import QualificationsSection from "../resume/QualificationsSection";
import AdditionalInfoSection from "../resume/AdditionalInfoSection";
import ResumeResult from "../resume/ResumeResult";
import ErrorMessage from "../resume/ErrorMessage";

const initialFormData = {
  personalInfo: {
    fullName: '',
    civilStatus: '',
    hasChildren: '',
    address: '',
    state: '',
    city: '',
    phone1: '',
    phone2: '',
    email: '',
    levelOfEducation: '',
    rg: '',
    cpf: '',
    dateOfBirth: '',
    nacionalidade: '', // Ensure this field is included
    cnh: '', // Ensure this field is included
    profilePictureFile: null,
    profilePictureUrl: '', // URL da imagem de perfil armazenada
  },
  objective: '',
  education: [
    { course: '', institution: '', startYear: '', endYearOrStatus: '', additionalInfo: '', level: '' },
    { course: '', institution: '', startYear: '', endYearOrStatus: '', additionalInfo: '', level: '' },
  ],
  experience: [
    {
      company: '',
      startYear: '',
      endYear: '',
      role: '',
      activities: '',
      localidade: '',
    },
  ],
  qualifications: '',
  additionalInfo: '',
};

export default function ResumeForm({
  selectedResume,
  onCustomGenerate = null,
  isCustomGenerating = false,
  showFormatSelector = true,
  clientId = null
}) {
  const [formData, setFormData] = useState(initialFormData);
  const [isLoading, setIsLoading] = useState(false);
  const [resumeUrl, setResumeUrl] = useState(null);
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [profilePictureFileToUpload, setProfilePictureFileToUpload] = useState(null);

  useEffect(() => {
    if (selectedResume) {
      // Ensure education and experience are arrays with at least one empty item if empty
      const educationData = selectedResume.education && selectedResume.education.length > 0
        ? selectedResume.education.map(edu => ({
            course: edu.course || '',
            institution: edu.institution || '',
            startYear: edu.startYear || '',
            endYearOrStatus: edu.completionYear || edu.endYearOrStatus || '',
            additionalInfo: edu.additionalInfo || '',
            level: edu.level || '' // Support level field
          }))
        : [{ course: '', institution: '', startYear: '', endYearOrStatus: '', additionalInfo: '', level: '' }];

      const experienceData = selectedResume.experience && selectedResume.experience.length > 0
        ? selectedResume.experience.map(exp => ({
            company: exp.company || '',
            startYear: exp.startYear || '',
            endYear: exp.endYear || '',
            role: exp.role || '',
            activities: exp.activities || '',
            localidade: exp.localidade || '' // Support location field
          }))
        : [{ company: '', startYear: '', endYear: '', role: '', activities: '', localidade: '' }];

      setFormData({
        personalInfo: {
          ...initialFormData.personalInfo, // Start with defaults to ensure all fields exist
          ...(selectedResume.personalInfo || {}),
          // Ensure new fields exist with default values if not present
          levelOfEducation: selectedResume.personalInfo?.levelOfEducation || '',
          rg: selectedResume.personalInfo?.rg || '',
          cpf: selectedResume.personalInfo?.cpf || '',
          dateOfBirth: selectedResume.personalInfo?.dateOfBirth || '',
          nacionalidade: selectedResume.personalInfo?.nacionalidade || '', // Add default
          cnh: selectedResume.personalInfo?.cnh || '', // Add default
          profilePictureUrl: selectedResume.personalInfo?.profilePictureUrl || '',
          profilePictureFile: null, // Always reset image file when loading new resume
        },
        objective: selectedResume.objective || '',
        education: educationData,
        experience: experienceData,
        qualifications: selectedResume.qualifications || '',
        additionalInfo: selectedResume.additionalInfo || '',
      });
      setResumeUrl(null); // Limpa a URL do currículo anterior ao carregar um novo
      setError(null); // Limpa erros anteriores
      setValidationErrors({}); // Limpa erros de validação anteriores
      setShowValidationErrors(false); // Não mostra erros de validação ao carregar
      setProfilePictureFileToUpload(null); // Reset file to upload when loading new resume
    } else {
      // Opcional: Resetar o formulário se selectedResume for null (por exemplo, ao limpar a seleção)
      // setFormData(initialFormData);
    }
  }, [selectedResume]);

  // Handlers para cada seção
  // const handleSituationChange = (value) => { // Removido
  //   setFormData({
  //     ...formData,
  //     situation: value,
  //   });
  // };

  const handlePersonalInfoChange = (field, value) => {
    setFormData({
      ...formData,
      personalInfo: {
        ...formData.personalInfo,
        [field]: value,
      },
    });
  };

  const handleProfilePictureChange = (e) => {
    const file = e.target.files[0] || null;
    
    // Update the file to upload state
    setProfilePictureFileToUpload(file);
    
    // Generate preview URL for immediate display
    if (file) {
      const previewUrl = URL.createObjectURL(file);
      setFormData({
        ...formData,
        personalInfo: {
          ...formData.personalInfo,
          profilePictureUrl: previewUrl,
          profilePictureFile: file,
        },
      });
    } else {
      setFormData({
        ...formData,
        personalInfo: {
          ...formData.personalInfo,
          profilePictureUrl: '',
          profilePictureFile: null,
        },
      });
    }
  };

  const handleObjectiveChange = (value) => {
    setFormData({
      ...formData,
      objective: value,
    });
  };

  const handleEducationChange = (field, value, index) => {
    const newEducation = [...formData.education];
    newEducation[index] = {
      ...newEducation[index],
      [field]: value,
    };
    setFormData({
      ...formData,
      education: newEducation,
    });
  };

  const addEducationField = () => {
    setFormData({
      ...formData,
      education: [
        ...formData.education,
        { course: '', institution: '', startYear: '', endYearOrStatus: '', additionalInfo: '', level: '' },
      ],
    });
  };

  const removeEducationField = (indexToRemove) => {
    if (formData.education.length <= 1) {
      return;
    }

    const newEducation = formData.education.filter((_, index) => index !== indexToRemove);
    setFormData({
      ...formData,
      education: newEducation,
    });
  };

  const handleExperienceChange = (field, value, index) => {
    const newExperience = [...formData.experience];
    newExperience[index] = {
      ...newExperience[index],
      [field]: value,
    };
    setFormData({
      ...formData,
      experience: newExperience,
    });
  };

  const addExperienceField = () => {
    setFormData({
      ...formData,
      experience: [
        ...formData.experience,
        { company: '', startYear: '', endYear: '', role: '', activities: '', localidade: '' },
      ],
    });
  };

  const removeExperienceField = (indexToRemove) => {
    if (formData.experience.length <= 1) {
      return;
    }

    const newExperience = formData.experience.filter((_, index) => index !== indexToRemove);
    setFormData({
      ...formData,
      experience: newExperience,
    });
  };

  const handleQualificationsChange = (value) => {
    setFormData({
      ...formData,
      qualifications: value,
    });
  };

  const handleAdditionalInfoChange = (value) => {
    setFormData({
      ...formData,
      additionalInfo: value,
    });
  };

  // Validação de campos obrigatórios
  const validateFields = () => {
    const errors = {};
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^\+?[0-9\s\(\)-]{10,15}$/; // Permite números, espaços, ( ), -, + e entre 10-15 chars
    
    // Nova regex para validar formatos de data em formato YYYY-MM-DD
    const dateYYYYMMDDRegex = /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/;

    // Dados Pessoais
    if (!formData.personalInfo.fullName) {
      errors.fullName = 'Nome completo é obrigatório.';
    }

    if (!formData.personalInfo.phone1) {
      errors.phone1 = 'Telefone 1 é obrigatório.';
    } else if (!phoneRegex.test(formData.personalInfo.phone1)) {
      errors.phone1 = 'Telefone 1 inválido. Use apenas números e ( ) - +.';
    }

    if (formData.personalInfo.phone2 && !phoneRegex.test(formData.personalInfo.phone2)) {
      errors.phone2 = 'Telefone 2 inválido. Use apenas números e ( ) - +.';
    }

    if (formData.personalInfo.email && !emailRegex.test(formData.personalInfo.email)) {
      errors.email = 'Formato de email inválido.';
    }

    // Educação
    let educationError = false;
    formData.education.forEach((edu, index) => {
      // Validação para cada item de educação
      const isAnyEduFieldForRowFilled = edu.course || edu.institution || edu.startYear || edu.endYearOrStatus;

      if (isAnyEduFieldForRowFilled) {
        if (!edu.course) {
          errors[`education_${index}_course`] = 'Curso é obrigatório.';
          educationError = true;
        }
        if (!edu.institution) {
          errors[`education_${index}_institution`] = 'Instituição é obrigatória.';
          educationError = true;
        }
        if (!edu.startYear) {
          errors[`education_${index}_startYear`] = 'Data de início é obrigatória.';
          educationError = true;
        } else if (edu.startYear && !dateYYYYMMDDRegex.test(edu.startYear)) {
          errors[`education_${index}_startYear`] = 'Formato inválido. Use o calendário para selecionar uma data válida.';
          educationError = true;
        }

        if (!edu.endYearOrStatus) {
          errors[`education_${index}_endYearOrStatus`] = 'Data de Conclusão é obrigatória.';
          educationError = true;
        } else if (edu.endYearOrStatus && !dateYYYYMMDDRegex.test(edu.endYearOrStatus)) {
          errors[`education_${index}_endYearOrStatus`] = 'Formato inválido. Use o calendário para selecionar uma data válida.';
          educationError = true;
        }
        
        // Validação cruzada para garantir que data de conclusão é posterior à data de início
        if (edu.startYear && edu.endYearOrStatus && 
            dateYYYYMMDDRegex.test(edu.startYear) && 
            dateYYYYMMDDRegex.test(edu.endYearOrStatus) && 
            new Date(edu.endYearOrStatus) < new Date(edu.startYear)) {
          errors[`education_${index}_endYearOrStatus`] = 'Data de conclusão deve ser após a data de início.';
          educationError = true;
        }
      }
    });

    // Verifica se pelo menos uma formação está completamente preenchida se alguma foi iniciada
    const isAnyEducationEntryStarted = formData.education.some(
      edu => edu.course || edu.institution || edu.startYear || edu.endYearOrStatus
    );
    const hasAtLeastOneCompleteEducation = formData.education.some(
      edu => edu.course && edu.institution && edu.startYear && dateYYYYMMDDRegex.test(edu.startYear) && 
             edu.endYearOrStatus && dateYYYYMMDDRegex.test(edu.endYearOrStatus)
    );

    if (isAnyEducationEntryStarted && !hasAtLeastOneCompleteEducation && !educationError) { // Adicionado !educationError para não sobrepor erros específicos
         errors.education = 'Se uma formação for iniciada, todos os campos obrigatórios (Curso, Instituição, Data de Início, Data de Conclusão) devem ser preenchidos corretamente.';
    }


    // Experiência
    formData.experience.forEach((exp, index) => {
      if (exp.company || exp.role || exp.startYear || exp.endYear || exp.activities) { // Se algum campo da linha foi preenchido
        if (exp.startYear && !dateYYYYMMDDRegex.test(exp.startYear)) {
          errors[`experience_${index}_startYear`] = 'Formato inválido. Use o calendário para selecionar uma data válida.';
        }

        if (exp.endYear && !dateYYYYMMDDRegex.test(exp.endYear)) {
          errors[`experience_${index}_endYear`] = 'Formato inválido. Use o calendário para selecionar uma data válida.';
        }
        
        // Validação cruzada para garantir que data de saída é posterior à data de entrada
        if (exp.startYear && exp.endYear && 
            dateYYYYMMDDRegex.test(exp.startYear) && 
            dateYYYYMMDDRegex.test(exp.endYear) && 
            new Date(exp.endYear) < new Date(exp.startYear)) {
          errors[`experience_${index}_endYear`] = 'Data de saída deve ser após a data de entrada.';
        }

        // Adicionar outras validações obrigatórias para experiência se necessário
        if (!exp.company) errors[`experience_${index}_company`] = 'Empresa é obrigatória se outros campos da experiência estiverem preenchidos.';
        if (!exp.role) errors[`experience_${index}_role`] = 'Cargo é obrigatório se outros campos da experiência estiverem preenchidos.';
      }
    });

    console.log("Validation Errors:", errors);
    return errors;
  };

  const handleCustomGenerate = async () => {
    try {
      let updatedFormData = { ...formData };

      // Se há uma nova imagem para upload
      if (profilePictureFileToUpload) {
        setError(null);
        
        try {
          const uploadResponse = await infocellApi.uploadProfilePicture(clientId, profilePictureFileToUpload);
          
          if (uploadResponse.success && uploadResponse.data) {
            // Atualizar formData com o path do MinIO
            updatedFormData = {
              ...updatedFormData,
              personalInfo: {
                ...updatedFormData.personalInfo,
                profilePictureUrl: uploadResponse.data.file_path
              }
            };
            
            // Reset the file to upload since it's now uploaded
            setProfilePictureFileToUpload(null);
            
            console.log('Foto de perfil enviada com sucesso:', uploadResponse.data.file_path);
          } else {
            throw new Error('Resposta inválida do servidor ao fazer upload da imagem');
          }
        } catch (uploadError) {
          console.error('Erro ao fazer upload da foto de perfil:', uploadError);
          setError(`Erro ao fazer upload da foto de perfil: ${uploadError.message}`);
          return; // Não prosseguir se o upload falhar
        }
      } else {
        // Não há nova imagem para upload, mas garantir que a URL existente seja preservada
        //console.log('Nenhuma nova imagem para upload. Usando URL existente:', formData.personalInfo.profilePictureUrl);
      }

      // Chamar a função de geração customizada com os dados atualizados
      onCustomGenerate(updatedFormData);
      
    } catch (error) {
      console.error('Erro no fluxo customizado:', error);
      setError(`Erro: ${error.message}`);
    }
  };

  const handleGenerateResume = async (e) => {
    e.preventDefault();

    // Mostrar erros de validação
    setShowValidationErrors(true);

    // Validar campos
    const fieldErrors = validateFields();
    if (Object.keys(fieldErrors).length > 0) {
      setValidationErrors(fieldErrors);
      setError('Por favor, preencha todos os campos obrigatórios.');
      return;
    }

    // Novo fluxo: Se há uma função de geração customizada E clientId (gerar-documento)
    if (onCustomGenerate && clientId) {
      await handleCustomGenerate();
      return;
    }

    // Fluxo legado: Se há uma função de geração customizada mas sem clientId
    if (onCustomGenerate) {
      onCustomGenerate(formData);
      return;
    }

    setIsLoading(true);
    setError(null);
    setValidationErrors({});

    try {
      // Criar um objeto FormData para enviar dados multipart/form-data
      const dataToSubmit = new FormData();
      
      // Adicionar campos simples
      dataToSubmit.append('objective', formData.objective);
      dataToSubmit.append('qualifications', formData.qualifications);
      dataToSubmit.append('additionalInfo', formData.additionalInfo);
      
      // Adicionar objetos complexos como strings JSON
      dataToSubmit.append('education', JSON.stringify(formData.education));
      dataToSubmit.append('experience', JSON.stringify(formData.experience));
      
      // Adicionar dados pessoais campo a campo
      Object.entries(formData.personalInfo).forEach(([key, value]) => {
        // Não incluímos o arquivo aqui, ele será tratado separadamente
        if (key !== 'profilePictureFile' && value !== null && value !== undefined) {
          dataToSubmit.append(`personalInfo[${key}]`, value);
        }
      });
      
      // Adicionar o arquivo de imagem se existir
      if (formData.personalInfo.profilePictureFile) {
        dataToSubmit.append('profilePictureFile', formData.personalInfo.profilePictureFile);
      }

      const response = await fetch(`${APP_PREFIX}/api/generate-resume`, {
        method: 'POST',
        // Não definimos Content-Type para que o navegador configure automaticamente
        // com o boundary correto para multipart/form-data
        body: dataToSubmit,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Erro ao gerar o currículo');
      }

      setResumeUrl(result.resumeUrl);
    } catch (err) {
      console.error('Erro:', err);
      setError(err.message || 'Ocorreu um erro ao gerar o currículo');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleGenerateResume}>
      {/* Situação Atual Removida */}
      {/* <CurrentSituationSection
        situation={formData.situation}
        onChange={handleSituationChange}
      /> */}

      {/* Dados Pessoais */}
      <PersonalInfoSection
        personalInfo={formData.personalInfo}
        onChange={(field, value) => {
          if (field === 'profilePictureFile') {
            handleProfilePictureChange(value);
          } else {
            handlePersonalInfoChange(field, value);
          }
        }}
        validationErrors={validationErrors}
        showValidationErrors={showValidationErrors}
      />

      {/* Objetivo */}
      <ObjectiveSection
        objective={formData.objective}
        onChange={handleObjectiveChange}
      />

      {/* Formação Acadêmica */}
      <EducationSection
        education={formData.education}
        onAddField={addEducationField}
        onRemoveField={removeEducationField}
        onChange={handleEducationChange}
        validationErrors={validationErrors}
        showValidationErrors={showValidationErrors}
      />

      {/* Experiência Profissional */}
      <ExperienceSection
        experience={formData.experience}
        onAddField={addExperienceField}
        onRemoveField={removeExperienceField}
        onChange={handleExperienceChange}
        validationErrors={validationErrors}
        showValidationErrors={showValidationErrors}
      />

      {/* Qualificações */}
      <QualificationsSection 
        qualifications={formData.qualifications} 
        onChange={handleQualificationsChange} 
      />

      {/* Informações Adicionais */}
      <AdditionalInfoSection 
        additionalInfo={formData.additionalInfo} 
        onChange={handleAdditionalInfoChange} 
      />

      {/* Mensagem de erro */}
      <ErrorMessage message={error} />

      {/* URL do Currículo */}
      <ResumeResult resumeUrl={resumeUrl} />

      {/* Botão Gerar */}
      <div className="flex justify-center mt-8">
        <Button
          type="submit"
          className="px-8 py-6 text-lg"
          size="lg"
          disabled={isLoading || isCustomGenerating}
        >
          {(isLoading || isCustomGenerating) ? (
            <>
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
              Gerando...
            </>
          ) : (
            onCustomGenerate ? 'Gerar Documento' : 'Gerar Meu Currículo'
          )}
        </Button>
      </div>
    </form>
  );
}
