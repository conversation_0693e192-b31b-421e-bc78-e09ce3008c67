'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Trash2 } from "lucide-react";

export default function ExperienceSection({ experience, onAddField, onRemoveField, onChange, validationErrors = {}, showValidationErrors = false }) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          4. EXPERIÊNCIA PROFISSIONAL ( o texto padrao sera usado se nao for preenchido
          : Em busca do primeiro emprego.)
        </CardTitle>
      </CardHeader>
      <CardContent>
        {experience.map((exp, index) => (
          <div key={index} className="mb-6 border-b pb-6 last:border-0">
            <div className="flex justify-between items-start mb-4">
              <label className="block text-sm font-medium">
                Empresa: <span className="text-red-500">*</span>
              </label>
              {experience.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-destructive"
                  onClick={() => onRemoveField(index)}
                >
                  <Trash2 className="h-4 w-4" />
                  <span className="sr-only">Remover experiência</span>
                </Button>
              )}
            </div>
            <div className="mb-4">
              <Input
                value={exp.company}
                onChange={(e) => onChange('company', e.target.value, index)}
                className={`${showValidationErrors && validationErrors[`experience_${index}_company`] ? 'border-red-500' : ''}`}
              />
              {showValidationErrors && validationErrors[`experience_${index}_company`] && (
                <p className="text-red-500 text-xs mt-1">{validationErrors[`experience_${index}_company`]}</p>
              )}
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Data de Entrada:</label>
                <Input
                  type="date"
                  value={exp.startYear}
                  onChange={(e) => onChange('startYear', e.target.value, index)}
                  className={`${showValidationErrors && validationErrors[`experience_${index}_startYear`] ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors[`experience_${index}_startYear`] && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors[`experience_${index}_startYear`]}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Data de Saída (deixar em branco se for o trabalho atual):</label>
                <Input
                  type="date"
                  value={exp.endYear}
                  onChange={(e) => onChange('endYear', e.target.value, index)}
                  className={`${showValidationErrors && validationErrors[`experience_${index}_endYear`] ? 'border-red-500' : ''}`}
                />
                 {showValidationErrors && validationErrors[`experience_${index}_endYear`] && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors[`experience_${index}_endYear`]}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Cargo: <span className="text-red-500">*</span>
                </label>
                <Input
                  value={exp.role}
                  onChange={(e) => onChange('role', e.target.value, index)}
                  className={`${showValidationErrors && validationErrors[`experience_${index}_role`] ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors[`experience_${index}_role`] && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors[`experience_${index}_role`]}</p>
                )}
              </div>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                Principais atividades desempenhadas no cargo:
                (separar por vírgula)
              </label>
              <Textarea 
                className="min-h-24"
                value={exp.activities}
                onChange={(e) => onChange('activities', e.target.value, index)}
              />
            </div>
          </div>
        ))}
        <Button 
          type="button" 
          variant="outline"
          className="w-full mt-2"
          onClick={onAddField}
        >
          <Plus className="w-4 h-4 mr-2" />
          Adicionar outra experiência
        </Button>
      </CardContent>
    </Card>
  );
}
