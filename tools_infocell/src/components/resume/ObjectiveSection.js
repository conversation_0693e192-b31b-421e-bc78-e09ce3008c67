'use client';

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";

export default function ObjectiveSection({ objective, onChange }) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          2. OBJETIVO
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Textarea 
          className="min-h-24"
          value={objective}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Se nao for preenchido o texto padrao sera usado: Desejo adquirir experiência na empresa e contribuir para seu crescimento, ao mesmo passo em que me desenvolvo como profissional."
        />
      </CardContent>
    </Card>
  );
}
