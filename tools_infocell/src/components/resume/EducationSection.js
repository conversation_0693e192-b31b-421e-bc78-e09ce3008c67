'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, Trash2 } from "lucide-react";

export default function EducationSection({ education, onAddField, onRemoveField, onChange, validationErrors = {}, showValidationErrors = false }) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          3. FORMAÇÃO ACADÊMICA
        </CardTitle>
      </CardHeader>
      <CardContent>
        {validationErrors.education && showValidationErrors && (
          <p className="text-red-500 text-xs mt-1 mb-4">{validationErrors.education}</p>
        )}
        {education.map((edu, index) => (
          <div key={index} className="mb-6 border-b pb-6 last:border-0">
            <div className="flex justify-between items-start mb-4">
              <label className="block text-sm font-medium">
                Dados do Curso: 
              </label>
              {education.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-destructive"
                  onClick={() => onRemoveField(index)}
                >
                  <Trash2 className="h-4 w-4" />
                  <span className="sr-only">Remover curso</span>
                </Button>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Tipo de curso:</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={edu.level || ''}
                  onChange={(e) => onChange('level', e.target.value, index)}
                >
                  <option value="">Selecione</option>
                  <option value="Graduação">Graduação</option>
                  <option value="Pós-graduação">Pós-graduação</option>
                  <option value="Técnico">Técnico</option>
                  <option value="Profissionalizante">Profissionalizante</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Nome do curso: <span className="text-red-500">*</span>
                </label>
                <Input
                  value={edu.course || ''}
                  onChange={(e) => onChange('course', e.target.value, index)}
                  className={`${showValidationErrors && validationErrors[`education_${index}_course`] ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors[`education_${index}_course`] && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors[`education_${index}_course`]}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Instituição: <span className="text-red-500">*</span>
                </label>
                <Input
                  value={edu.institution || ''}
                  onChange={(e) => onChange('institution', e.target.value, index)}
                  className={`${showValidationErrors && validationErrors[`education_${index}_institution`] ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors[`education_${index}_institution`] && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors[`education_${index}_institution`]}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Data de Início: <span className="text-red-500">*</span>
                </label>
                <Input
                  type="date"
                  value={edu.startYear || ''}
                  onChange={(e) => onChange('startYear', e.target.value, index)}
                  className={`${showValidationErrors && validationErrors[`education_${index}_startYear`] ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors[`education_${index}_startYear`] && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors[`education_${index}_startYear`]}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  Data de Conclusão/Previsão de Conclusão: <span className="text-red-500">*</span>
                </label>
                <Input
                  type="date"
                  value={edu.endYearOrStatus || ''}
                  onChange={(e) => onChange('endYearOrStatus', e.target.value, index)}
                  className={`${showValidationErrors && validationErrors[`education_${index}_endYearOrStatus`] ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors[`education_${index}_endYearOrStatus`] && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors[`education_${index}_endYearOrStatus`]}</p>
                )}
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                Informações adicionais:
              </label>
              <Input 
                placeholder="Ex: Média 9.5, Monitor da disciplina, etc. (opcional)"
                value={edu.additionalInfo || ''}
                onChange={(e) => onChange('additionalInfo', e.target.value, index)}
              />
            </div>
          </div>
        ))}
        <Button 
          type="button" 
          variant="outline"
          className="w-full mt-4"
          onClick={onAddField}
        >
          <Plus className="w-4 h-4 mr-2" />
          Adicionar outra formação
        </Button>
        <p className="text-xs text-center text-muted-foreground mt-2">
          Adicione quantas formações forem necessárias para seu currículo
        </p>
      </CardContent>
    </Card>
  );
}
