'use client';

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function PersonalInfoSection({ personalInfo, onChange, validationErrors = {}, showValidationErrors = false }) {
  const [profileImageUrl, setProfileImageUrl] = useState(null);
  
  // Funções de formatação
  const formatCPF = (value) => {
    if (!value) return "";
    const cpf = value.replace(/\D/g, ""); // Remove tudo que não é dígito
    if (cpf.length <= 3) return cpf;
    if (cpf.length <= 6) return `${cpf.slice(0, 3)}.${cpf.slice(3)}`;
    if (cpf.length <= 9) return `${cpf.slice(0, 3)}.${cpf.slice(3, 6)}.${cpf.slice(6)}`;
    return `${cpf.slice(0, 3)}.${cpf.slice(3, 6)}.${cpf.slice(6, 9)}-${cpf.slice(9, 11)}`;
  };

  const formatRG = (value) => {
    if (!value) return "";
    const rg = value.replace(/\D/g, ""); // Remove tudo que não é dígito
    // Formato XX.XXX.XXX-X ou X.XXX.XXX (simplificado)
    if (rg.length <= 2) return rg;
    if (rg.length <= 5) return `${rg.slice(0, 2)}.${rg.slice(2)}`;
    if (rg.length <= 8) return `${rg.slice(0, 2)}.${rg.slice(2, 5)}.${rg.slice(5)}`;
    if (rg.length <= 9) return `${rg.slice(0, 2)}.${rg.slice(2, 5)}.${rg.slice(5, 8)}-${rg.slice(8, 9)}`;
    return `${rg.slice(0, 2)}.${rg.slice(2, 5)}.${rg.slice(5, 8)}-${rg.slice(8, 9)}`; // Limita o tamanho
  };

  // Atualiza a imagem se o personalInfo tiver uma imagem já definida
  useEffect(() => {
    if (personalInfo.profilePictureUrl) {
      // Se a URL já é uma URL completa (HTTP/HTTPS) ou blob (para preview), usar diretamente
      if (personalInfo.profilePictureUrl.startsWith('http') || 
          personalInfo.profilePictureUrl.startsWith('blob:')) {
        setProfileImageUrl(personalInfo.profilePictureUrl);
      } else if (personalInfo.profilePictureUrl.startsWith('profile-pictures/')) {
        // Se é um path do MinIO, construir a URL da API usando a variável de ambiente
        const pathParts = personalInfo.profilePictureUrl.split('/');
        if (pathParts.length >= 3) {
          const clientId = pathParts[1];
          const fileName = pathParts[2];
          // Usar a variável de ambiente configurada
          const apiUrl = process.env.NEXT_PUBLIC_INFOCELL_SERVER_API_URL || 'http://localhost:8080';
          setProfileImageUrl(`${apiUrl}/api/v1/clients/${clientId}/profile-picture/${fileName}`);
        }
      }
    } else if (personalInfo.profilePicture) {
      setProfileImageUrl(personalInfo.profilePicture);
    }
  }, [personalInfo.profilePictureUrl, personalInfo.profilePicture]);

  const handleChange = (field, value) => {
    let formattedValue = value;
    if (field === 'cpf') {
      formattedValue = formatCPF(value);
    } else if (field === 'rg') {
      formattedValue = formatRG(value);
    }
    onChange(field, formattedValue);
  };

  const handleProfilePictureChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const imageUrl = URL.createObjectURL(file);
      setProfileImageUrl(imageUrl);
      
      // Passa o evento para o componente pai
      const event = {
        target: {
          files: e.target.files
        }
      };
      onChange('profilePictureFile', event);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          1. DADOS PESSOAIS
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col-reverse md:flex-row">
          {/* Área principal com os campos do formulário */}
          <div className="flex-grow">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Nome Completo: <span className="text-red-500">*</span>
                </label>
                <Input
                  value={personalInfo.fullName}
                  onChange={(e) => handleChange('fullName', e.target.value)}
                  className={`${showValidationErrors && validationErrors.fullName ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors.fullName && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors.fullName}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Data de Nascimento:</label>
                <Input 
                  type="date"
                  value={personalInfo.dateOfBirth}
                  onChange={(e) => handleChange('dateOfBirth', e.target.value)}
                />
              </div>
            </div>

            {/* Documentos pessoais */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">CPF:</label>
                <Input 
                  type="text"
                  value={personalInfo.cpf}
                  onChange={(e) => handleChange('cpf', e.target.value)}
                  maxLength={14} // 9 digits + 2 dots + 1 dash
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">RG:</label>
                <Input 
                  type="text"
                  value={personalInfo.rg}
                  onChange={(e) => handleChange('rg', e.target.value)}
                  maxLength={12} // 8 digits + 2 dots + 1 dash (formato XX.XXX.XXX-X)
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Estado Civil:</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={personalInfo.civilStatus}
                  onChange={(e) => handleChange('civilStatus', e.target.value)}
                >
                  <option value="">Selecione</option>
                  <option value="Solteiro(a)">Solteiro(a)</option>
                  <option value="Casado(a)">Casado(a)</option>
                  <option value="Separado(a)">Separado(a)</option>
                  <option value="União Estável">União Estável</option>
                  <option value="Divorciado(a)">Divorciado(a)</option>
                  <option value="Viúvo(a)">Viúvo(a)</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Tipo de CNH:</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={personalInfo.driverLicenseType || ""}
                  onChange={(e) => handleChange('driverLicenseType', e.target.value)}
                >
                  <option value="">Não possui</option>
                  <option value="A">A</option>
                  <option value="B">B</option>
                  <option value="AB">AB</option>
                  <option value="C">C</option>
                  <option value="D">D</option>
                  <option value="E">E</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Tem Filho(s)?</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={personalInfo.hasChildren}
                  onChange={(e) => handleChange('hasChildren', e.target.value)}
                >
                  <option value="">Selecione</option>
                  <option value="Sim">Sim</option>
                  <option value="Não">Não</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Nível de Formação:</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={personalInfo.levelOfEducation}
                  onChange={(e) => handleChange('levelOfEducation', e.target.value)}
                >
                  <option value="">Selecione</option>
                  <option value="fundamental_incompleto">Ensino Fundamental Incompleto</option>
                  <option value="fundamental_completo">Ensino Fundamental Completo</option>
                  <option value="medio_incompleto">Ensino Médio Incompleto</option>
                  <option value="medio_completo">Ensino Médio Completo</option>
                  <option value="profissionalizante">Profissionalizante</option>
                  <option value="superior_incompleto">Ensino Superior Incompleto</option>
                  <option value="superior_completo">Ensino Superior Completo</option>
                  <option value="pos_graduacao">Pós-graduação</option>
                </select>
              </div>
            </div>

            {/* Endereço */}
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Endereço:</label>
              <Input 
                value={personalInfo.address}
                onChange={(e) => handleChange('address', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">Estado:</label>
                <select
                  className="w-full p-2 border rounded-md"
                  value={personalInfo.state}
                  onChange={(e) => handleChange('state', e.target.value)}
                >
                  <option value="">Selecione</option>
                  <option value="AC">Acre</option>
                  <option value="AL">Alagoas</option>
                  <option value="AP">Amapá</option>
                  <option value="AM">Amazonas</option>
                  <option value="BA">Bahia</option>
                  <option value="CE">Ceará</option>
                  <option value="DF">Distrito Federal</option>
                  <option value="ES">Espírito Santo</option>
                  <option value="GO">Goiás</option>
                  <option value="MA">Maranhão</option>
                  <option value="MT">Mato Grosso</option>
                  <option value="MS">Mato Grosso do Sul</option>
                  <option value="MG">Minas Gerais</option>
                  <option value="PA">Pará</option>
                  <option value="PB">Paraíba</option>
                  <option value="PR">Paraná</option>
                  <option value="PE">Pernambuco</option>
                  <option value="PI">Piauí</option>
                  <option value="RJ">Rio de Janeiro</option>
                  <option value="RN">Rio Grande do Norte</option>
                  <option value="RS">Rio Grande do Sul</option>
                  <option value="RO">Rondônia</option>
                  <option value="RR">Roraima</option>
                  <option value="SC">Santa Catarina</option>
                  <option value="SP">São Paulo</option>
                  <option value="SE">Sergipe</option>
                  <option value="TO">Tocantins</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Cidade:</label>
                <Input 
                  value={personalInfo.city}
                  onChange={(e) => handleChange('city', e.target.value)}
                />
              </div>
            </div>

            {/* Contato */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Telefone 1: <span className="text-red-500">*</span>
                </label>
                <Input
                  type="tel"
                  value={personalInfo.phone1}
                  onChange={(e) => handleChange('phone1', e.target.value)}
                  className={`${showValidationErrors && validationErrors.phone1 ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors.phone1 && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors.phone1}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">Telefone 2:</label>
                <Input
                  type="tel"
                  value={personalInfo.phone2}
                  onChange={(e) => handleChange('phone2', e.target.value)}
                  className={`${showValidationErrors && validationErrors.phone2 ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors.phone2 && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors.phone2}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  E-Mail:
                </label>
                <Input
                  type="email"
                  value={personalInfo.email}
                  onChange={(e) => handleChange('email', e.target.value)}
                  className={`${showValidationErrors && validationErrors.email ? 'border-red-500' : ''}`}
                />
                {showValidationErrors && validationErrors.email && (
                  <p className="text-red-500 text-xs mt-1">{validationErrors.email}</p>
                )}
              </div>
            </div>
          </div>
          
          {/* Área lateral para a foto de perfil */}
          <div className="w-full md:w-48 mb-4 md:mb-0 md:ml-4">
            <div className="flex flex-col items-center">
              <div className="w-full aspect-square mb-2 bg-gray-100 border-2 border-dashed border-gray-300 rounded-md overflow-hidden flex items-center justify-center">
                {profileImageUrl ? (
                  <img 
                    src={profileImageUrl} 
                    alt="Foto de perfil" 
                    className="w-full h-full object-cover" 
                  />
                ) : (
                  <div className="text-gray-400 text-center p-2 text-sm">
                    Nenhuma foto selecionada
                  </div>
                )}
              </div>
              <label className="block text-sm font-medium mb-1 text-center">Foto de Perfil:</label>
              <Input
                type="file"
                accept="image/jpeg, image/png, image/webp"
                onChange={handleProfilePictureChange}
                className="text-xs"
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
