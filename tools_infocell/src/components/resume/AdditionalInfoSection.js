'use client';

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";

export default function AdditionalInfoSection({ additionalInfo, onChange }) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          6. INFORMAÇÕES ADICIONAIS
          (separar por vírgula)
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Textarea 
          className="min-h-24"
          value={additionalInfo}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Ex: Disponibilidade para viagens, CNH categoria B..."
        />
      </CardContent>
    </Card>
  );
}
