'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

export default function ResumeResult({ resumeUrl }) {
  if (!resumeUrl) return null;
  
  return (
    <Card className="mb-6 bg-primary/10">
      <CardContent className="pt-6">
        <p className="mb-4">Seu currículo foi gerado com sucesso! Clique no botão abaixo para download:</p>
        <div className="flex justify-center">
          <Button asChild>
            <a href={resumeUrl} target="_blank" rel="noopener noreferrer" download>
              Download do Currículo
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
