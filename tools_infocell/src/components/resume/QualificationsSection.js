'use client';

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";

export default function QualificationsSection({ qualifications, onChange }) {
  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center">
          5. QUALIFICAÇÕES E ATIVIDADES COMPLEMENTARES
          (separar por vírgula)
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Textarea 
          className="min-h-24"
          value={qualifications}
          onChange={(e) => onChange(e.target.value)}
          placeholder="Ex: Curso de Excel avançado, Inglês intermediário..."
        />
      </CardContent>
    </Card>
  );
}
