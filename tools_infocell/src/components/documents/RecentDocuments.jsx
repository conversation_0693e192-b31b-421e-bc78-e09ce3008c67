"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText, Clock, ArrowRight, Download } from "lucide-react";
import infocell<PERSON><PERSON> from "@/lib/infocellApi";
import useDownload from "@/lib/useDownload";

export default function RecentDocuments() {
  const [documents, setDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { downloadFile } = useDownload();

  useEffect(() => {
    loadRecentDocuments();
  }, []);

  const loadRecentDocuments = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await infocellApi.listDocumentHistory({
        page: 1,
        per_page: 5 // Apenas os 5 mais recentes
      });
      
      console.log('Resposta da API de documentos recentes:', response);
      
      // Verificar diferentes estruturas possíveis da resposta
      const documents = response.data?.documents || response.documents || response.data || [];
      setDocuments(documents);
      
    } catch (err) {
      console.error('Erro ao carregar documentos recentes:', err);
      setError(`Erro ao carregar documentos recentes: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (doc) => {
    try {
      const response = await infocellApi.downloadDocument(doc.id);
      const blob = await response.blob();
      const filename = doc.file_name || `documento_${doc.id}`;
      
      await downloadFile(blob, filename);
      
    } catch (err) {
      console.error('Erro no download:', err);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Função para encurtar nomes de arquivo longos
  const truncateFileName = (fileName, maxLength = 30) => {
    if (!fileName || fileName.length <= maxLength) {
      return fileName;
    }
    
    const extension = fileName.split('.').pop();
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    
    if (nameWithoutExt.length <= maxLength - extension.length - 1) {
      return fileName;
    }
    
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4);
    return `${truncatedName}...${extension}`;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-medium flex items-center gap-2">
          <Clock className="h-5 w-5 text-primary" />
          Documentos Recentes
        </CardTitle>
        <Link href="/dashboard-infocell/documentos">
          <Button variant="ghost" size="sm">
            Ver todos
            <ArrowRight className="h-4 w-4 ml-1" />
          </Button>
        </Link>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-6">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="ml-2 text-sm text-muted-foreground">Carregando...</span>
          </div>
        ) : error ? (
          <p className="text-sm text-destructive text-center py-4">{error}</p>
        ) : documents.length === 0 ? (
          <div className="text-center py-6">
            <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              Nenhum documento gerado ainda
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {documents.map((document) => (
              <div 
                key={document.id} 
                className="flex items-center justify-between p-3 rounded-lg border bg-muted/30 hover:bg-muted/50 transition-colors"
              >
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <FileText className="h-4 w-4 text-primary flex-shrink-0" />
                  <div className="min-w-0 flex-1">
                    <p 
                      className="text-sm font-medium leading-tight"
                      title={document.file_name || `Documento ${document.id}`}
                    >
                      {truncateFileName(document.file_name) || `Documento ${document.id}`}
                    </p>
                    <p className="text-xs text-muted-foreground truncate">
                      {document.client_name || document.client_id} • {formatDate(document.created_at)}
                    </p>
                  </div>
                </div>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleDownload(document)}
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 