@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem; /* Seu raio original, ajuste se desejar o padrão 0.5rem do Shadcn */

  /* Paleta base - Tons neutros claros */
  --background: oklch(0.99 0.005 260); /* Quase branco, levemente frio */
  --foreground: oklch(0.12 0.02 260); /* Cinza escuro, quase preto, levemente frio */
  --card: oklch(1 0 0);               /* Branco puro para cards */
  --card-foreground: oklch(0.12 0.02 260);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.12 0.02 260);
  --border: oklch(0.90 0.015 260);     /* Cinza claro para bordas */
  --input: oklch(0.90 0.015 260);      /* Mesma cor para inputs */
  --ring: oklch(0.55 0.18 270);       /* Azul/Roxo vibrante para anel de foco */

  /* Cores de Ação */
  --primary: oklch(0.55 0.18 270);             /* Azul/Roxo vibrante principal */
  --primary-foreground: oklch(0.98 0.005 270); /* Branco para texto sobre primário */
  --secondary: oklch(0.94 0.01 260);           /* Cinza muito claro para secundário */
  --secondary-foreground: oklch(0.25 0.02 260);/* Cinza escuro para texto sobre secundário */
  --accent: oklch(0.96 0.01 260);              /* Cinza ainda mais claro para destaque sutil */
  --accent-foreground: oklch(0.15 0.02 260);   /* Cor de texto padrão sobre accent */

  /* Cores de Feedback */
  --muted: oklch(0.94 0.01 260);         /* Mesmo que secundário, para texto/elementos silenciados */
  --muted-foreground: oklch(0.45 0.02 260);/* Cinza médio para texto silenciado */
  --destructive: oklch(0.60 0.22 25);      /* Vermelho para ações destrutivas */
  --destructive-foreground: oklch(0.98 0.005 25); /* Branco para texto sobre destrutivo */
  --success: oklch(0.65 0.18 150);         /* Verde para sucesso */
  --success-foreground: oklch(0.05 0.02 150);/* Preto/Cinza escuro para texto sobre sucesso */
  --warning: oklch(0.75 0.20 60);         /* Amarelo/Laranja para aviso */
  --warning-foreground: oklch(0.10 0.03 60); /* Preto/Cinza escuro para texto sobre aviso */
  --info: oklch(0.60 0.20 230);           /* Azul para informação */
  --info-foreground: oklch(0.98 0.005 230);  /* Branco para texto sobre info */

  /* Cores de Gráfico (mantendo como exemplo) */
  --chart-1: oklch(0.65 0.20 260);
  --chart-2: oklch(0.68 0.18 300);
  --chart-3: oklch(0.70 0.16 200);
  --chart-4: oklch(0.75 0.15 140);
  --chart-5: oklch(0.72 0.20 40);

  /* Sidebar (baseado no seu tema anterior, mas com nomes consistentes) */
  --sidebar-background: oklch(0.97 0.01 260);      /* Um pouco mais escuro que o fundo principal */
  --sidebar-foreground: oklch(0.15 0.02 260);      /* Cor de texto padrão */
  --sidebar-primary: var(--primary);               /* Usa a cor primária geral */
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: oklch(0.93 0.015 260);     /* Cor de destaque para item ativo/hover na sidebar */
  --sidebar-accent-foreground: var(--primary); /* Texto do item ativo/hover usa cor primária */
  --sidebar-border: oklch(0.88 0.02 260);
  --sidebar-ring: var(--ring);
}

/* .dark define as variáveis para o modo escuro (dark mode) */
.dark {
  /* Paleta base - Tons neutros escuros */
  --background: oklch(0.10 0.015 260); /* Cinza muito escuro, levemente frio */
  --foreground: oklch(0.95 0.01 260);  /* Quase branco, levemente frio para texto */
  --card: oklch(0.15 0.02 260);        /* Cinza escuro para cards */
  --card-foreground: oklch(0.95 0.01 260);
  --popover: oklch(0.15 0.02 260);
  --popover-foreground: oklch(0.95 0.01 260);
  --border: oklch(0.25 0.025 260);     /* Cinza médio-escuro para bordas */
  --input: oklch(0.25 0.025 260);      /* Mesma cor para inputs */
  --ring: oklch(0.65 0.20 270);       /* Roxo/Azul mais claro e vibrante para foco no escuro */

  /* Cores de Ação */
  --primary: oklch(0.65 0.20 270);             /* Roxo/Azul vibrante, mais claro para dark mode */
  --primary-foreground: oklch(0.10 0.015 270); /* Cinza escuro para texto sobre primário */
  --secondary: oklch(0.20 0.02 260);           /* Cinza escuro para secundário */
  --secondary-foreground: oklch(0.90 0.01 260);/* Cinza claro para texto sobre secundário */
  --accent: oklch(0.22 0.02 260);              /* Cinza um pouco mais claro para destaque sutil */
  --accent-foreground: oklch(0.95 0.01 260);   /* Cor de texto padrão sobre accent */

  /* Cores de Feedback */
  --muted: oklch(0.20 0.02 260);         /* Mesmo que secundário */
  --muted-foreground: oklch(0.60 0.015 260);/* Cinza médio-claro para texto silenciado */
  --destructive: oklch(0.65 0.24 25);      /* Vermelho vibrante para dark mode */
  --destructive-foreground: oklch(0.98 0.005 25); /* Quase branco para texto sobre destrutivo */
  --success: oklch(0.70 0.20 150);         /* Verde mais claro para sucesso */
  --success-foreground: oklch(0.08 0.02 150);/* Preto/Cinza escuro para texto sobre sucesso */
  --warning: oklch(0.80 0.22 60);         /* Amarelo/Laranja mais claro para aviso */
  --warning-foreground: oklch(0.12 0.03 60); /* Preto/Cinza escuro para texto sobre aviso */
  --info: oklch(0.65 0.22 230);           /* Azul mais claro para informação */
  --info-foreground: oklch(0.08 0.015 230); /* Preto/Cinza escuro para texto sobre info */

  /* Cores de Gráfico (ajustar para bom contraste no escuro) */
  --chart-1: oklch(0.70 0.22 260);
  --chart-2: oklch(0.72 0.20 300);
  --chart-3: oklch(0.75 0.18 200);
  --chart-4: oklch(0.80 0.17 140);
  --chart-5: oklch(0.78 0.22 40);

  /* Sidebar (dark mode) */
  --sidebar-background: oklch(0.12 0.018 260);    /* Um pouco mais claro que o fundo principal escuro */
  --sidebar-foreground: oklch(0.90 0.01 260);    /* Cor de texto */
  --sidebar-primary: var(--primary);
  --sidebar-primary-foreground: var(--primary-foreground);
  --sidebar-accent: oklch(0.25 0.025 260);   /* Cor de destaque para item ativo/hover */
  --sidebar-accent-foreground: var(--primary);
  --sidebar-border: oklch(0.30 0.03 260);
  --sidebar-ring: var(--ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  [data-slot="card"] {
    @apply border shadow-md;
  }
}

