'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  FileText, 
  User, 
  FolderOpen, 
  Download, 
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Zap,
  UserPlus,
  Printer,
  Share2,
  Loader2
} from "lucide-react";
import ClientSearchInput from "@/components/custom/ClientSearchInput";
import ResumeForm from "@/components/custom/ResumeForm";
import ProcuracaoForm from "@/components/custom/ProcuracaoForm";
import ContratoAluguelForm from "@/components/custom/ContratoAluguelForm";
import infocellApi from "@/lib/infocellApi";
import usePrint from "@/lib/hooks/usePrint";
import { shortenUrl } from "@/lib/urlShortener";

const OUTPUT_FORMATS = [
  { value: 'pdf', label: 'PDF', description: 'Formato final para impressão' },
  { value: 'docx', label: 'Word (DOCX)', description: 'Editável no Microsoft Word' }
];

const RESUME_OUTPUT_FORMATS = [
  { value: 'docx', label: 'Word (DOCX)', description: 'Formato recomendado - Inclui foto de perfil automaticamente' }
];

export default function GenerateDocumentPage() {
  // Estados do fluxo
  const [currentStep, setCurrentStep] = useState(1);
  // Novo estado para múltiplos clientes por papel
  const [selectedClientsByRole, setSelectedClientsByRole] = useState({});
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [outputFormat, setOutputFormat] = useState('docx'); // Padrão DOCX para currículos
  const [formData, setFormData] = useState(null);
  const [customData, setCustomData] = useState('{}'); // Para templates não-currículo
  
  // Estados de carregamento e status
  const [templates, setTemplates] = useState([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [generatedDocument, setGeneratedDocument] = useState(null);
  const [isPrinting, setIsPrinting] = useState(false);
  
  // Novos estados para criação de cliente
  const [willCreateNewClient, setWillCreateNewClient] = useState(false);
  const [isCreatingClient, setIsCreatingClient] = useState(false);
  const [clientsToCreate, setClientsToCreate] = useState([]); // Array com os papéis que serão criados
  const { printFile } = usePrint();

  // Estados para o novo fluxo
  const [currentClientSelection, setCurrentClientSelection] = useState(''); // qual papel está sendo selecionado

  // Carregar todos os templates ativos
  useEffect(() => {
    loadTemplates();
  }, []);

  // Limpar mensagens após 5 segundos
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  // Carregar dados do cliente quando selecionado (apenas para currículos)
  useEffect(() => {
    const resumeClient = selectedClientsByRole.titular;
    if (resumeClient && selectedTemplate && selectedTemplate.template_type === 'resume') {
      loadClientData(resumeClient);
    }
  }, [selectedClientsByRole.titular, selectedTemplate]);

  const loadTemplates = async () => {
    try {
      setIsLoadingTemplates(true);
      const response = await infocellApi.getTemplates({
        is_active: true,
        per_page: 50
      });
      
      setTemplates(response.data?.templates || []);
    } catch (err) {
      console.error('Erro ao carregar templates:', err);
      setError('Erro ao carregar templates: ' + err.message);
      setTemplates([]);
    } finally {
      setIsLoadingTemplates(false);
    }
  };

  const loadClientData = async (client) => {
    try {
      setIsLoadingData(true);
      setError(null);
      
      const erpPersonalInfo = mapErpToPersonalInfo(client);
      
      try {
        const localDataResponse = await infocellApi.getLocalClientSupplement(client.id);
        const localData = localDataResponse?.data;
        
        if (localData && typeof localData === 'object') {
          const mappedFormData = mapLocalToFormData(localData, erpPersonalInfo);
          setFormData(mappedFormData);
        } else {
          setFormData({
            personalInfo: erpPersonalInfo,
            objective: '',
            education: [{ course: '', institution: '', startYear: '', endYearOrStatus: '', additionalInfo: '', level: '' }],
            experience: [{ company: '', startYear: '', endYear: '', role: '', activities: '', localidade: '' }],
            qualifications: '',
            additionalInfo: ''
          });
        }
      } catch (localErr) {
        
        setFormData({
          personalInfo: erpPersonalInfo,
          objective: '',
          education: [{ course: '', institution: '', startYear: '', endYearOrStatus: '', additionalInfo: '', level: '' }],
          experience: [{ company: '', startYear: '', endYear: '', role: '', activities: '', localidade: '' }],
          qualifications: '',
          additionalInfo: ''
        });
      }
    } catch (err) {
      console.error('❌ [LOAD_DATA] Erro geral ao carregar dados do cliente:', err);
      setError('Erro ao carregar dados do cliente: ' + err.message);
    } finally {
      setIsLoadingData(false);
    }
  };

  const mapErpToPersonalInfo = (erpClient) => {
    const address = erpClient.enderecos?.[0]?.endereco;
    return {
      fullName: erpClient.nome || '',
      email: erpClient.email || '',
      phone1: erpClient.telefone || erpClient.celular || '',
      phone2: erpClient.celular && erpClient.telefone !== erpClient.celular ? erpClient.celular : '',
      cpf: erpClient.cpf || '',
      rg: erpClient.rg || '',
      dateOfBirth: erpClient.data_nascimento || '',
      address: address ? `${address.logradouro || ''} ${address.numero || ''}`.trim() : '',
      city: address?.nome_cidade || '',
      state: address?.estado || '',
      civilStatus: '',
      hasChildren: '',
      levelOfEducation: '',
      nacionalidade: '',
      cnh: '',
      profilePictureFile: null,
      // Não sobrescreve profilePictureUrl - será tratado no merge com dados locais
    };
  };

  const mapLocalToFormData = (localData, erpPersonalInfo) => {
    const mergedPersonalInfo = {
      ...erpPersonalInfo,
      ...(localData?.personalInfo || {})
    };

    const education = localData?.education_history?.map(edu => ({
      course: edu.course || '',
      institution: edu.institution || '',
      startYear: edu.start_date ? edu.start_date.split('T')[0] : '',
      endYearOrStatus: edu.end_date ? edu.end_date.split('T')[0] : '',
      additionalInfo: edu.additional_info || '',
      level: edu.level || ''
    })) || [{ course: '', institution: '', startYear: '', endYearOrStatus: '', additionalInfo: '', level: '' }];

    const experience = localData?.professional_experience?.map(exp => ({
      company: exp.company || '',
      startYear: exp.start_date ? exp.start_date.split('T')[0] : '',
      endYear: exp.end_date ? exp.end_date.split('T')[0] : '',
      role: exp.role || '',
      activities: exp.activities || '',
      localidade: exp.localidade || ''
    })) || [{ company: '', startYear: '', endYear: '', role: '', activities: '', localidade: '' }];

    const result = {
      personalInfo: mergedPersonalInfo,
      objective: localData?.objective || '',
      education,
      experience,
      qualifications: localData?.qualifications_summary || '',
      additionalInfo: localData?.additional_notes || ''
    };
    
    return result;
  };

  const handleNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClientSelect = (client) => {
    setSelectedClientsByRole({ ...selectedClientsByRole, [currentClientSelection]: client });
    setWillCreateNewClient(false);
    
    // Verificar se todos os clientes necessários foram selecionados
    if (selectedTemplate) {
      const updatedClients = { ...selectedClientsByRole, [currentClientSelection]: client };
      if (checkAllClientsSelected(updatedClients)) {
        handleNextStep();
      } else {
        // Continuar para próximo papel
        setCurrentClientSelection(getNextRole());
      }
    } else {
      handleNextStep();
    }
  };

  const handleCreateNewClient = () => {
    // Adicionar o papel atual à lista de clientes para criar
    setClientsToCreate([...clientsToCreate, currentClientSelection]);
    setSelectedClientsByRole({ ...selectedClientsByRole, [currentClientSelection]: null });
    
    // Verificar se ainda há clientes para selecionar
    if (selectedTemplate) {
      const updatedClients = { ...selectedClientsByRole, [currentClientSelection]: null };
      const updatedClientsToCreate = [...clientsToCreate, currentClientSelection];
      
      // Marcar o cliente atual como "será criado"
      const requiredRoles = getRequiredRoles(selectedTemplate.template_type);
              const hasMoreRoles = requiredRoles.some(role => {
          if (updatedClientsToCreate.includes(role)) return false; // Será criado
          return !updatedClients[role]; // Ainda precisa ser selecionado
        });
      
      if (hasMoreRoles) {
        // Continuar para próximo papel
        setCurrentClientSelection(getNextRole());
      } else {
        // Todos os clientes foram definidos, ir para formulário
        setWillCreateNewClient(updatedClientsToCreate.length > 0);
        handleNextStep();
      }
    } else {
      setWillCreateNewClient(true);
      handleNextStep(); // Vai para seleção de template se não temos um ainda
    }
  };

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template);
    
    // Determinar quais papéis são necessários e inicializar seleção
    const requiredRoles = getRequiredRoles(template.template_type);
    
    if (requiredRoles.length > 0) {
      setCurrentClientSelection(requiredRoles[0]);
      
      // Se já temos clientes selecionados para todos os papéis, ir direto para o formulário
      if (checkAllClientsSelected(selectedClientsByRole, requiredRoles)) {
        setCurrentStep(3);
      } else {
        handleNextStep();
      }
    } else {
      // Para templates sem papéis específicos, ir direto para o formulário
      setCurrentStep(3);
    }
  };

  const getRequiredRoles = (templateType) => {
    switch (templateType) {
      case 'resume':
        return ['titular'];
      case 'proxy':
        return ['outorgante', 'outorgado'];
      case 'contract':
        return ['locador', 'locatario'];
      default:
        return [];
    }
  };

  const checkAllClientsSelected = (clients, roles = null) => {
    const requiredRoles = roles || getRequiredRoles(selectedTemplate?.template_type);
    return requiredRoles.every(role => {
      // Se o papel está na lista de clientes para criar, considerar como selecionado
      if (clientsToCreate.includes(role)) return true;
      return clients[role] != null;
    });
  };

  const getNextRole = () => {
    const roles = getRequiredRoles(selectedTemplate?.template_type);
    const currentIndex = roles.indexOf(currentClientSelection);
    return currentIndex < roles.length - 1 ? roles[currentIndex + 1] : roles[0];
  };

  const getCurrentRoleLabel = () => {
    switch (currentClientSelection) {
      case 'titular':
        return 'Titular do Currículo';
      case 'outorgante':
        return 'Outorgante';
      case 'outorgado':
        return 'Outorgado';
      case 'locador':
        return 'Locador';
      case 'locatario':
        return 'Locatário';
      default:
        return 'Cliente';
    }
  };

  // Função para criar cliente no ERP a partir dos dados do currículo
  const createClientFromFormData = async (formData, templateType) => {
    try {
      setIsCreatingClient(true);
      
      let personalInfo = null;
      let clientName = '';
      
      // Extrair dados de acordo com o tipo de template
      if (templateType === 'resume') {
        personalInfo = formData.personalInfo;
        clientName = personalInfo.fullName;
      } else if (templateType === 'proxy') {
        // Para procuração, criar cliente baseado nos dados do outorgante ou outorgado
        const isOutorgante = currentClientSelection === 'outorgante';
        const clientData = isOutorgante ? formData.outorgante : formData.outorgado;
        
        personalInfo = {
          fullName: clientData.nomeCompleto,
          cpf: clientData.cpf,
          rg: clientData.rg,
          dateOfBirth: clientData.data_nascimento,
          phone1: clientData.telefone,
          phone2: clientData.celular,
          email: clientData.email,
          address: clientData.endereco,
          city: clientData.municipio || clientData.cidade,
          state: clientData.estado
        };
        clientName = clientData.nomeCompleto;
      } else if (templateType === 'contract') {
        // Para contrato,  criar cliente baseado nos dados do locador ou locatário
        const isLocador = currentClientSelection === 'locador';
        const clientData = isLocador ? formData.locador : formData.locatario;
        
        personalInfo = {
          fullName: clientData.nomeCompleto,
          cpf: clientData.cpf,
          rg: clientData.rg,
          dateOfBirth: clientData.data_nascimento,
          phone1: clientData.telefone,
          phone2: clientData.celular,
          email: clientData.email,
          address: clientData.endereco,
          city: clientData.cidade,
          state: clientData.estado
        };
        clientName = clientData.nomeCompleto;
      }
      
      // Validar dados obrigatórios
      if (!clientName?.trim()) {
        console.error('Dados do formulário:', formData);
        console.error('clientData extraído:', templateType === 'contract' ? (currentClientSelection === 'locador' ? formData.locador : formData.locatario) : (currentClientSelection === 'outorgante' ? formData.outorgante : formData.outorgado));
        throw new Error('Nome completo é obrigatório para cadastrar o cliente');
      }

      // Preparar endereços
      const enderecos = [];
      if (personalInfo.address || personalInfo.city || personalInfo.state) {
        enderecos.push({
          tipo_id: null,
          nome_tipo: null,
          cep: null,
          logradouro: personalInfo.address || null,
          numero: null,
          complemento: null,
          bairro: null,
          pais: "Brasil",
          cidade_id: null,
          nome_cidade: personalInfo.city || null,
          estado: personalInfo.state || null
        });
      }

      // Preparar dados para o ERP
      const clientData = {
        tipo_pessoa: 'PF', // Sempre Pessoa Física
        nome: clientName.trim(),
        razao_social: null,
        cnpj: null,
        inscricao_estadual: null,
        inscricao_municipal: null,
        tipo_contribuinte: null,
        responsavel: null,
        cpf: personalInfo.cpf || null,
        rg: personalInfo.rg || null,
        data_nascimento: personalInfo.dateOfBirth || null,
        sexo: null,
        loja_virtual_is_ativo: null,
        email_acesso: null,
        telefone: personalInfo.phone1 || null,
        celular: personalInfo.phone2 || personalInfo.phone1 || null,
        fax: null,
        email: personalInfo.email || null,
        ativo: true,
        vendedor_id: null,
        nome_vendedor: null,
        enderecos: enderecos
      };

      const response = await infocellApi.createClient(clientData);
      const newClient = response.data;
      
      setSelectedClientsByRole({ ...selectedClientsByRole, [currentClientSelection]: newClient });
      setSuccess(`Cliente "${newClient.nome}" criado com sucesso no ERP!`);
      
      return newClient;
    } catch (err) {
      console.error('Erro ao criar cliente:', err);
      throw new Error('Erro ao criar cliente: ' + err.message);
    } finally {
      setIsCreatingClient(false);
    }
  };

  const handleGenerate = async (documentData = null) => {
    try {
      setIsGenerating(true);
      setError(null);

      let generatePayload = {};
      let additionalData = {};

      if (selectedTemplate.template_type === 'resume') {
        // Lógica existente para currículos
        let clientToUse = selectedClientsByRole.titular;
        if (clientsToCreate.includes('titular') && documentData) {
          clientToUse = await createClientFromFormData(documentData, 'resume');
        }

        if (!clientToUse) {
          throw new Error('Nenhum cliente selecionado ou criado para o currículo');
        }

        const supplementPayload = {
          erp_client_id: clientToUse.id,
          personalInfo: documentData.personalInfo,
          objective: documentData.objective,
          qualifications_summary: documentData.qualifications,
          additional_notes: documentData.additionalInfo,
          education_history: documentData.education.map(edu => ({
            institution: edu.institution,
            course: edu.course,
            level: edu.level || '',
            start_date: edu.startYear ? new Date(edu.startYear).toISOString() : null,
            end_date: edu.endYearOrStatus ? new Date(edu.endYearOrStatus).toISOString() : null,
            additional_info: edu.additionalInfo
          })),
          professional_experience: documentData.experience.map(exp => ({
            company: exp.company,
            role: exp.role,
            localidade: exp.localidade || '',
            start_date: exp.startYear ? new Date(exp.startYear).toISOString() : null,
            end_date: exp.endYear ? new Date(exp.endYear).toISOString() : null,
            activities: exp.activities
          })),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        try {
          await infocellApi.saveLocalClientSupplement(clientToUse.id, supplementPayload);
        } catch (saveErr) {
          console.warn('Erro ao salvar dados suplementares:', saveErr);
        }

        generatePayload = {
          client_id: clientToUse.id,
          template_id: selectedTemplate._id.$oid,
          output_format: outputFormat,
          additional_data: supplementPayload
        };

      } else if (selectedTemplate.template_type === 'proxy') {
        // Nova lógica para procurações
        let outorganteClient = selectedClientsByRole.outorgante;
        let outorgadoClient = selectedClientsByRole.outorgado;

        // Criar cliente outorgante se necessário
        if (!outorganteClient && clientsToCreate.includes('outorgante')) {
          // Temporariamente definir seleção para outorgante
          const originalSelection = currentClientSelection;
          setCurrentClientSelection('outorgante');
          outorganteClient = await createClientFromFormData(documentData, 'proxy');
          setCurrentClientSelection(originalSelection);
        }

        if (!outorganteClient) {
          throw new Error('Cliente outorgante é obrigatório para procurações');
        }

        // Criar cliente outorgado se necessário
        if (!outorgadoClient && clientsToCreate.includes('outorgado')) {
          // Temporariamente mudar a seleção para outorgado
          const originalSelection = currentClientSelection;
          setCurrentClientSelection('outorgado');
          outorgadoClient = await createClientFromFormData(documentData, 'proxy');
          setCurrentClientSelection(originalSelection);
        }

        // Construir client_ids_by_role
        const clientIdsByRole = {
          outorgante: outorganteClient.id
        };

        if (outorgadoClient) {
          clientIdsByRole.outorgado = outorgadoClient.id;
        }

        // Estruturar additional_data conforme esperado pelo backend
        additionalData = {
          outorgante: documentData.outorgante,
          outorgado: documentData.outorgado,
          procuracao: documentData.procuracao
        };

        generatePayload = {
          client_ids_by_role: clientIdsByRole,
          template_id: selectedTemplate._id.$oid,
          output_format: outputFormat,
          additional_data: additionalData
        };

      } else if (selectedTemplate.template_type === 'contract') {
        // Nova lógica para contratos de aluguel
        let locadorClient = selectedClientsByRole.locador;
        let locatarioClient = selectedClientsByRole.locatario;

        // Criar cliente locador se necessário
        if (!locadorClient && clientsToCreate.includes('locador')) {
          // Temporariamente definir seleção para locador
          const originalSelection = currentClientSelection;
          setCurrentClientSelection('locador');
          locadorClient = await createClientFromFormData(documentData, 'contract');
          setCurrentClientSelection(originalSelection);
        }

        if (!locadorClient) {
          throw new Error('Cliente locador é obrigatório para contratos');
        }

        // Criar cliente locatário se necessário
        if (!locatarioClient && clientsToCreate.includes('locatario')) {
          // Temporariamente mudar a seleção para locatario
          const originalSelection = currentClientSelection;
          setCurrentClientSelection('locatario');
          locatarioClient = await createClientFromFormData(documentData, 'contract');
          setCurrentClientSelection(originalSelection);
        }

        const clientIdsByRole = {
          locador: locadorClient.id
        };

        // Adicionar locatário se existe
        if (locatarioClient) {
          clientIdsByRole.locatario = locatarioClient.id;
        }

        // Estruturar additional_data conforme esperado pelo backend
        additionalData = {
          locador: documentData.locador, // Incluindo dados do locador do formulário
          locatario: documentData.locatario,
          imovel: documentData.imovel,
          contrato: documentData.contrato
          // Testemunhas removidas - serão preenchidas à mão no documento impresso
        };

        generatePayload = {
          client_ids_by_role: clientIdsByRole,
          template_id: selectedTemplate._id.$oid,
          output_format: outputFormat,
          additional_data: additionalData
        };

      } else {
        // Lógica para outros tipos de template (JSON customizado)
        try {
          additionalData = JSON.parse(customData);
          additionalData.template_id = selectedTemplate.id;
        } catch (parseErr) {
          throw new Error('Dados JSON inválidos. Verifique a sintaxe.');
        }

        generatePayload = {
          client_id: Object.values(selectedClientsByRole)[0]?.id,
          template_id: selectedTemplate._id.$oid,
          output_format: outputFormat,
          additional_data: additionalData
        };
      }

      const response = await infocellApi.generateDocument(generatePayload);
      setGeneratedDocument({
        data: response.data
      });
      setSuccess('Documento gerado com sucesso!');

    } catch (err) {
      console.error('Erro ao gerar documento:', err);
      setError('Erro ao gerar documento: ' + err.message);
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePrint = async () => {
    if (!generatedDocument?.data?.generated_document_id) {
      setError('ID do documento não encontrado para impressão.');
      return;
    }
    
    setIsPrinting(true);
    try {
      setError(null);
      setSuccess(null);
      
      const docId = generatedDocument.data.generated_document_id;
      const isDocx = outputFormat === 'docx';
      
      if (isDocx) {
        setSuccess('Convertendo DOCX para PDF para impressão... Isso pode levar alguns segundos.');
        
        const response = await infocellApi.convertDocxToPdf(docId);
        const pdfBlob = await response.blob();
        
        setSuccess(null);
        
        await printFile(pdfBlob, 'application/pdf');
      } else {
        const response = await infocellApi.downloadDocument(docId);
        const blob = await response.blob();
        const fileType = response.headers.get('content-type') || 'application/pdf';
        await printFile(blob, fileType);
      }
    } catch (err) {
      console.error('Erro ao processar impressão:', err);
      setError(`Erro ao processar impressão: ${err.message}`);
    } finally {
      setIsPrinting(false);
    }
  };

  const handleShare = async () => {
    if (!generatedDocument?.data?.generated_document_id) {
      setError('ID do documento não encontrado para compartilhar.');
      return;
    }
    
    try {
      const docId = generatedDocument.data.generated_document_id;
      const primaryClient = Object.values(selectedClientsByRole)[0];
      const clientName = primaryClient?.nome || 'Cliente';
      const templateName = selectedTemplate?.name || 'Documento';
      const docName = `${templateName} - ${clientName}`;

      const response = await infocellApi.getDocumentShareLink(docId);
      const { share_url } = response.data;
      
      // Encurtar a URL antes de compartilhar
      const shortUrl = await shortenUrl(share_url);
      
      const message = `Olá! Segue o documento "${docName}" para visualização: ${shortUrl}`;
      const encodedMessage = encodeURIComponent(message);
      
      const desktopUrl = `whatsapp://send?text=${encodedMessage}`;
      const webUrl = `https://web.whatsapp.com/send?text=${encodedMessage}`;
      
      let fallbackTimeout;

      const cleanup = () => {
        clearTimeout(fallbackTimeout);
        window.removeEventListener('blur', handleBlur);
      };
      
      const handleBlur = () => {
        cleanup();
      };

      window.addEventListener('blur', handleBlur);

      fallbackTimeout = setTimeout(() => {
        console.log("Fallback para WhatsApp Web acionado.");
        window.open(webUrl, '_blank', 'noopener,noreferrer');
        cleanup();
      }, 2500);

      window.location.href = desktopUrl;

    } catch (err) {
      console.error('Erro ao compartilhar:', err);
      setError('Erro ao gerar link de compartilhamento: ' + err.message);
    }
  };

  const handleDownload = async () => {
    if (!generatedDocument?.data?.generated_document_id) {
      setError('ID do documento não encontrado');
      return;
    }
    
    try {
      const response = await infocellApi.downloadDocument(generatedDocument.data.generated_document_id);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      // Criar nome descritivo para o arquivo
      const primaryClient = Object.values(selectedClientsByRole)[0];
      const clientName = primaryClient?.nome || 'Cliente';
      const templateName = selectedTemplate?.name || 'Documento';
      const timestamp = new Date().toISOString().slice(0, 10); // YYYY-MM-DD format
      const fileExtension = outputFormat || 'pdf';
      
      const fileName = `${clientName}_${templateName}_${timestamp}.${fileExtension}`
        .replace(/[/\\:*?"<>|]/g, '_') // Remove caracteres inválidos para nomes de arquivo
        .replace(/\s+/g, '_'); // Substitui espaços por underscores
      
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Erro no download:', err);
      setError('Erro ao fazer download: ' + err.message);
    }
  };

  const resetFlow = () => {
    setCurrentStep(1);
    setSelectedClientsByRole({});
    setSelectedTemplate(null);
    setFormData(null);
    setCustomData('{}');
    setGeneratedDocument(null);
    setError(null);
    setSuccess(null);
    setWillCreateNewClient(false);
    setClientsToCreate([]);
    setCurrentClientSelection('');
  };

  const getDocumentTypeLabel = (type) => {
    const labels = {
      'resume': 'Currículo',
      'contract': 'Contrato',
      'proxy': 'Procuração',
      'other': 'Outro'
    };
    return labels[type] || type;
  };

  const groupTemplatesByType = (templates) => {
    return templates.reduce((groups, template) => {
      const type = template.template_type || 'other';
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(template);
      return groups;
    }, {});
  };

  return (
    <div className="container py-8 max-w-4xl mx-auto">
      <header className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-foreground mb-2">
          Gerar Documento
        </h1>
        <p className="text-muted-foreground">
          Selecione um cliente e template para gerar documentos personalizados
        </p>
      </header>

      {/* Mensagens de Status */}
      {error && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-md flex items-center gap-2">
          <AlertCircle className="h-4 w-4 text-destructive" />
          <p className="text-destructive text-sm">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <p className="text-green-800 text-sm">{success}</p>
        </div>
      )}

      {/* Indicador de Progresso  */}
      <div className="mb-8">
        <div className="flex items-center justify-center space-x-4">
          {[1, 2, 3].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= step 
                  ? 'bg-primary text-primary-foreground' 
                  : 'bg-muted text-muted-foreground'
              }`}>
                {step}
              </div>
              {step < 3 && (
                <div className={`w-16 h-0.5 mx-2 ${
                  currentStep > step ? 'bg-primary' : 'bg-muted'
                }`} />
              )}
            </div>
          ))}
        </div>
        <div className="flex justify-center mt-2">
          <p className="text-sm text-muted-foreground">
            {currentStep === 1 && 'Escolher Template'}
            {currentStep === 2 && `Selecionar ${getCurrentRoleLabel()}`}
            {currentStep === 3 && 'Preencher Dados'}
          </p>
        </div>
      </div>

      {/* Etapa 1: Seleção de Template */}
      {currentStep === 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FolderOpen className="h-5 w-5" />
              Passo 1: Escolher Template
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingTemplates ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-2"></div>
                <span>Carregando templates...</span>
              </div>
            ) : (
              <div className="grid gap-4">
                {Object.entries(groupTemplatesByType(templates)).map(([type, typeTemplates]) => (
                  <div key={type}>
                    <h3 className="font-medium text-sm text-muted-foreground mb-3 uppercase tracking-wide">
                      {getDocumentTypeLabel(type)}
                    </h3>
                    <div className="grid gap-3 mb-6">
                      {typeTemplates.map((template) => (
                        <button
                          key={template._id.$oid}
                          onClick={() => handleTemplateSelect(template)}
                          className={`p-4 border rounded-lg text-left transition-all hover:border-primary hover:shadow-sm ${
                            selectedTemplate?._id.$oid === template._id.$oid 
                              ? 'border-primary bg-primary/5' 
                              : 'border-border'
                          }`}
                        >
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-medium">{template.name}</h4>
                              <p className="text-sm text-muted-foreground mt-1">
                                {template.description || 'Sem descrição'}
                              </p>
                            </div>
                            <FileText className="h-5 w-5 text-muted-foreground" />
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Etapa 2: Seleção de Cliente(s) */}
      {currentStep === 2 && selectedTemplate && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Passo 2: Selecionar {getCurrentRoleLabel()}
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-2">
              Template selecionado: <strong>{selectedTemplate.name}</strong>
            </p>
          </CardHeader>
          <CardContent>
            {/* Busca de cliente */}
            {clientsToCreate.includes(currentClientSelection) ? (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center gap-3 mb-3">
                  <UserPlus className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-blue-900">Novo {getCurrentRoleLabel().toLowerCase()} será criado</p>
                    <p className="text-sm text-blue-700">O cliente será cadastrado automaticamente no ERP</p>
                  </div>
                </div>
                <Button variant="outline" onClick={() => {
                  setClientsToCreate(clientsToCreate.filter(role => role !== currentClientSelection));
                  setWillCreateNewClient(clientsToCreate.filter(role => role !== currentClientSelection).length > 0);
                }}>
                  Cancelar - Buscar cliente existente
                </Button>
              </div>
            ) : (
              <ClientSearchInput
                onSelect={handleClientSelect}
                selectedClient={selectedClientsByRole[currentClientSelection]}
                showCreateOption={true}
                onCreateNewClient={handleCreateNewClient}
              />
            )}

            <div className="flex justify-between mt-6">
              <Button variant="outline" onClick={handlePrevStep}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Voltar
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Etapa 3: Preenchimento de Dados */}
      {currentStep === 3 && selectedTemplate && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Passo 3: Preencher Dados
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-2">
              Template selecionado: <strong>{selectedTemplate.name}</strong>
              {clientsToCreate.length > 0 && (
                <span className="ml-2 text-blue-600">• {clientsToCreate.length} cliente(s) será(ão) criado(s)</span>
              )}
            </p>
          </CardHeader>
          <CardContent>
            {/* Configuração de formato de saída */}
            <div className="mb-6 p-4 border rounded-md bg-green-50 border-green-200">
              <div className="flex items-center gap-4">
                <label className="text-sm font-medium text-green-800">Formato de Saída:</label>
                <select
                  value={outputFormat}
                  onChange={(e) => setOutputFormat(e.target.value)}
                  className="flex h-8 rounded-md border border-input bg-background px-3 py-1 text-sm"
                >
                  {(selectedTemplate.template_type === 'resume' ? RESUME_OUTPUT_FORMATS : OUTPUT_FORMATS).map(format => (
                    <option key={format.value} value={format.value}>
                      {format.label} - {format.description}
                    </option>
                  ))}
                </select>
              </div>
              {selectedTemplate.template_type === 'resume' && (
                <p className="text-sm text-green-700 mt-2">
                  💡 <strong>Currículos são gerados apenas em DOCX</strong> para garantir inserção automática da foto de perfil
                </p>
              )}
            </div>

            {selectedTemplate.template_type === 'resume' ? (
              // Formulário de Currículo
              formData || clientsToCreate.length > 0 ? (
                <div>
                  <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-blue-800 text-sm">
                      <strong>Instruções:</strong> Preencha os dados do currículo abaixo e clique em {`Gerar Currículo`} 
                      para criar o documento usando o template selecionado.
                      {clientsToCreate.length > 0 && (
                        <span className="block mt-1 font-medium">
                          📝 {clientsToCreate.length > 1 ? `Os clientes serão cadastrados` : `O cliente será cadastrado`} automaticamente no ERP usando os dados preenchidos.
                        </span>
                      )}
                    </p>
                  </div>

                  <ResumeForm
                    selectedResume={formData || {
                      personalInfo: {
                        fullName: '',
                        email: '',
                        phone1: '',
                        phone2: '',
                        cpf: '',
                        rg: '',
                        dateOfBirth: '',
                        address: '',
                        city: '',
                        state: '',
                        civilStatus: '',
                        hasChildren: '',
                        levelOfEducation: '',
                        nacionalidade: '',
                        cnh: '',
                        profilePictureFile: null,
                        profilePictureUrl: ''
                      },
                      objective: '',
                      education: [{ course: '', institution: '', startYear: '', endYearOrStatus: '', additionalInfo: '', level: '' }],
                      experience: [{ company: '', startYear: '', endYear: '', role: '', activities: '', localidade: '' }],
                      qualifications: '',
                      additionalInfo: ''
                    }}
                    onCustomGenerate={handleGenerate}
                    isCustomGenerating={isGenerating || isCreatingClient}
                    clientId={selectedClientsByRole.titular?.id}
                  />
                  
                  {(isCreatingClient) && (
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                        <span className="text-blue-800 text-sm font-medium">Criando cliente no ERP...</span>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mr-2"></div>
                  <span>Carregando dados do cliente...</span>
                </div>
              )
            ) : selectedTemplate.template_type === 'proxy' ? (
              // Formulário de Procuração
              <ProcuracaoForm
                initialOutorganteData={selectedClientsByRole.outorgante}
                initialOutorgadoData={selectedClientsByRole.outorgado}
                onSubmit={handleGenerate}
                isGenerating={isGenerating}
              />
            ) : selectedTemplate.template_type === 'contract' ? (
              // Formulário de Contrato de Aluguel
              <ContratoAluguelForm
                initialLocadorData={selectedClientsByRole.locador}
                initialLocatarioData={selectedClientsByRole.locatario}
                onSubmit={handleGenerate}
                isGenerating={isGenerating}
              />
            ) : (
              // Editor JSON para outros tipos de template
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Dados do Template (JSON)
                  </label>
                  <p className="text-xs text-muted-foreground mb-3">
                    Insira os dados que serão utilizados para preencher o template. 
                    Use a sintaxe JSON válida com as variáveis que correspondem ao seu template.
                  </p>
                  <Textarea
                    value={customData}
                    onChange={(e) => setCustomData(e.target.value)}
                    placeholder='{\n  "nome": "João Silva",\n  "empresa": "Empresa XYZ",\n  "data": "2024-01-01"\n}'
                    rows={10}
                    className="font-mono text-sm"
                  />
                </div>

                {/* Controles de navegação e Geração (Unificados) */}
                <div className="mt-8 pt-6 border-t flex justify-between items-center">
                  <Button variant="outline" onClick={handlePrevStep}>
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Voltar
                  </Button>

                  {/* O botão de gerar só aparece se não for um formulário com botão próprio (como o de Currículo) */}
                  {selectedTemplate.template_type !== 'resume' &&
                   selectedTemplate.template_type !== 'proxy' &&
                   selectedTemplate.template_type !== 'contract' &&
                  (
                    <Button
                      onClick={() => handleGenerate()}
                      disabled={isGenerating || isCreatingClient}
                      className="min-w-[150px]"
                    >
                      {(isGenerating || isCreatingClient) ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {isCreatingClient ? 'Criando cliente...' : 'Gerando...'}
                        </>
                      ) : (
                        <>
                          <Zap className="mr-2 h-4 w-4" />
                          {`Gerar ${getDocumentTypeLabel(selectedTemplate.template_type)}`}
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Resultado da Geração */}
      {generatedDocument && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-5 w-5" />
              Documento Gerado com Sucesso
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Seu documento está pronto!</p>
                <p className="text-sm text-muted-foreground">
                  Clique nos botões ao lado para fazer o download, imprimir ou compartilhar
                </p>
              </div>
              <div className="flex gap-3">
                <Button onClick={handleDownload}>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
                <Button variant="outline" onClick={handlePrint} disabled={isPrinting}>
                  {isPrinting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Preparando...
                    </>
                  ) : (
                    <>
                      <Printer className="mr-2 h-4 w-4" />
                      Imprimir
                    </>
                  )}
                </Button>
                <Button variant="outline" onClick={handleShare}>
                  <Share2 className="mr-2 h-4 w-4" />
                  Compartilhar
                </Button>
                <Button variant="outline" onClick={resetFlow}>
                  Gerar Novo
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}