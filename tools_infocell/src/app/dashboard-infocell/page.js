import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { 
  FileText, 
  FolderOpen, 
  Zap, 
  Archive, 
  ShoppingCart,
  Boxes,
  Import,
  Package,
  LineChart,
  Calculator,
  TrendingUp,
  Users
} from "lucide-react";
import Link from "next/link";
import RecentDocuments from "@/components/documents/RecentDocuments";

export default function Page() {
  const rechargeTools = [
    {
      title: "Nova Venda",
      description: "Realize vendas de códigos de recarga com controle de estoque automático.",
      icon: ShoppingCart,
      link: "/dashboard-infocell/recharges/new-sale",
      category: "recarga",
      color: "bg-green-500/10 hover:bg-green-500/20 text-green-600"
    },
    {
      title: "Estoque",
      description: "Visualize e gerencie o estoque de códigos de recarga por fornecedor.",
      icon: Boxes,
      link: "/dashboard-infocell/recharges/stock",
      category: "recarga",
      color: "bg-blue-500/10 hover:bg-blue-500/20 text-blue-600"
    },
    {
      title: "Importar Estoque",
      description: "Importe lotes de códigos de recarga de fornecedores.",
      icon: Import,
      link: "/dashboard-infocell/recharges/import",
      category: "recarga",
      color: "bg-purple-500/10 hover:bg-purple-500/20 text-purple-600"
    },
    {
      title: "Pacotes",
      description: "Configure tipos de pacotes e preços de venda.",
      icon: Package,
      link: "/dashboard-infocell/recharges/packages",
      category: "recarga",
      color: "bg-orange-500/10 hover:bg-orange-500/20 text-orange-600"
    },
    {
      title: "Relatórios",
      description: "Visualize relatórios de vendas e análise de performance.",
      icon: LineChart,
      link: "/dashboard-infocell/recharges/reports",
      category: "recarga",
      color: "bg-red-500/10 hover:bg-red-500/20 text-red-600"
    }
  ];

  const documentTools = [
    {
      title: "Gerar Documento",
      description: "Gere documentos personalizados usando templates e dados de clientes.",
      icon: Zap,
      link: "/dashboard-infocell/gerar-documento",
      category: "documento",
      color: "bg-primary/10 hover:bg-primary/20 text-primary"
    },
    {
      title: "Gerenciar Templates",
      description: "Faça upload e gerencie seus templates de documentos compatíveis com Carbone.js.",
      icon: FolderOpen,
      link: "/dashboard-infocell/templates",
      category: "documento",
      color: "bg-primary/10 hover:bg-primary/20 text-primary"
    },
    {
      title: "Documentos Gerados",
      description: "Visualize e gerencie o histórico de documentos criados no sistema.",
      icon: Archive,
      link: "/dashboard-infocell/documentos",
      category: "documento",
      color: "bg-primary/10 hover:bg-primary/20 text-primary"
    }
  ];

  const utilityTools = [
    {
      title: "Calculadora Parcelamento",
      description: "Calcule parcelamentos e simulações financeiras para clientes.",
      icon: Calculator,
      link: "/dashboard-infocell/calculadora-parcelamento",
      category: "utilitario",
      color: "bg-gray-500/10 hover:bg-gray-500/20 text-gray-600"
    }
  ];

  return (
    <div className="space-y-8">
      <header>
        <h1 className="text-3xl font-bold tracking-tight text-foreground text-center">
          Dashboard Infocell
        </h1>
        
      </header>

      {/* Seção Sistema de Recargas */}
      <section className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-green-500/10 rounded-lg">
            <Zap className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-foreground">Sistema de Recargas</h2>
            <p className="text-sm text-muted-foreground">Gestão completa de vendas e estoque de códigos</p>
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {rechargeTools.map((tool, index) => (
            <Card key={index} className="group hover:shadow-md transition-all duration-200 border shadow">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg transition-colors ${tool.color}`}>
                    <tool.icon className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-base">{tool.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pb-4">
                <CardDescription className="text-sm leading-relaxed">
                  {tool.description}
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link href={tool.link} passHref className="w-full">
                  <Button className="w-full" variant="outline" size="sm">
                    Acessar
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      </section>

      {/* Seção Sistema de Documentos */}
      <section className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-500/10 rounded-lg">
            <FileText className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-foreground">Sistema de Documentos</h2>
            <p className="text-sm text-muted-foreground">Geração e gestão de documentos personalizados</p>
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {documentTools.map((tool, index) => (
            <Card key={index} className="group hover:shadow-md transition-all duration-200 border shadow">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg transition-colors ${tool.color}`}>
                    <tool.icon className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-base">{tool.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pb-4">
                <CardDescription className="text-sm leading-relaxed">
                  {tool.description}
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link href={tool.link} passHref className="w-full">
                  <Button className="w-full" variant="outline" size="sm">
                    Acessar
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      </section>

      {/* Seção Utilitários */}
      <section className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gray-500/10 rounded-lg">
            <Calculator className="h-6 w-6 text-gray-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-foreground">Utilitários</h2>
            <p className="text-sm text-muted-foreground">Ferramentas auxiliares para o negócio</p>
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {utilityTools.map((tool, index) => (
            <Card key={index} className="group hover:shadow-md transition-all duration-200 border shadow">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg transition-colors ${tool.color}`}>
                    <tool.icon className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-base">{tool.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="pb-4">
                <CardDescription className="text-sm leading-relaxed">
                  {tool.description}
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Link href={tool.link} passHref className="w-full">
                  <Button className="w-full" variant="outline" size="sm">
                    Acessar
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      </section>

      {/* Sidebar com documentos recentes */}
      <section className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-purple-500/10 rounded-lg">
            <TrendingUp className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-foreground">Atividade Recente</h2>
            <p className="text-sm text-muted-foreground">Documentos gerados recentemente</p>
          </div>
        </div>
        <RecentDocuments />
      </section>
    </div>
  );
}
