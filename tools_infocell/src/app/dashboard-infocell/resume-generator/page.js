'use client';

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ResumeForm from "@/components/custom/ResumeForm";
import { Trash2 } from 'lucide-react'; // Ícone de lixeira
import { APP_PREFIX } from "@/lib/path-utils";

export default function ResumeGeneratorPage() {
  const [resumes, setResumes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResume, setSelectedResume] = useState(null);
  const [filteredResumes, setFilteredResumes] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [resumeToDelete, setResumeToDelete] = useState(null);
  const [isFormVisible, setIsFormVisible] = useState(false);

  useEffect(() => {
    const fetchResumes = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`${APP_PREFIX}/api/resumes`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setResumes(data);
        setFilteredResumes(data);
      } catch (e) {
        console.error("Error fetching resumes:", e);
        setError(e.message);
      } finally {
        setIsLoading(false);
      }
    };
    fetchResumes();
  }, []);

  useEffect(() => {
    if (searchTerm === '') {
      setFilteredResumes(resumes);
    } else {
      setFilteredResumes(
        resumes.filter(resume =>
          resume.personalInfo.fullName.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
  }, [searchTerm, resumes]);

  const handleSelectResume = (resume) => {
    setSelectedResume(resume);
    setSearchTerm(''); // Limpa a busca após selecionar
  };

  const openDeleteModal = (resume) => {
    setResumeToDelete(resume);
    setShowDeleteModal(true);
  };

  const closeDeleteModal = () => {
    setShowDeleteModal(false);
    setResumeToDelete(null);
  };

  const confirmDeleteResume = async () => {
    if (!resumeToDelete) return;

    const resumeId = resumeToDelete.id;
    closeDeleteModal(); // Fecha o modal imediatamente

    const apiUrl = `${APP_PREFIX}/api/resumes/${resumeId}`;

    try {
      const response = await fetch(apiUrl, {
        method: 'DELETE',
      });

      if (!response.ok) {
        let errorData = { message: `Erro ${response.status}` };
        try {
          errorData = await response.json();
        } catch (jsonError) {
          const textError = await response.text();
          errorData.message = textError || errorData.message;
        }
        throw new Error(errorData.message || 'Erro ao excluir o currículo');
      }
      
      setResumes(prevResumes => {
        const newResumes = prevResumes.filter(r => r.id !== resumeId);
        return newResumes;
      });

      if (selectedResume && selectedResume.id === resumeId) {
        setSelectedResume(null);
      }

      alert('Currículo excluído com sucesso!');

    } catch (error) {
      console.error('[Frontend] Erro durante a exclusão:', error);
      alert(`Erro ao excluir currículo: ${error.message}`);
    }
  };

  const handleDownload = (resume) => {
    if (resume.filePath) {
      window.open(resume.filePath, '_blank');
    } else {
      alert("Arquivo não encontrado para download.");
    }
  };

  return (
    <div className="container py-8 max-w-4xl mx-auto">
      {/* Modal de Confirmação de Exclusão */}
      {showDeleteModal && resumeToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Confirmar Exclusão</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Tem certeza que deseja excluir o currículo de <span className="font-semibold">{resumeToDelete.personalInfo.fullName}</span>?
                Esta ação não pode ser desfeita.
              </p>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={closeDeleteModal}>
                  Cancelar
                </Button>
                <Button variant="destructive" onClick={confirmDeleteResume}>
                  Excluir
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <header className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-foreground mb-2">
          Gerador de Currículo
        </h1>
        <p className="text-muted-foreground">
          Preencha os dados para gerar um currículo ou busque um existente para editar ou excluir.
        </p>
      </header>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Buscar Currículo Existente</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4">
            <div className="flex items-center space-x-2">
              <select
                value={selectedResume ? selectedResume.id : ""}
                onChange={(e) => {
                  const resumeId = e.target.value;
                  if (resumeId) {
                    const resumeToSelect = resumes.find(r => r.id.toString() === resumeId);
                    if (resumeToSelect) {
                      handleSelectResume(resumeToSelect);
                    }
                  } else {
                    setSelectedResume(null); // Limpa o formulário
                  }
                }}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 flex-grow"
              >
                <option value="">-- Novo Currículo / Selecione --</option>
                {/* Alterado para usar filteredResumes para que a busca reflita no select */}
                {filteredResumes.map((resume) => (
                  <option key={resume.id} value={resume.id}>
                    {resume.personalInfo.fullName}
                    {resume.createdAt && (
                      ` (${new Date(resume.createdAt).toLocaleDateString()} ${new Date(resume.createdAt).toLocaleTimeString()})`
                    )}
                  </option>
                ))}
              </select>
              {selectedResume && (
                <>
                  <Button variant="outline" size="icon" onClick={() => {setSelectedResume(null); setSearchTerm('');}} aria-label="Limpar seleção">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                  </Button>
                  <Button variant="destructive" size="icon" onClick={() => openDeleteModal(selectedResume)} aria-label="Excluir currículo selecionado">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </>
              )}
            </div>
            <Input
              type="text"
              placeholder="Filtrar lista por nome..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            {/* O select agora usa filteredResumes, melhorando a UX da busca. */}
          </div>
        </CardContent>
      </Card>

      <ResumeForm selectedResume={selectedResume} />
    </div>
  );
}
