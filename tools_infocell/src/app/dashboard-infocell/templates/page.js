'use client';

import { useState, useEffect, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, FileText, Trash2, Edit3, Check, X, Plus, Download } from "lucide-react";
import infocellApi from "@/lib/infocellApi";

const DOCUMENT_TYPES = [
  { value: 'resume', label: 'Currículo', icon: FileText },
  { value: 'contract', label: 'Contrato', icon: FileText },
  { value: 'proxy', label: 'Procuração', icon: FileText },
];

export default function TemplatesPage() {
  const [templates, setTemplates] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [editingTemplate, setEditingTemplate] = useState(null);
  
  // Estado do formulário de upload
  const [uploadForm, setUploadForm] = useState({
    name: '',
    description: '',
    template_type: 'resume',
    file: null
  });
  
  const fileInputRef = useRef(null);
  const dropZoneRef = useRef(null);

  // Carregar templates ao montar o componente
  useEffect(() => {
    loadTemplates();
  }, []);

  // Limpar mensagens após 5 segundos
  useEffect(() => {
    if (success || error) {
      const timer = setTimeout(() => {
        setSuccess(null);
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success, error]);

  const loadTemplates = async () => {
    try {
      setIsLoading(true);
      const response = await infocellApi.getTemplates();
      
      // Verifica se response.data e response.data.templates existem
      const templatesData = response?.data?.templates;

      if (Array.isArray(templatesData)) {
        const mappedTemplates = templatesData.map(template => ({
          ...template,
          id: template._id?.$oid || template.id, // Mapeia _id.$oid para id ou usa template.id se já existir
        }));
        setTemplates(mappedTemplates);
      } else {
        console.warn('Formato de resposta inesperado ou templates ausentes:', response);
        setTemplates([]); // Define como array vazio se os dados não estiverem no formato esperado
      }
      setError(null);
    } catch (err) {
      console.error('Erro ao carregar templates:', err);
      setError('Erro ao carregar templates: ' + err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileSelect = (file) => {
    // Validar tipo de arquivo
    const allowedTypes = ['application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword'];
    const maxSize = parseInt(process.env.NEXT_PUBLIC_MAX_TEMPLATE_SIZE) || 10485760; // 10MB default

    if (!allowedTypes.includes(file.type)) {
      setError('Tipo de arquivo não suportado. Use apenas arquivos .docx ou .doc');
      return false;
    }

    if (file.size > maxSize) {
      setError(`Arquivo muito grande. Tamanho máximo: ${Math.round(maxSize / 1024 / 1024)}MB`);
      return false;
    }

    setUploadForm(prev => ({ ...prev, file }));
    setError(null);
    return true;
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    dropZoneRef.current?.classList.add('border-primary');
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    dropZoneRef.current?.classList.remove('border-primary');
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    dropZoneRef.current?.classList.remove('border-primary');
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUpload = async (e) => {
    e.preventDefault();
    
    if (!uploadForm.file) {
      setError('Selecione um arquivo para upload');
      return;
    }

    if (!uploadForm.name.trim()) {
      setError('Nome do template é obrigatório');
      return;
    }

    try {
      setIsUploading(true);
      setError(null);

      await infocellApi.uploadTemplate({
        name: uploadForm.name.trim(),
        description: uploadForm.description.trim(),
        template_type: uploadForm.template_type,
        file: uploadForm.file
      });

      setSuccess('Template enviado com sucesso!');
      setUploadForm({
        name: '',
        description: '',
        template_type: 'resume',
        file: null
      });
      
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      await loadTemplates();
    } catch (err) {
      console.error('Erro no upload:', err);
      setError('Erro ao enviar template: ' + err.message);
    } finally {
      setIsUploading(false);
    }
  };

  const handleUpdateTemplate = async (templateId, updateData) => {
    try {
      await infocellApi.updateTemplate(templateId, updateData);
      setSuccess('Template atualizado com sucesso!');
      setEditingTemplate(null);
      await loadTemplates();
    } catch (err) {
      console.error('Erro ao atualizar template:', err);
      setError('Erro ao atualizar template: ' + err.message);
    }
  };

  const handleDeleteTemplate = async (template) => {
    if (!confirm(`Tem certeza que deseja excluir o template "${template.name}"? Esta ação não pode ser desfeita.`)) {
      return;
    }

    try {
      await infocellApi.deleteTemplate(template.id);
      setSuccess('Template excluído com sucesso!');
      await loadTemplates();
    } catch (err) {
      console.error('Erro ao excluir template:', err);
      setError('Erro ao excluir template: ' + err.message);
    }
  };

  const handleDownloadTemplate = async (template) => {
    try {
      setError(null);
      const response = await infocellApi.downloadTemplate(template.id);
      const blob = await response.blob();
      
      // Criar URL temporária para download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = template.file_name || `${template.name}.docx`;
      document.body.appendChild(a);
      a.click();
      
      // Limpeza
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      setSuccess(`Template "${template.name}" baixado com sucesso!`);
    } catch (err) {
      console.error('Erro ao baixar template:', err);
      setError('Erro ao baixar template: ' + err.message);
    }
  };

  const getDocumentTypeLabel = (type) => {
    const docType = DOCUMENT_TYPES.find(dt => dt.value === type);
    return docType ? docType.label : type;
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container py-8 max-w-6xl mx-auto">
      <header className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-foreground mb-2">
          Gerenciar Templates
        </h1>
        <p className="text-muted-foreground">
          Faça upload e gerencie seus templates de documentos compatíveis com Carbone.js
        </p>
      </header>

      {/* Mensagens de Status */}
      {error && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
          <p className="text-destructive text-sm">{error}</p>
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-800 text-sm">{success}</p>
        </div>
      )}

      {/* Formulário de Upload */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Novo Template
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleUpload} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Nome do Template *</label>
                <Input
                  value={uploadForm.name}
                  onChange={(e) => setUploadForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Ex: Currículo Padrão Infocell"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Tipo de Documento</label>
                <select
                  value={uploadForm.template_type}
                  onChange={(e) => setUploadForm(prev => ({ ...prev, template_type: e.target.value }))}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  {DOCUMENT_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Descrição</label>
              <Textarea
                value={uploadForm.description}
                onChange={(e) => setUploadForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Descrição opcional do template..."
                rows={2}
              />
            </div>

            {/* Drag & Drop Zone */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Arquivo do Template *</label>
              <div
                ref={dropZoneRef}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-primary/50 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                {uploadForm.file ? (
                  <div>
                    <p className="text-sm font-medium text-foreground">{uploadForm.file.name}</p>
                    <p className="text-xs text-muted-foreground">{formatFileSize(uploadForm.file.size)}</p>
                  </div>
                ) : (
                  <div>
                    <p className="text-sm font-medium text-foreground mb-2">
                      Arraste seu template DOCX aqui ou clique para selecionar
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Arquivos suportados: .docx, .doc | Tamanho máximo: 10MB
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Use a sintaxe {`{{variavel}}`} para campos dinâmicos
                    </p>
                  </div>
                )}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".docx,.doc"
                  onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
                  className="hidden"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" disabled={isUploading || !uploadForm.file || !uploadForm.name.trim()}>
                {isUploading ? (
                  <>
                    <Upload className="mr-2 h-4 w-4 animate-spin" />
                    Enviando...
                  </>
                ) : (
                  <>
                    <Upload className="mr-2 h-4 w-4" />
                    Enviar Template
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Lista de Templates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Templates Disponíveis
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Carregando templates...</span>
            </div>
          ) : templates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="mx-auto h-12 w-12 mb-4" />
              <p>Nenhum template encontrado</p>
              <p className="text-sm">Faça upload do seu primeiro template acima</p>
            </div>
          ) : (
            <div className="space-y-4">
              {templates.map((template) => (
                <TemplateCard
                  key={template.id}
                  template={template}
                  editingTemplate={editingTemplate}
                  setEditingTemplate={setEditingTemplate}
                  onUpdate={handleUpdateTemplate}
                  onDelete={handleDeleteTemplate}
                  onDownload={handleDownloadTemplate}
                  getDocumentTypeLabel={getDocumentTypeLabel}
                  formatDate={formatDate}
                  formatFileSize={formatFileSize}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Componente do card de template
function TemplateCard({ 
  template, 
  editingTemplate, 
  setEditingTemplate, 
  onUpdate, 
  onDelete, 
  onDownload, 
  getDocumentTypeLabel,
  formatDate,
  formatFileSize 
}) {
  const [editForm, setEditForm] = useState({
    name: template.name,
    description: template.description || '',
    is_active: template.is_active
  });

  const isEditing = editingTemplate === template.id;

  const handleSave = () => {
    onUpdate(template.id, editForm);
  };

  const handleCancel = () => {
    setEditForm({
      name: template.name,
      description: template.description || '',
      is_active: template.is_active
    });
    setEditingTemplate(null);
  };

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex-1 min-w-0">
        {isEditing ? (
          <div className="space-y-2">
            <Input
              value={editForm.name}
              onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
              className="font-medium"
            />
            <Textarea
              value={editForm.description}
              onChange={(e) => setEditForm(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Descrição..."
              rows={2}
            />
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={editForm.is_active}
                onChange={(e) => setEditForm(prev => ({ ...prev, is_active: e.target.checked }))}
                className="rounded"
              />
              <span className="text-sm">Template ativo</span>
            </div>
          </div>
        ) : (
          <div>
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-medium text-foreground truncate">{template.name}</h3>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                template.is_active 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {template.is_active ? 'Ativo' : 'Inativo'}
              </span>
            </div>
            {template.description && (
              <p className="text-sm text-muted-foreground mb-2">{template.description}</p>
            )}
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span>Tipo: {getDocumentTypeLabel(template.template_type)}</span>
              <span>Arquivo: {template.file_name}</span>
              <span>Tamanho: {formatFileSize(template.file_size)}</span>
              <span>Criado: {formatDate(template.created_at)}</span>
            </div>
          </div>
        )}
      </div>

      <div className="flex items-center gap-2 ml-4">
        {isEditing ? (
          <>
            <Button size="sm" variant="outline" onClick={handleSave}>
              <Check className="h-4 w-4" />
            </Button>
            <Button size="sm" variant="outline" onClick={handleCancel}>
              <X className="h-4 w-4" />
            </Button>
          </>
        ) : (
          <>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setEditingTemplate(template.id)}
            >
              <Edit3 className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onDelete(template)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => onDownload(template)}
            >
              <Download className="h-4 w-4" />
            </Button>
          </>
        )}
      </div>
    </div>
  );
}