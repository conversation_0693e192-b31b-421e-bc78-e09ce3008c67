"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search } from "lucide-react";
import infocellApi from '@/lib/infocellApi';
import { useDebounce } from '@/lib/hooks/useDebounce';
import SupplierSearchInput from '@/components/custom/SupplierSearchInput';

export default function RechargeStockPage() {
    const [stock, setStock] = useState([]);
    const [packages, setPackages] = useState([]);
    const [suppliers, setSuppliers] = useState([]);
    const [suppliersMap, setSuppliersMap] = useState(new Map());
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({ page: 1, per_page: 20, total: 0 });
    const [filters, setFilters] = useState({
        package_type: 'all',
        is_available: 'all',
        search_code: '',
        erp_supplier_id: '',
        sort_by: 'imported_at',
        sort_order: 'desc'
    });
    const [selectedSupplier, setSelectedSupplier] = useState(null);

    const debouncedFilters = useDebounce(filters, 500);

    const fetchStock = useCallback(async (page = 1) => {
        setIsLoading(true);
        setError(null);
        try {
            const apiParams = { page, per_page: pagination.per_page };
            if (debouncedFilters.package_type !== 'all') {
                apiParams.package_type = debouncedFilters.package_type;
            }
            if (debouncedFilters.is_available !== 'all') {
                apiParams.is_available = debouncedFilters.is_available === 'true';
            }
            if (debouncedFilters.search_code) {
                apiParams.search_code = debouncedFilters.search_code;
            }
            if (debouncedFilters.erp_supplier_id) {
                apiParams.erp_supplier_id = debouncedFilters.erp_supplier_id;
            }
            if (debouncedFilters.sort_by) {
                apiParams.sort_by = debouncedFilters.sort_by;
                apiParams.sort_order = debouncedFilters.sort_order;
            }

            const response = await infocellApi.getRechargeStock(apiParams);
            setStock(response.data.codes || []);
            setPagination(prev => ({ ...prev, total: response.data.total, page }));
        } catch (err) {
            setError('Falha ao buscar estoque.');
            console.error(err);
        } finally {
            setIsLoading(false);
        }
    }, [pagination.per_page, debouncedFilters]);

    useEffect(() => {
        fetchStock(1);
    }, [fetchStock]);

    useEffect(() => {
        const fetchPackages = async () => {
            try {
                const response = await infocellApi.getRechargePackages();
                setPackages(response.data || []);
            } catch (err) {
                console.error("Failed to fetch packages for filter");
            }
        };
        fetchPackages();
    }, []);

    useEffect(() => {
        const fetchSuppliers = async () => {
            try {
                // Buscar uma quantidade maior de fornecedores para criar o mapa
                const response = await infocellApi.getSuppliers({ per_page: 1000 });
                const suppliersData = response.data || [];
                setSuppliers(suppliersData);
                
                // Criar mapa para busca rápida por ID
                const map = new Map();
                suppliersData.forEach(supplier => {
                    map.set(supplier.id, supplier.nome);
                });
                setSuppliersMap(map);
            } catch (err) {
                console.error("Failed to fetch suppliers:", err);
            }
        };
        fetchSuppliers();
    }, []);
    
    const handleFilterChange = (name, value) => {
        setFilters(prev => ({ ...prev, [name]: value }));
    };

    const handleSupplierSelect = (supplier) => {
        setSelectedSupplier(supplier);
        handleFilterChange('erp_supplier_id', supplier ? supplier.id : '');
    };

    const getSupplierName = (supplierId) => {
        if (!supplierId) return 'N/A';
        return suppliersMap.get(supplierId) || 'Fornecedor não encontrado';
    };

    const handleSortChange = (column) => {
        setFilters(prev => ({
            ...prev,
            sort_by: column,
            sort_order: prev.sort_by === column && prev.sort_order === 'asc' ? 'desc' : 'asc'
        }));
    };

    const getSortIcon = (column) => {
        if (filters.sort_by !== column) return '↕️';
        return filters.sort_order === 'asc' ? '↑' : '↓';
    };

    const handlePageChange = (newPage) => {
        if (newPage > 0 && newPage <= Math.ceil(pagination.total / pagination.per_page)) {
            fetchStock(newPage);
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('pt-BR');
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Estoque de Códigos de Recarga</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                    <div>
                        <Label htmlFor="search_code">Buscar por Código</Label>
                        <div className="relative">
                            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                id="search_code"
                                placeholder="Digite o código..."
                                className="pl-8"
                                value={filters.search_code}
                                onChange={(e) => handleFilterChange('search_code', e.target.value)}
                            />
                        </div>
                    </div>

                    <div>
                        <Label>Filtrar por Fornecedor</Label>
                        <SupplierSearchInput 
                            onSelect={handleSupplierSelect} 
                            selectedSupplier={selectedSupplier}
                        />
                    </div>

                    <div>
                        <Label>Filtrar por Pacote</Label>
                        <Select onValueChange={(value) => handleFilterChange('package_type', value)} defaultValue="all">
                            <SelectTrigger>
                                <SelectValue placeholder="Filtrar por pacote" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Todos os Pacotes</SelectItem>
                                {packages.map(p => <SelectItem key={p._id.$oid} value={p.package_type}>{p.package_name}</SelectItem>)}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <Label>Filtrar por Status</Label>
                        <Select onValueChange={(value) => handleFilterChange('is_available', value)} defaultValue="all">
                            <SelectTrigger>
                                <SelectValue placeholder="Filtrar por status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">Todos os Status</SelectItem>
                                <SelectItem value="true">Disponível</SelectItem>
                                <SelectItem value="false">Vendido</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('code')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Código</span>
                                        <span className="text-xs">{getSortIcon('code')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('package_type')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Tipo</span>
                                        <span className="text-xs">{getSortIcon('package_type')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('unit_cost')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Custo</span>
                                        <span className="text-xs">{getSortIcon('unit_cost')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('erp_supplier_id')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Fornecedor</span>
                                        <span className="text-xs">{getSortIcon('erp_supplier_id')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('imported_at')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Importado em</span>
                                        <span className="text-xs">{getSortIcon('imported_at')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('is_available')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Status</span>
                                        <span className="text-xs">{getSortIcon('is_available')}</span>
                                    </div>
                                </TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {isLoading ? (
                                <TableRow><TableCell colSpan="6" className="text-center">Carregando...</TableCell></TableRow>
                            ) : stock.map((item) => (
                                <TableRow key={item._id.$oid}>
                                    <TableCell className="font-mono">{item.code}</TableCell>
                                    <TableCell>{item.package_type}</TableCell>
                                    <TableCell>R$ {item.unit_cost.toFixed(2)}</TableCell>
                                    <TableCell>{getSupplierName(item.erp_supplier_id)}</TableCell>
                                    <TableCell>{formatDate(item.imported_at)}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${item.is_available ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                            {item.is_available ? 'Disponível' : 'Vendido'}
                                        </span>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
                
                <div className="flex items-center justify-between mt-4">
                    <span className="text-sm text-muted-foreground">
                        Total de {pagination.total} registros.
                    </span>
                    <div className="flex space-x-2">
                        <Button onClick={() => handlePageChange(pagination.page - 1)} disabled={pagination.page <= 1}>
                            Anterior
                        </Button>
                        <span className="p-2">{pagination.page} de {Math.ceil(pagination.total / pagination.per_page)}</span>
                        <Button onClick={() => handlePageChange(pagination.page + 1)} disabled={pagination.page >= Math.ceil(pagination.total / pagination.per_page)}>
                            Próximo
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
} 