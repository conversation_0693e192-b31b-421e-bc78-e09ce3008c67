"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { CalendarDays, Download, Search, Filter, X, AlertCircle, ChevronDown, ChevronRight, FileText, DollarSign, TrendingUp, ShoppingCart, Target } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import RechargeReceipt from '@/components/custom/RechargeReceipt';
import ClientSearchInput from '@/components/custom/ClientSearchInput';
import EmployeeSearchInput from '@/components/custom/EmployeeSearchInput';
import infocellApi from '@/lib/infocellApi';
import { toast } from 'sonner';
import { useDebounce } from '@/lib/hooks/useDebounce';
import { formatBsonDate } from '@/lib/utils';

const ReportsPage = () => {
    const [filters, setFilters] = useState({
        page: 1,
        per_page: 25,
        erp_client_id: '',
        erp_employee_id: '',
        start_date: '',
        end_date: '',
        payment_method: '',
        min_value: '',
        max_value: '',
        search_text: '',
        sort_by: 'sale_timestamp',
        sort_order: 'desc'
    });
    const [sales, setSales] = useState([]);
    const [totalSales, setTotalSales] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [packagesMap, setPackagesMap] = useState(new Map());
    const [packagesData, setPackagesData] = useState([]);
    const [employeesMap, setEmployeesMap] = useState(new Map());
    const [clientsMap, setClientsMap] = useState(new Map());
    const [expandedSales, setExpandedSales] = useState(new Set());
    const [selectedSaleForReceipt, setSelectedSaleForReceipt] = useState(null);
    const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);


    const debouncedFilters = useDebounce(filters, 500);

    // Funções auxiliares para filtros de data
    const getDatePresets = () => {
        // Usar data local para evitar problemas de fuso horário
        const formatLocalDate = (date) => {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        };

        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        const lastWeek = new Date(today);
        lastWeek.setDate(lastWeek.getDate() - 7);

        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);

        return {
            today: {
                start_date: formatLocalDate(today),
                end_date: formatLocalDate(today)
            },
            yesterday: {
                start_date: formatLocalDate(yesterday),
                end_date: formatLocalDate(yesterday)
            },
            lastWeek: {
                start_date: formatLocalDate(lastWeek),
                end_date: formatLocalDate(today)
            },
            lastMonth: {
                start_date: formatLocalDate(lastMonth),
                end_date: formatLocalDate(today)
            }
        };
    };

    const applyDatePreset = (preset) => {
        const presets = getDatePresets();
        console.log('🔍 Applying preset:', preset, 'with dates:', presets[preset]);

        // Debug: verificar se há vendas sem filtro primeiro
        if (preset === 'today') {
            console.log('🔍 Testing: fetching all sales without date filter first...');
            infocellApi.getSalesReports({ page: 1, per_page: 5 }).then(response => {
                console.log('🔍 All sales (no filter):');
                response.data.sales.forEach((s, i) => {
                    const timestamp = s.sale.sale_timestamp;
                    
                    
                });
            });
        }

        if (presets[preset]) {
            setFilters(prev => ({
                ...prev,
                ...presets[preset],
                page: 1
            }));
        }
    };

    useEffect(() => {
        const fetchInitialData = async () => {
            try {
                const [packagesResponse, employeesResponse] = await Promise.all([
                    infocellApi.getRechargePackages(),
                    infocellApi.getEmployees({ per_page: 500 }) // Busca um número grande de funcionários
                ]);
                
                const newPackagesMap = new Map(packagesResponse.data.map(pkg => [pkg.package_type, pkg.package_name]));
                setPackagesMap(newPackagesMap);
                setPackagesData(packagesResponse.data);

                const newEmployeesMap = new Map(employeesResponse.data.map(emp => [
                    emp.id || emp._id?.$oid || emp._id,
                    emp.nome
                ]));
                setEmployeesMap(newEmployeesMap);

            } catch (error) {
                toast.error("Falha ao carregar dados iniciais (pacotes ou vendedores).");
            }
        };
        fetchInitialData();
    }, []);

    const fetchReports = useCallback(async (currentFilters) => {
        setIsLoading(true);
        try {
            const params = { ...currentFilters };

            // Clean empty filters
            if (!params.erp_client_id) delete params.erp_client_id;
            if (!params.erp_employee_id) delete params.erp_employee_id;
            if (!params.payment_method) delete params.payment_method;
            if (!params.min_value) delete params.min_value;
            if (!params.max_value) delete params.max_value;
            if (!params.search_text) delete params.search_text;

            // Garante que parâmetros vazios não sejam enviados
            if (!params.start_date) delete params.start_date;
            if (!params.end_date) delete params.end_date;

            console.log('🔍 Final params:', params);

            const response = await infocellApi.getSalesReports(params);
            const salesData = response.data.sales;

            console.log('🔍 API Response:', {
                total: response.data.total,
                salesCount: salesData.length,
                firstSale: salesData[0]?.sale?.sale_timestamp,
                params: params
            });

            setSales(salesData);
            setTotalSales(response.data.total);

            // Fetch client names for new sales
            const newClientsMap = new Map(clientsMap);
            for (const sale of salesData) {
                const clientId = sale.sale?.erp_client_id || sale.erp_client_id;
                if (!newClientsMap.has(clientId)) {
                    try {
                        const clientResponse = await infocellApi.getClient(clientId);
                        const clientName = clientResponse.data?.nome || clientResponse.nome || 'Nome não encontrado';
                        newClientsMap.set(clientId, clientName);
                    } catch (e) {
                        console.error(`Falha ao buscar cliente ${clientId}`, e);
                        newClientsMap.set(clientId, 'Não encontrado');
                    }
                }
            }
            setClientsMap(newClientsMap);



        } catch (error) {
            toast.error(`Falha ao buscar relatórios: ${error.message}`);
        } finally {
            setIsLoading(false);
        }
    }, [toast]);

    useEffect(() => {
        fetchReports(debouncedFilters);
    }, [debouncedFilters, fetchReports]);

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value, page: 1 }));
    };

    const handleClientSelect = (client) => {
        handleFilterChange('erp_client_id', client ? client.id : '');
    };

    const handleEmployeeSelect = (employee) => {
        handleFilterChange('erp_employee_id', employee ? employee.id : '');
    };

    const handleSortChange = (column) => {
        setFilters(prev => ({
            ...prev,
            sort_by: column,
            sort_order: prev.sort_by === column && prev.sort_order === 'asc' ? 'desc' : 'asc',
            page: 1
        }));
    };

    const clearAllFilters = () => {
        setFilters({
            page: 1,
            per_page: 25,
            erp_client_id: '',
            erp_employee_id: '',
            start_date: '',
            end_date: '',
            payment_method: '',
            min_value: '',
            max_value: '',
            search_text: '',
            sort_by: 'sale_timestamp',
            sort_order: 'desc'
        });
    };

    const summary = useMemo(() => {
        return sales.reduce((acc, item) => acc + item.sale.total_sale_price, 0);
    }, [sales]);

    const totalCost = useMemo(() => {
        return sales.reduce((acc, item) => acc + (item.total_cost || 0), 0);
    }, [sales]);

    const profit = useMemo(() => {
        return summary - totalCost;
    }, [summary, totalCost]);

    const totalPages = Math.ceil(totalSales / filters.per_page);

    const getSortIcon = (column) => {
        if (filters.sort_by !== column) return '↕️';
        return filters.sort_order === 'asc' ? '↑' : '↓';
    };

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: 'BRL'
        }).format(value);
    };

    // Função para gerar badges dos filtros ativos
    const getActiveFilters = () => {
        const activeFilters = [];

        if (filters.start_date || filters.end_date) {
            let dateText = 'Período: ';
            // Função para formatar data corretamente evitando problemas de fuso horário
            const formatDisplayDate = (dateStr) => {
                const [year, month, day] = dateStr.split('-');
                return new Date(year, month - 1, day).toLocaleDateString('pt-BR');
            };

            if (filters.start_date && filters.end_date) {
                dateText += `${formatDisplayDate(filters.start_date)} até ${formatDisplayDate(filters.end_date)}`;
            } else if (filters.start_date) {
                dateText += `a partir de ${formatDisplayDate(filters.start_date)}`;
            } else {
                dateText += `até ${formatDisplayDate(filters.end_date)}`;
            }
            activeFilters.push({
                key: 'date',
                label: dateText,
                onRemove: () => {
                    setFilters(prev => ({ ...prev, start_date: '', end_date: '', page: 1 }));
                }
            });
        }

        if (filters.payment_method) {
            activeFilters.push({
                key: 'payment',
                label: `Pagamento: ${filters.payment_method}`,
                onRemove: () => {
                    setFilters(prev => ({ ...prev, payment_method: '', page: 1 }));
                }
            });
        }

        if (filters.search_text) {
            activeFilters.push({
                key: 'search',
                label: `Busca: "${filters.search_text}"`,
                onRemove: () => {
                    setFilters(prev => ({ ...prev, search_text: '', page: 1 }));
                }
            });
        }

        if (filters.erp_client_id) {
            activeFilters.push({
                key: 'client',
                label: 'Cliente selecionado',
                onRemove: () => {
                    setFilters(prev => ({ ...prev, erp_client_id: '', page: 1 }));
                }
            });
        }

        if (filters.erp_employee_id) {
            activeFilters.push({
                key: 'employee',
                label: 'Vendedor selecionado',
                onRemove: () => {
                    setFilters(prev => ({ ...prev, erp_employee_id: '', page: 1 }));
                }
            });
        }

        return activeFilters;
    };

    const exportToPDF = async () => {
        try {
            // Importação dinâmica para evitar problemas de SSR
            const { default: jsPDF } = await import('jspdf');
            const { default: autoTable } = await import('jspdf-autotable');

            const doc = new jsPDF();

            // Cabeçalho do relatório
            doc.setFontSize(18);
            doc.text('Relatório de Vendas de Recargas', 14, 22);

            doc.setFontSize(12);
            doc.text(`Gerado em: ${new Date().toLocaleString('pt-BR')}`, 14, 32);

            // Informações dos filtros aplicados
            let filterInfo = [];
            if (filters.start_date) filterInfo.push(`Data inicial: ${new Date(filters.start_date).toLocaleDateString('pt-BR')}`);
            if (filters.end_date) filterInfo.push(`Data final: ${new Date(filters.end_date).toLocaleDateString('pt-BR')}`);
            if (filters.payment_method) filterInfo.push(`Pagamento: ${filters.payment_method}`);
            if (filters.search_text) filterInfo.push(`Busca: ${filters.search_text}`);

            if (filterInfo.length > 0) {
                doc.setFontSize(10);
                doc.text('Filtros aplicados:', 14, 42);
                filterInfo.forEach((info, index) => {
                    doc.text(`• ${info}`, 14, 48 + (index * 6));
                });
            }

            // Resumo
            const startY = filterInfo.length > 0 ? 60 + (filterInfo.length * 6) : 50;
            doc.setFontSize(12);
            doc.text(`Total Arrecadado: ${formatCurrency(summary)}`, 14, startY);
            doc.text(`Número de Transações: ${sales.length}`, 14, startY + 8);
            doc.text(`Total de Custo: ${formatCurrency(totalCost)}`, 14, startY + 16);
            doc.text(`Lucro Total: ${formatCurrency(profit)}`, 14, startY + 24);
            if (summary > 0) {
                doc.text(`Margem de Lucro: ${((profit / summary) * 100).toFixed(1)}%`, 14, startY + 32);
            }

            // Preparar dados da tabela
            const tableData = sales.map(item => [
                formatBsonDate(item.sale.sale_timestamp, { day: '2-digit', month: '2-digit', year: 'numeric' }),
                clientsMap.get(item.sale.erp_client_id) || item.sale.erp_client_id,
                employeesMap.get(item.sale.erp_employee_id) || item.sale.erp_employee_id,
                packagesMap.get(item.sale.package_type) || item.sale.package_type,
                item.sale.quantity.toString(),
                formatCurrency(item.sale.total_sale_price),
                item.sale.payment_method
            ]);

            // Criar tabela
            autoTable(doc, {
                head: [['Data', 'Cliente', 'Vendedor', 'Pacote', 'Qtd', 'Valor', 'Pagamento']],
                body: tableData,
                startY: startY + 45,
                styles: {
                    fontSize: 8,
                    cellPadding: 2
                },
                headStyles: {
                    fillColor: [66, 139, 202],
                    textColor: 255
                },
                columnStyles: {
                    0: { cellWidth: 20 }, // Data
                    1: { cellWidth: 35 }, // Cliente
                    2: { cellWidth: 30 }, // Vendedor
                    3: { cellWidth: 25 }, // Pacote
                    4: { cellWidth: 15 }, // Qtd
                    5: { cellWidth: 25 }, // Valor
                    6: { cellWidth: 25 }  // Pagamento
                }
            });

            // Salvar o PDF
            const fileName = `relatorio-vendas-${new Date().toISOString().split('T')[0]}.pdf`;
            doc.save(fileName);

            toast.success('Relatório PDF exportado com sucesso!');
        } catch (error) {
            console.error('Erro ao exportar PDF:', error);

            // Fallback: exportar como CSV se PDF não funcionar
            try {
                const csvContent = [
                    ['Data', 'Cliente', 'Vendedor', 'Pacote', 'Quantidade', 'Valor Total', 'Pagamento'],
                    ...sales.map(sale => [
                        formatBsonDate(sale.sale_timestamp, { day: '2-digit', month: '2-digit', year: 'numeric' }),
                        clientsMap.get(sale.erp_client_id) || sale.erp_client_id,
                        employeesMap.get(sale.erp_employee_id) || sale.erp_employee_id,
                        packagesMap.get(sale.package_type) || sale.package_type,
                        sale.quantity,
                        `R$ ${sale.total_sale_price.toFixed(2)}`,
                        sale.payment_method
                    ])
                ].map(row => row.join(';')).join('\n');

                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `relatorio-vendas-${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                toast.success('Relatório exportado como CSV (instale jspdf e jspdf-autotable para PDF)');
            } catch (csvError) {
                toast.error('Erro ao exportar relatório. Instale as dependências: pnpm add jspdf jspdf-autotable');
            }
        }
    };

    // Função para alternar expansão de uma venda
    const toggleSaleExpansion = (saleId) => {
        setExpandedSales(prev => {
            const newSet = new Set(prev);
            if (newSet.has(saleId)) {
                newSet.delete(saleId);
            } else {
                newSet.add(saleId);
            }
            return newSet;
        });
    };

    // Função para abrir modal de recibo
    const openReceiptModal = async (sale) => {
        // Garantir que o nome do cliente esteja carregado
        const saleData = sale.sale || sale;
        if (!clientsMap.has(saleData.erp_client_id)) {
            try {
                const clientResponse = await infocellApi.getClient(saleData.erp_client_id);
                const clientName = clientResponse.data?.nome || clientResponse.nome || 'Nome não encontrado';
                setClientsMap(prev => new Map(prev).set(saleData.erp_client_id, clientName));
            } catch (e) {
                console.error(`Falha ao buscar cliente ${saleData.erp_client_id}`, e);
                setClientsMap(prev => new Map(prev).set(saleData.erp_client_id, 'Cliente não encontrado'));
            }
        }

        setSelectedSaleForReceipt(sale);
        setIsReceiptModalOpen(true);
    };

    // Função para fechar modal de recibo
    const closeReceiptModal = () => {
        setSelectedSaleForReceipt(null);
        setIsReceiptModalOpen(false);
    };

    // Função para obter dados completos do pacote
    const getPackageData = (packageType, codes = []) => {
        const packageData = packagesData.find(pkg => pkg.package_type === packageType);

        // Tentar obter validity_days dos códigos vendidos
        let validityDays = 30; // Fallback padrão
        if (codes.length > 0 && codes[0].validity_days) {
            validityDays = codes[0].validity_days;
        }

        return {
            package_name: packageData?.package_name || packagesMap.get(packageType) || packageType,
            validity_days: validityDays,
            sale_price: packageData?.sale_price || 0
        };
    };

    return (
        <div className="space-y-6">

            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                        <Filter className="h-5 w-5" />
                        <span>Filtros de Relatório</span>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={clearAllFilters}
                            className="ml-auto"
                        >
                            Limpar Filtros
                        </Button>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Filtros Ativos - Badges */}
                    {getActiveFilters().length > 0 && (
                        <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <div className="flex items-center gap-2 mb-2">
                                <Filter className="h-4 w-4 text-blue-600" />
                                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                                    Filtros aplicados:
                                </span>
                            </div>
                            <div className="flex flex-wrap gap-2">
                                {getActiveFilters().map((filter) => (
                                    <Badge
                                        key={filter.key}
                                        variant="secondary"
                                        className="bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-200"
                                    >
                                        {filter.label}
                                        <X
                                            className="h-3 w-3 ml-1 cursor-pointer hover:text-blue-600"
                                            onClick={filter.onRemove}
                                        />
                                    </Badge>
                                ))}
                            </div>
                        </div>
                    )}

                    {/* Filtros de Data Pré-definidos */}
                    <div>
                        <Label className="text-sm font-medium">Período Rápido</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => applyDatePreset('today')}
                                className="h-8"
                            >
                                <CalendarDays className="h-3 w-3 mr-1" />
                                Hoje
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => applyDatePreset('yesterday')}
                                className="h-8"
                            >
                                Ontem
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => applyDatePreset('lastWeek')}
                                className="h-8"
                            >
                                Última Semana
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => applyDatePreset('lastMonth')}
                                className="h-8"
                            >
                                Último Mês
                            </Button>
                        </div>
                    </div>

                    {/* Filtros Principais - Simplificados */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <Label htmlFor="start_date">Data Inicial</Label>
                            <Input
                                type="date"
                                id="start_date"
                                value={filters.start_date}
                                onChange={e => handleFilterChange('start_date', e.target.value)}
                            />
                        </div>
                        <div>
                            <Label htmlFor="end_date">Data Final</Label>
                            <Input
                                type="date"
                                id="end_date"
                                value={filters.end_date}
                                onChange={e => handleFilterChange('end_date', e.target.value)}
                            />
                        </div>
                        <div>
                            <Label>Método de Pagamento</Label>
                            <Select value={filters.payment_method || 'all'} onValueChange={value => handleFilterChange('payment_method', value === 'all' ? '' : value)}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Todos os métodos" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Todos os métodos</SelectItem>
                                    <SelectItem value="Dinheiro">Dinheiro</SelectItem>
                                    <SelectItem value="PIX">PIX</SelectItem>
                                    <SelectItem value="Cartão Débito">Cartão Débito</SelectItem>
                                    <SelectItem value="Cartão Crédito">Cartão Crédito</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Filtros de Busca e Seleção */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* <div>
                            <Label htmlFor="search_text">Buscar</Label>
                            <div className="relative">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="search_text"
                                    placeholder="Cliente ou vendedor..."
                                    className="pl-8"
                                    value={filters.search_text}
                                    onChange={e => handleFilterChange('search_text', e.target.value)}
                                />
                            </div>
                        </div> */}
                        <div>
                            <Label>Vendedor</Label>
                            <EmployeeSearchInput onSelect={handleEmployeeSelect} />
                        </div>
                        <div>
                            <Label>Cliente</Label>
                            <ClientSearchInput onSelect={handleClientSelect} />
                        </div>
                    </div>

                    {/* Ações */}
                    <div className="flex justify-between items-center pt-2">
                        <div className="flex items-center gap-2">
                            <Label className="text-sm">Itens por página:</Label>
                            <Select value={filters.per_page.toString()} onValueChange={value => handleFilterChange('per_page', parseInt(value))}>
                                <SelectTrigger className="w-20">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="10">10</SelectItem>
                                    <SelectItem value="25">25</SelectItem>
                                    <SelectItem value="50">50</SelectItem>
                                    <SelectItem value="100">100</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        {/* <Button onClick={exportToPDF}>
                            <Download className="h-4 w-4 mr-2" />
                            Exportar PDF
                        </Button> */}
                    </div>
                </CardContent>
            </Card>

            {/* Cards de Resumo */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                {/* Total Arrecadado */}
                {/* <Card className="border-l-4 border-l-green-500">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Arrecadado</p>
                                <p className="text-2xl font-bold text-green-600">R$ {summary.toFixed(2)}</p>
                            </div>
                            <div className="p-3 bg-green-100 rounded-full">
                                <DollarSign className="h-6 w-6 text-green-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card> */}

                {/* Total de Vendas */}
                {/* <Card className="border-l-4 border-l-blue-500">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total de Vendas</p>
                                <p className="text-2xl font-bold text-blue-600">{sales.length}</p>
                            </div>
                            <div className="p-3 bg-blue-100 rounded-full">
                                <ShoppingCart className="h-6 w-6 text-blue-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card> */}

                {/* Total de Custo */}
                {/* <Card className="border-l-4 border-l-red-500">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total de Custo</p>
                                <p className="text-2xl font-bold text-red-600">R$ {totalCost.toFixed(2)}</p>
                            </div>
                            <div className="p-3 bg-red-100 rounded-full">
                                <Target className="h-6 w-6 text-red-600" />
                            </div>
                        </div>
                    </CardContent>
                </Card> */}

                {/* Lucro Total */}
                {/* <Card className="border-l-4 border-l-purple-500">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Lucro Total</p>
                                <p className={`text-2xl font-bold ${profit >= 0 ? 'text-purple-600' : 'text-red-600'}`}>
                                    R$ {profit.toFixed(2)}
                                </p>
                                {profit > 0 && (
                                    <p className="text-xs text-gray-500 mt-1">
                                        Margem: {((profit / summary) * 100).toFixed(1)}%
                                    </p>
                                )}
                            </div>
                            <div className={`p-3 rounded-full ${profit >= 0 ? 'bg-purple-100' : 'bg-red-100'}`}>
                                <TrendingUp className={`h-6 w-6 ${profit >= 0 ? 'text-purple-600' : 'text-red-600'}`} />
                            </div>
                        </div>
                    </CardContent>
                </Card> */}
            </div>

            <Card>
                <CardHeader>
                    <CardTitle>Histórico de Vendas</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                            <TableRow>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('sale_timestamp')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Data</span>
                                        <span className="text-xs">{getSortIcon('sale_timestamp')}</span>
                                    </div>
                                </TableHead>
                                <TableHead>Cliente</TableHead>
                                <TableHead>Vendedor</TableHead>
                                <TableHead>Pacote</TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('quantity')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Qtd</span>
                                        <span className="text-xs">{getSortIcon('quantity')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('total_sale_price')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Valor Total</span>
                                        <span className="text-xs">{getSortIcon('total_sale_price')}</span>
                                    </div>
                                </TableHead>
                                <TableHead
                                    className="cursor-pointer hover:bg-muted/50 select-none"
                                    onClick={() => handleSortChange('payment_method')}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>Pagamento</span>
                                        <span className="text-xs">{getSortIcon('payment_method')}</span>
                                    </div>
                                </TableHead>
                                <TableHead className="w-32">Ações</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {isLoading ? (
                                // Loading skeleton
                                Array.from({ length: filters.per_page }).map((_, index) => (
                                    <TableRow key={`skeleton-${index}`}>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-24"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-32"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-28"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-20"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-8"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-16"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-20"></div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="h-4 bg-muted animate-pulse rounded w-16"></div>
                                        </TableCell>
                                    </TableRow>
                                ))
                            ) : sales.length === 0 ? (
                                <TableRow><TableCell colSpan="8" className="text-center">Nenhuma venda encontrada.</TableCell></TableRow>
                            ) : (
                                sales.map((sale, index) => {
                                    // Garantir que a key seja sempre uma string válida
                                    const saleKey = sale.sale?._id?.$oid || sale.sale?._id || sale.sale?.id || `sale-${index}`;
                                    const saleData = sale.sale || sale; // Compatibilidade com nova estrutura
                                    const codes = sale.codes || [];
                                    const isExpanded = expandedSales.has(saleKey);

                                    return (
                                        <React.Fragment key={saleKey}>
                                            <TableRow>
                                                <TableCell>{formatBsonDate(saleData.sale_timestamp)}</TableCell>
                                                <TableCell>{clientsMap.get(saleData.erp_client_id) || saleData.erp_client_id}</TableCell>
                                                <TableCell>{employeesMap.get(saleData.erp_employee_id) || saleData.erp_employee_id}</TableCell>
                                                <TableCell>{packagesMap.get(saleData.package_type) || saleData.package_type}</TableCell>
                                                <TableCell>{saleData.quantity}</TableCell>
                                                <TableCell>R$ {saleData.total_sale_price.toFixed(2)}</TableCell>
                                                <TableCell>{saleData.payment_method}</TableCell>
                                                <TableCell>
                                                    <div className="flex space-x-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => toggleSaleExpansion(saleKey)}
                                                        >
                                                            {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => openReceiptModal(sale)}
                                                        >
                                                            <FileText className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                            {isExpanded && codes.length > 0 && (
                                                <TableRow>
                                                    <TableCell colSpan="8" className="bg-muted/50">
                                                        <div className="p-4">
                                                            <h4 className="font-semibold mb-2">Códigos de Recarga:</h4>
                                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                                                                {codes.map((code, codeIndex) => (
                                                                    <div key={code.id?.$oid || code._id?.$oid || codeIndex} className="font-mono text-sm bg-white p-2 rounded border">
                                                                        <div className="text-xs text-muted-foreground">{code.package_type}</div>
                                                                        <div>{code.code}</div>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            )}
                                        </React.Fragment>
                                    );
                                })
                            )}
                        </TableBody>
                        </Table>
                    </div>
                    <div className="flex items-center justify-between py-4">
                        <div className="text-sm text-muted-foreground">
                            Mostrando {((filters.page - 1) * filters.per_page) + 1} a {Math.min(filters.page * filters.per_page, totalSales)} de {totalSales} resultados
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleFilterChange('page', 1)}
                                disabled={filters.page <= 1}
                            >
                                Primeira
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleFilterChange('page', filters.page - 1)}
                                disabled={filters.page <= 1}
                            >
                                Anterior
                            </Button>
                            <span className="text-sm px-2">
                                Página {filters.page} de {totalPages}
                            </span>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleFilterChange('page', filters.page + 1)}
                                disabled={filters.page >= totalPages}
                            >
                                Próxima
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleFilterChange('page', totalPages)}
                                disabled={filters.page >= totalPages}
                            >
                                Última
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Modal de Recibo */}
            <Dialog open={isReceiptModalOpen} onOpenChange={setIsReceiptModalOpen}>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Comprovante de Venda</DialogTitle>
                    </DialogHeader>
                    {selectedSaleForReceipt && (() => {
                        const saleData = selectedSaleForReceipt.sale || selectedSaleForReceipt;
                        const codes = selectedSaleForReceipt.codes || [];
                        const packageType = saleData.package_type;
                        const packageData = getPackageData(packageType, codes);



                        return (
                            <RechargeReceipt
                                saleReceipt={selectedSaleForReceipt}
                                selectedPackage={packageData}
                                selectedClient={{
                                    nome: clientsMap.get(saleData.erp_client_id) || 'Cliente não encontrado'
                                }}
                                selectedEmployee={{
                                    nome: employeesMap.get(saleData.erp_employee_id) || saleData.erp_employee_id
                                }}
                                onNewSale={closeReceiptModal}
                            />
                        );
                    })()}
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default ReportsPage; 