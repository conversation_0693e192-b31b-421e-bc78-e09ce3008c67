"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Calculator as CalculatorIcon } from "lucide-react";

export default function CalculadoraPage() {
  return (
    <div className="container mx-auto max-w-4xl py-8">
      
      <Card>
        <CardContent className="pt-6">
          <iframe
            src="/simulador_parcelamento_declaracao_final_limpa.html"
            title="Calculadora de Parcelamento Infocell"
            style={{
              width: '100%',
              height: '100vh', 
              border: 'none',
              borderRadius: '8px'
            }}
          />
        </CardContent>
      </Card>
    </div>
  );
}