"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { 
  FileText, 
  Download, 
  Trash2, 
  Calendar,
  User,
  AlertCircle,
  RefreshCw,
  Search,
  Printer,
  Share2,
  Loader2
} from "lucide-react";
import infocellApi from "@/lib/infocellApi";
import useDownload from "@/lib/useDownload";
import { useDebounce } from "@/lib/hooks/useDebounce";
import usePrint from "@/lib/hooks/usePrint";
import { shortenUrl } from "@/lib/urlShortener";

export default function DocumentosPage() {
  const [documents, setDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [printingDocId, setPrintingDocId] = useState(null);
  const { downloadFile } = useDownload();
  const { printFile } = usePrint();
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    } else {
      loadDocuments();
    }
  }, [debouncedSearchTerm]);

  useEffect(() => {
    loadDocuments();
  }, [currentPage]);

  const loadDocuments = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const params = {
        page: currentPage,
        per_page: 12,
      };

      if (debouncedSearchTerm) {
        params.client_name = debouncedSearchTerm;
      }

      const response = await infocellApi.listDocumentHistory(params);
      
      console.log('Resposta da API de documentos:', response);
      
      const documents = response.data?.documents || response.documents || response.data || [];
      const totalPages = response.data?.total_pages || response.total_pages || 1;
      
      setDocuments(documents);
      setTotalPages(totalPages);
      
    } catch (err) {
      console.error('Erro ao carregar documentos:', err);
      setError(`Erro ao carregar histórico de documentos: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (doc) => {
    try {
      setError(null);
      const response = await infocellApi.downloadDocument(doc.id);
      const blob = await response.blob();
      const filename = doc.file_name || `documento_${doc.id}`;
      
      await downloadFile(blob, filename);
      
    } catch (err) {
      console.error('Erro no download:', err);
      setError(`Erro ao fazer download: ${err.message}`);
    }
  };

  const handlePrint = async (doc) => {
    setPrintingDocId(doc.id);
    try {
      setError(null);
      setSuccess(null);
      
      const isDocx = doc.file_name.endsWith('.docx') || doc.output_format === 'docx';

      if (isDocx) {
        setSuccess('Convertendo DOCX para PDF para impressão... Isso pode levar alguns segundos.');

        const response = await infocellApi.convertDocxToPdf(doc.id);
        const pdfBlob = await response.blob();
        
        setSuccess(null);
        
        await printFile(pdfBlob, 'application/pdf');

      } else {
        const response = await infocellApi.downloadDocument(doc.id);
        const blob = await response.blob();
        const fileType = response.headers.get('content-type') || 'application/pdf';
        await printFile(blob, fileType);
      }
    } catch (err) {
      console.error('Erro ao processar impressão:', err);
      setError(`Erro ao processar impressão: ${err.message}`);
    } finally {
      setPrintingDocId(null);
    }
  };

  const handleShare = async (doc) => {
    try {
      setError(null);
      setSuccess(null);
      
      const response = await infocellApi.getDocumentShareLink(doc.id);
      const { share_url } = response.data;
      
      const shortUrl = await shortenUrl(share_url);
      
      const message = `Olá ${doc.client_name}! Segue o documento para download: ${shortUrl}`;
      const encodedMessage = encodeURIComponent(message);
      
      const desktopUrl = `whatsapp://send?text=${encodedMessage}`;
      const webUrl = `https://web.whatsapp.com/send?text=${encodedMessage}`;
      
      let fallbackTimeout;

      const cleanup = () => {
        clearTimeout(fallbackTimeout);
        window.removeEventListener('blur', handleBlur);
      };
      
      const handleBlur = () => {
        cleanup();
      };

      window.addEventListener('blur', handleBlur);

      fallbackTimeout = setTimeout(() => {
        console.log("Fallback para WhatsApp Web acionado.");
        window.open(webUrl, '_blank', 'noopener,noreferrer');
        cleanup();
      }, 2500);

      window.location.href = desktopUrl;

    } catch (err) {
      console.error('Erro ao compartilhar:', err);
      setError(`Erro ao gerar link de compartilhamento: ${err.message}`);
    }
  };

  const handleDelete = async (document) => {
    if (!confirm(`Tem certeza que deseja excluir o documento "${document.file_name}"?`)) {
      return;
    }

    try {
      await infocellApi.deleteDocument(document.id);
      setSuccess('Documento excluído com sucesso!');
      loadDocuments();
    } catch (err) {
      console.error('Erro ao excluir documento:', err);
      setError('Erro ao excluir documento: ' + err.message);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFileIcon = (fileName) => {
    return <FileText className="h-5 w-5 text-primary" />;
  };

  const truncateFileName = (fileName, maxLength = 25) => {
    if (!fileName || fileName.length <= maxLength) return fileName;
    const extension = fileName.split('.').pop();
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));
    if (nameWithoutExt.length <= maxLength - extension.length - 1) return fileName;
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4);
    return `${truncatedName}...${extension}`;
  };

  return (
    <div className="space-y-6">
      <header className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Documentos Gerados
          </h1>
          <p className="text-muted-foreground mt-2">
            Histórico e gerenciamento dos documentos criados
          </p>
        </div>
        <Button 
          onClick={loadDocuments} 
          variant="outline" 
          size="sm"
          disabled={isLoading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Atualizar
        </Button>
      </header>

      {/* Filtros */}
      <div className="flex items-center gap-2">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Filtrar por nome do cliente..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Mensagens de Status */}
      {error && (
        <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg flex items-center gap-2">
          <AlertCircle className="h-4 w-4 text-destructive" />
          <p className="text-destructive text-sm">{error}</p>
        </div>
      )}

      {success && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-800 text-sm">{success}</p>
        </div>
      )}

      {/* Lista de Documentos */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-3 text-muted-foreground">Carregando documentos...</span>
        </div>
      ) : documents.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Nenhum documento encontrado
            </h3>
            <p className="text-muted-foreground text-center">
              Ainda não há documentos gerados. Comece criando seu primeiro documento.
            </p>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {documents.map((document) => {
              const isPrinting = printingDocId === document.id;
              
              return (
              <Card key={document.id} className="group hover:shadow-md transition-all duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-2 min-w-0 flex-1">
                      {getFileIcon(document.file_name)}
                      <div className="min-w-0 flex-1">
                        <CardTitle 
                          className="text-sm font-medium leading-tight text-left break-words"
                          title={document.file_name || `Documento ${document.id}`}
                        >
                          {truncateFileName(document.file_name) || `Documento ${document.id}`}
                        </CardTitle>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-3">
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <User className="h-4 w-4" />
                      <span className="truncate">
                        Cliente: {document.client_name || document.client_id}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>{formatDate(document.created_at)}</span>
                    </div>
                  </div>
                  
                  <div className="flex gap-2 pt-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDownload(document)}
                            className="flex-1"
                          >
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Baixar o arquivo original ({document.output_format || 'pdf'})</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handlePrint(document)}
                      title="Imprimir"
                      disabled={isPrinting}
                    >
                      {isPrinting ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Printer className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleShare(document)}
                      title="Compartilhar no WhatsApp"
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDelete(document)}
                      className="text-destructive hover:text-destructive"
                      title="Excluir"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
              );
            })}
          </div>

          {/* Paginação */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage <= 1}
              >
                Anterior
              </Button>
              
              <span className="text-sm text-muted-foreground">
                Página {currentPage} de {totalPages}
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage >= totalPages}
              >
                Próxima
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
} 