// src/app/api/generate-resume/route.js
import { NextResponse } from 'next/server';
import { put } from '@vercel/blob';
import { createResume } from '../../../../modules/resume-generator/services/resume.service.js';

// Função principal da API Route
export async function POST(request) {
  try {
    // Obter FormData em vez de JSON
    const submittedFormData = await request.formData();
    
    // Reconstruir os dados a partir do FormData
    const parsedResumeData = {
      personalInfo: {},
      createdAt: new Date().toISOString(), // Adiciona o timestamp aqui
    };

    // Capturar campos simples
    parsedResumeData.objective = submittedFormData.get('objective') || '';
    parsedResumeData.qualifications = submittedFormData.get('qualifications') || '';
    parsedResumeData.additionalInfo = submittedFormData.get('additionalInfo') || '';
    
    // Campos complexos (objetos/arrays) foram enviados como JSON
    try {
      parsedResumeData.education = JSON.parse(submittedFormData.get('education') || '[]');
      parsedResumeData.experience = JSON.parse(submittedFormData.get('experience') || '[]');
    } catch (e) {
      console.error('Erro ao parsear JSON de education/experience:', e);
      parsedResumeData.education = [];
      parsedResumeData.experience = [];
    }
    
    // Processar campos personalInfo (foram enviados como personalInfo[campo])
    for (const [key, value] of submittedFormData.entries()) {
      if (key.startsWith('personalInfo[') && key.endsWith(']')) {
        // Extrair o nome do campo entre colchetes, ex: personalInfo[fullName] -> fullName
        const fieldName = key.substring(13, key.length - 1);
        parsedResumeData.personalInfo[fieldName] = value;
      }
    }
    
    // Processar o arquivo de imagem de perfil, se existir
    const profilePictureFile = submittedFormData.get('profilePictureFile');
    let profilePictureBlobUrl = parsedResumeData.personalInfo.profilePictureUrl || null;
    
    if (profilePictureFile && typeof profilePictureFile === 'object' && typeof profilePictureFile.name === 'string' && profilePictureFile.size > 0) {
      try {
        // Nome de arquivo único com timestamp
        const fileName = `profile_${Date.now()}_${profilePictureFile.name}`;
        
        // Upload para Vercel Blob
        const blob = await put(`images/profile/${fileName}`, profilePictureFile, { 
          access: 'public', 
          contentType: profilePictureFile.type 
        });
        
        profilePictureBlobUrl = blob.url;
      } catch (e) {
        console.error('Erro ao fazer upload da imagem:', e);
      }
    }
    
    // Atualizar a URL da imagem nos dados
    parsedResumeData.personalInfo.profilePictureUrl = profilePictureBlobUrl;
    
    // Validação mínima dos dados recebidos
    if (!parsedResumeData.personalInfo || !parsedResumeData.personalInfo.fullName) {
      console.error('API POST: Dados do formulário incompletos ou inválidos.', parsedResumeData);
      return NextResponse.json(
        { success: false, message: 'Dados do formulário incompletos. Nome completo é obrigatório.' },
        { status: 400 } // Bad Request
      );
    }

    // Chama o serviço orquestrador para lidar com toda a lógica de criação do currículo
    const result = await createResume(parsedResumeData);

    if (result.success) {
      // Usar a URL do Vercel Blob diretamente sem modificação
      return NextResponse.json({
        success: true,
        message: result.message,
        resumeUrl: result.resumeUrl, // URL direta para o PDF do Vercel Blob
      });
    } else {
      // O resume.service já logou o erro detalhado e o stack
      return NextResponse.json(
        { success: false, message: result.message },
        { status: 500 } // Internal Server Error
      );
    }

  } catch (error) {
    // Este catch lida com erros inesperados na própria rota (ex: falha ao parsear JSON)
    // Erros dentro de createResume são tratados e formatados pelo próprio serviço.
    console.error('Erro inesperado na rota /api/generate-resume:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido no servidor ao processar a requisição.';
    if (error instanceof Error && error.stack) {
      console.error("Stack do Erro (rota API):", error.stack);
    }
    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 500 }
    );
  }
}