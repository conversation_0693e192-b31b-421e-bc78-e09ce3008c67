import { NextResponse } from 'next/server';

const API_BASE_URL = process.env.INFOCELL_SERVER_API_URL || 'http://infocell-backend-0jgfya-357133-46-202-178-159.traefik.me/api/v1';

export async function GET(request, { params }) {
  return proxyRequest('GET', request, params);
}

export async function POST(request, { params }) {
  return proxyRequest('POST', request, params);
}

export async function PUT(request, { params }) {
  return proxyRequest('PUT', request, params);
}

export async function DELETE(request, { params }) {
  return proxyRequest('DELETE', request, params);
}

async function proxyRequest(method, request, routeParams) {
  try {
    const path = routeParams.path.join('/');
    const url = new URL(request.url);
    const searchParams = url.searchParams.toString();
    const targetUrl = `${API_BASE_URL}/${path}${searchParams ? `?${searchParams}` : ''}`;

    console.log(`[Proxy] ${method} ${targetUrl}`);

    const headers = {};
    
    const authHeader = request.headers.get('authorization');
    if (authHeader) {
      headers['Authorization'] = authHeader;
    }

    const contentType = request.headers.get('content-type');
    if (contentType) {
      headers['Content-Type'] = contentType;
    }

    let body;
    if (method !== 'GET' && method !== 'HEAD') {
      try {
        // Para multipart/form-data (uploads), preservar dados binários
        if (contentType && contentType.includes('multipart/form-data')) {
          body = await request.arrayBuffer();
        } else {
          // Para JSON e outros tipos texto, usar .text()
          body = await request.text();
          if (body && !contentType) {
            headers['Content-Type'] = 'application/json';
          }
        }
      } catch (error) {
        console.error('Erro ao ler corpo da requisição:', error);
      }
    }

    // Configurar timeout maior para downloads
    const isDownloadRequest = path.includes('/download');
    const timeoutMs = isDownloadRequest ? 50000 : 10000; // 50s para downloads, 10s para outros

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      const response = await fetch(targetUrl, {
        method,
        headers,
        body: body || undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log(`[Proxy] Resposta: ${response.status} ${response.statusText}`);

      const responseContentType = response.headers.get('content-type') || '';
      
      // Detectar se é um arquivo para download
      const isFileDownload = responseContentType.includes('application/pdf') ||
                            responseContentType.includes('application/vnd.openxmlformats') ||
                            responseContentType.includes('application/vnd.oasis') ||
                            response.headers.get('content-disposition')?.includes('attachment');

      if (isFileDownload) {
        console.log(`[Proxy] Processando download de arquivo`);
        
        // Para downloads, usar streaming
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('Não foi possível obter reader do response body');
        }

        const chunks = [];
        let totalSize = 0;
        const maxSize = 50 * 1024 * 1024; // 50MB limit
        
        try {
          while (true) {
            const { done, value } = await reader.read();
            
            if (done) break;
            
            if (value) {
              totalSize += value.length;
              if (totalSize > maxSize) {
                throw new Error('Arquivo muito grande para download via proxy');
              }
              chunks.push(value);
            }
          }
        } finally {
          reader.releaseLock();
        }

        // Combinar chunks
        const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
        const combinedArray = new Uint8Array(totalLength);
        let offset = 0;
        
        for (const chunk of chunks) {
          combinedArray.set(chunk, offset);
          offset += chunk.length;
        }

        console.log(`[Proxy] Download concluído: ${totalLength} bytes`);

        // Preparar headers para o arquivo
        const responseHeaders = new Headers();
        responseHeaders.set('Content-Type', responseContentType);
        responseHeaders.set('Content-Length', totalLength.toString());
        
        const contentDisposition = response.headers.get('content-disposition');
        if (contentDisposition) {
          responseHeaders.set('Content-Disposition', contentDisposition);
        }
        
        // Adicionar headers de cache para arquivos
        responseHeaders.set('Cache-Control', 'public, max-age=3600');
        
        return new NextResponse(combinedArray, {
          status: response.status,
          statusText: response.statusText,
          headers: responseHeaders,
        });
      }

      // Para respostas JSON normais
      const data = await response.text();
      
      return new NextResponse(data, {
        status: response.status,
        statusText: response.statusText,
        headers: {
          'Content-Type': 'application/json',
        },
      });

    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      if (fetchError.name === 'AbortError') {
        console.error(`[Proxy] Timeout após ${timeoutMs}ms`);
        return NextResponse.json(
          { error: 'Timeout na requisição', message: 'A requisição demorou muito para responder' },
          { status: 504 }
        );
      }
      
      throw fetchError;
    }

  } catch (error) {
    console.error('[Proxy] Erro:', error);
    
    let errorMessage = 'Erro no proxy da API';
    let status = 500;
    
    if (error.message.includes('muito grande')) {
      errorMessage = 'Arquivo muito grande para download';
      status = 413;
    } else if (error.message.includes('Timeout') || error.name === 'AbortError') {
      errorMessage = 'Timeout na requisição';
      status = 504;
    }
    
    return NextResponse.json(
      { error: errorMessage, message: error.message },
      { status }
    );
  }
} 