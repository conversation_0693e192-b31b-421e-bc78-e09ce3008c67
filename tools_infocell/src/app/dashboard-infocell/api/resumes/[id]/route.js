import { NextResponse } from 'next/server';
import { del } from '@vercel/blob';
import { readResumeData, writeResumeData } from '@/modules/resume-generator/services/resume-storage.service.js';

const resumesPublicDir = process.cwd() + '/public/resumes'; // Mantido para referência, mas não mais utilizado para novos PDFs

export async function DELETE(request, { params }) {
  const { id } = params;

  if (!id) {
    return NextResponse.json({ message: 'ID do currículo não fornecido.' }, { status: 400 });
  }

  try {
    let resumes = await readResumeData();
    const resumeIdNumber = parseInt(id, 10);

    if (isNaN(resumeIdNumber)) {
      return NextResponse.json({ message: 'ID inválido fornecido.' }, { status: 400 });
    }

    const resumeToDelete = resumes.find(resume => resume.id === resumeIdNumber);

    if (!resumeToDelete) {
      return NextResponse.json({ message: '<PERSON>urrí<PERSON>lo não encontrado.' }, { status: 404 });
    }

    // Verificar se há URL do Vercel Blob e excluir
    if (resumeToDelete.resumeUrl && resumeToDelete.resumeUrl.includes('blob.vercel-storage.com')) {
      try {
        await del(resumeToDelete.resumeUrl); // Deleta o blob usando sua URL completa
        console.log(`Blob PDF ${resumeToDelete.resumeUrl} excluído do Vercel Blob.`);
      } catch (blobError) {
        console.error(`Erro ao excluir blob PDF ${resumeToDelete.resumeUrl}:`, blobError);
        // Considerar se o processo de exclusão do registro no DB deve continuar ou não
      }
    } else {
      // Tentativa de excluir o arquivo PDF local (legado)
      const pdfFileName = `${resumeToDelete.id}.pdf`;
      const pdfPath = path.join(resumesPublicDir, pdfFileName);

      try {
        await fsPromises.unlink(pdfPath);
      } catch (fileError) {
        if (fileError.code === 'ENOENT') {
          console.log(`Arquivo PDF ${pdfFileName} não encontrado em ${pdfPath}, nada a excluir.`);
        } else {
          console.error(`Erro ao tentar excluir arquivo PDF ${pdfFileName} de ${pdfPath}:`, fileError);
        }
      }
    }

    const filteredResumes = resumes.filter(resume => resume.id !== resumeIdNumber);
    await writeResumeData(filteredResumes);

    return NextResponse.json({ message: 'Currículo excluído com sucesso.' }, { status: 200 });

  } catch (error) {
    console.error('[API DELETE] Erro ao excluir currículo:', error);
    return NextResponse.json({ message: error.message || 'Erro interno do servidor ao excluir currículo.' }, { status: 500 });
  }
}

export async function GET(request, { params }) {
  const { id } = params;
  if (!id) {
    return NextResponse.json({ message: 'ID do currículo não fornecido.' }, { status: 400 });
  }

  try {
    const resumes = await readResumeData();
    const resumeIdNumber = parseInt(id, 10);
    if (isNaN(resumeIdNumber)) {
      return NextResponse.json({ message: 'ID inválido fornecido.' }, { status: 400 });
    }

    const resume = resumes.find(r => r.id === resumeIdNumber);
    if (!resume) {
      return NextResponse.json({ message: 'Currículo não encontrado.' }, { status: 404 });
    }

    // Verifica se o currículo tem uma URL do Vercel Blob
    if (resume.resumeUrl && resume.resumeUrl.includes('blob.vercel-storage.com')) {
      // Se tiver uma URL do Vercel Blob, retorna diretamente
      return NextResponse.json({ ...resume, filePath: resume.resumeUrl });
    } else {
      // Fallback para o sistema antigo (legado)
      const pdfFileName = `${resume.id}.pdf`;
      const pdfPath = path.join(resumesPublicDir, pdfFileName);

      if (fs.existsSync(pdfPath)) {
        // Retorna o caminho relativo público para o cliente, sem o prefixo da aplicação
        const relativePdfPath = `/resumes/${pdfFileName}`;
        return NextResponse.json({ ...resume, filePath: relativePdfPath });
      } else {
        // Se o arquivo PDF não existe, retorna os dados do currículo sem o filePath
        // ou com uma mensagem indicando que o arquivo não foi encontrado.
        return NextResponse.json({ ...resume, filePath: null, fileMessage: 'Arquivo PDF não encontrado no servidor.' });
      }
    }

  } catch (error) {
    console.error('Erro ao buscar currículo por ID:', error);
    return NextResponse.json({ message: 'Erro ao buscar currículo.' }, { status: 500 });
  }
}

export async function PUT(request, { params }) {
  const { id } = params;
  if (!id) {
    return NextResponse.json({ message: 'ID do currículo não fornecido.' }, { status: 400 });
  }

  try {
    const resumeData = await request.json();
    let resumes = await readResumeData();
    const resumeIdNumber = parseInt(id, 10);

    if (isNaN(resumeIdNumber)) {
      return NextResponse.json({ message: 'ID inválido fornecido.' }, { status: 400 });
    }

    const resumeIndex = resumes.findIndex(resume => resume.id === resumeIdNumber);

    if (resumeIndex === -1) {
      return NextResponse.json({ message: 'Currículo não encontrado para atualização.' }, { status: 404 });
    }

    // Antes de atualizar, exclui o PDF antigo se o nome for mudar ou se for relevante
    const oldResumeData = resumes[resumeIndex];
    const oldPdfFileName = `${oldResumeData.id}.pdf`;
    const oldPdfPath = path.join(resumesPublicDir, oldPdfFileName);

    try {
      if (fs.existsSync(oldPdfPath)) {
        await fsPromises.unlink(oldPdfPath);
      }
    } catch (fileError) {
      console.error(`Erro ao excluir PDF antigo ${oldPdfFileName}:`, fileError);
      // Continuar mesmo se a exclusão do PDF antigo falhar
    }

    // Atualiza o currículo
    const updatedResume = { ...oldResumeData, ...resumeData, id: resumeIdNumber, updatedAt: new Date().toISOString() };
    resumes[resumeIndex] = updatedResume;
    await writeResumeData(resumes);

    // Gera o novo PDF
    // A geração do PDF já usa o ID como nome do arquivo.
    // A função generatePdfForResume deve ser ajustada para salvar como <id>.pdf
    // Esta chamada é apenas ilustrativa, a geração real acontece em /api/generate-resume
    // Aqui, apenas atualizamos o JSON. A geração do PDF deve ser acionada pelo frontend
    // ou o cliente deve estar ciente de que precisa re-gerar/baixar o PDF.

    // Para consistência, vamos assumir que o PDF é gerado/atualizado em outro endpoint
    // ou que o nome do arquivo é sempre <id>.pdf e o conteúdo é atualizado quando o PDF é gerado.

    return NextResponse.json(updatedResume, { status: 200 });

  } catch (error) {
    console.error('Erro ao atualizar currículo:', error);
    if (error instanceof SyntaxError) {
      return NextResponse.json({ message: 'Corpo da requisição inválido (JSON mal formatado).' }, { status: 400 });
    }
    return NextResponse.json({ message: error.message || 'Erro interno do servidor ao atualizar currículo.' }, { status: 500 });
  }
}