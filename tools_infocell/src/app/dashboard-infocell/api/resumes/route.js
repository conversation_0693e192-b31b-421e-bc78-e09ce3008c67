import { NextResponse } from 'next/server';
import { readResumeData } from '@/modules/resume-generator/services/resume-storage.service.js';

export async function GET() {
  try {
    const data = await readResumeData();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error reading resumes data from Redis:', error);
    return NextResponse.json({ message: 'Error reading resumes data' }, { status: 500 });
  }
}