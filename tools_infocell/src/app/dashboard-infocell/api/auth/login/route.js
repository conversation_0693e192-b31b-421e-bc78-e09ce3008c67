import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

export async function POST(request) {
  try {
    const { password } = await request.json();

    if (!password) {
      return NextResponse.json({ message: 'Senha é obrigatória.' }, { status: 400 });
    }

    const hashedPassword = process.env.ADMIN_PASSWORD_HASH;
    const jwtSecret = process.env.JWT_SECRET;

    if (!hashedPassword) {
      console.error('ADMIN_PASSWORD_HASH não está configurado no .env');
      return NextResponse.json({ message: 'Erro de configuração do servidor.' }, { status: 500 });
    }
    if (!jwtSecret) {
      console.error('JWT_SECRET não está configurado no .env');
      return NextResponse.json({ message: 'Erro de configuração do servidor.' }, { status: 500 });
    }

    const passwordIsValid = await bcrypt.compare(password, hashedPassword);

    if (!passwordIsValid) {
      return NextResponse.json({ message: 'Senha inválida.' }, { status: 401 });
    }

    // Senha válida, gerar token JWT
    const token = jwt.sign({ sub: "infocell_internal_user", authenticated: true }, jwtSecret, {
      expiresIn: '7d', // Token expira em 7 dias
    });

    return NextResponse.json({ success: true, token });

  } catch (error) {
    console.error('Erro na API de login:', error);
    return NextResponse.json({ message: 'Erro interno do servidor.' }, { status: 500 });
  }
}