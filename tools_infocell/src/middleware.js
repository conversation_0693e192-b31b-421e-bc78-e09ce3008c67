import { NextResponse } from 'next/server';
import { jwtVerify } from 'jose';
import { APP_PREFIX } from '@/lib/path-utils';

const JWT_SECRET = process.env.JWT_SECRET;

export async function middleware(request) {
  console.log("--- Middleware Executing ---");
  console.log("Timestamp:", new Date().toISOString());
  console.log("Request URL Pathname:", request.nextUrl.pathname);

  let { pathname } = request.nextUrl;
  let normalizedPathname = pathname;

  
  if (normalizedPathname !== APP_PREFIX && normalizedPathname.endsWith('/')) {
    normalizedPathname = normalizedPathname.slice(0, -1);
  }
  
  if (pathname === APP_PREFIX) {
    normalizedPathname = `${APP_PREFIX}/`;
  }

  console.log("Calculated normalizedPathname:", normalizedPathname);
  
  const authTokenCookie = request.cookies.get('authToken');
  const authToken = authTokenCookie ? authTokenCookie.value : null;
  
  console.log("AuthToken from cookie:", authToken ? "Present" : "Ausente");

  const protectedRoutes = [`${APP_PREFIX}/`, `${APP_PREFIX}/resume-generator`, `${APP_PREFIX}/templates`, `${APP_PREFIX}/gerar-documento`];
  const loginRoute = `${APP_PREFIX}/auth/login`;

  console.log(`Checking if normalizedPathname='${normalizedPathname}' is in protectedRoutes='${JSON.stringify(protectedRoutes)}':`, protectedRoutes.includes(normalizedPathname));

  if (protectedRoutes.includes(normalizedPathname)) {
    if (!authToken) {
      console.log("User is unauthenticated. Preparing to redirect to login.");
      const loginUrl_for_debug = new URL(loginRoute, request.url).toString();
      console.log("Redirecting to login URL:", loginUrl_for_debug);
      
      const redirectUrl = new URL(loginRoute, request.url);
      return NextResponse.redirect(redirectUrl);
    }

    try {
      if (!JWT_SECRET) {
        throw new Error('JWT_SECRET não está configurado no servidor.');
      }
      const secretKey = new TextEncoder().encode(JWT_SECRET);
      await jwtVerify(authToken, secretKey);
      return NextResponse.next();
    } catch (error) {
      console.error('Erro de verificação do token no middleware:', error.message);
      console.log("Token verification failed. Redirecting to login with session_expired parameter.");
      const redirectUrl = new URL(loginRoute, request.url);
      redirectUrl.searchParams.set('error', 'session_expired');
      return NextResponse.redirect(redirectUrl);
    }
  }

  if (normalizedPathname === loginRoute && authToken) {
    try {
      if (!JWT_SECRET) {
        console.warn('JWT_SECRET não configurado ao tentar redirecionar de /auth/login');
      } else {
        const secretKey = new TextEncoder().encode(JWT_SECRET);
        await jwtVerify(authToken, secretKey);
        // Redireciona para a raiz da aplicação prefixada
        return NextResponse.redirect(new URL(`${APP_PREFIX}/`, request.url));
      }
    } catch (error) {
      // Token inválido, permite que o usuário prossiga para a página de login
      return NextResponse.next();
    }
  }

  return NextResponse.next();
}

// Next.js não permite expressões em template literals no config.matcher
// Precisamos usar valores estáticos baseados no valor de APP_PREFIX
export const config = {
  matcher: ['/dashboard-infocell/:path*'],
};