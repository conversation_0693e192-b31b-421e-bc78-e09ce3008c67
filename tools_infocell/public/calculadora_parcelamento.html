
<!DOCTYPE html>
<html lang="pt-BR">
<head>
<meta charset="utf-8"/>
<title>Calculadora de Parcelamento - Infocell</title>
<style>
    body {
      font-family: Arial, sans-serif;
      background-color: #fdfdfd;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      padding-top: 120px;
    }
    .container {
      width: 100%;
      max-width: 500px;
      padding: 20px;
      border: 2px solid #ddd;
      border-radius: 10px;
      background-color: white;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    label {
      display: block;
      margin-bottom: 1px;
      font-weight: bold;
    }
    input, select {
      display: block;
      width: 100%;
      height: 40px;
      box-sizing: border-box;
      margin-bottom: 16px;
    }
    button {
      padding: 10px;
      background-color: #8000ff;
      color: white;
      border: none;
      border-radius: 5px;
      font-weight: bold;
      width: 100%;
      height: 45px;
      margin-top: 10px;
    }
    .resultado {
      margin-top: 20px;
      padding: 10px;
      background: #f4f4f4;
      border-radius: 5px;
    }
    .header-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      background-color: white;
      z-index: 999;
      border-bottom: 2px solid #ddd;
      padding: 10px 0;
      text-align: center;
    }
</style>
</head>
<body>
<div class="container">
  <div class="header-container"><h2 class="header-fixed">Calculadora de Parcelamento - Infocell</h2></div>

  <label for="cidade">Cidade da Venda:</label>
  <select id="cidade" required>
    <option value="">Selecione</option>
    <option>Pratania</option>
    <option>São Manuel</option>
    <option>Lençóis Paulista</option>
  </select>

  <label for="vendedor">Selecionar Vendedor:</label>
  <select id="vendedor" required>
    <option value="">Selecione</option>
    <option>Denilson</option>
    <option>Daniela</option>
    <option>Luis Henrique</option>
    <option>Sarah</option>
    <option>Sabrina</option>
  </select>

  <label for="pedido">Nº do Pedido:</label>
  <input id="pedido" placeholder="Ex: 12345" type="text"/>

  <label for="valorVenda">Valor Total da Venda (R$):</label>
  <input id="valorVenda" inputmode="numeric" placeholder="0,00" type="text"/>

  <label for="entrada">Valor de Entrada (R$):</label>
  <input id="entrada" inputmode="numeric" placeholder="0,00" type="text"/>

  <label for="parcelas">Número de Parcelas:</label>
  <select id="parcelas"><option value="">Selecione</option></select>

  <div class="resultado" id="resultado"></div>

  <div style="margin-top: 20px; background: #f9f9f9; padding: 10px; border-radius: 5px; font-size: 15px;">
    🧾 Após calcular, o atendente deve somar a entrada + valor parcelado e lançar o total no sistema como valor do produto. 
    Em seguida, informa o valor de entrada e escolhe a forma de pagamento parcelado normalmente.
  </div>

  <button onclick="imprimirResumo()">Imprimir Resumo</button>
</div>

<script>
function formatarMoeda(valor) {
  return valor.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' });
}

function parseMoeda(valor) {
  return parseFloat(valor.replace(/\./g, '').replace(',', '.')) || 0;
}

document.addEventListener("DOMContentLoaded", function () {
  const campos = ["valorVenda", "entrada", "parcelas", "cidade", "vendedor", "pedido"];
  campos.forEach(id => {
    const el = document.getElementById(id);
    if (el) {
      el.addEventListener("input", calcular);
      el.addEventListener("change", calcular);
    }
  });

  const selectParcelas = document.getElementById("parcelas");
  function atualizarOpcoesParcelas() {
    const valorVenda = parseMoeda(document.getElementById("valorVenda").value);
    const entrada = parseMoeda(document.getElementById("entrada").value || "0");
    const restante = valorVenda - entrada;
    const taxaMensal = 0.0223;

    selectParcelas.innerHTML = '<option value="">Selecione</option>';
    for (let i = 1; i <= 12; i++) {
      const jurosTotal = ((Math.pow(1 + taxaMensal, i) - 1) * 100).toFixed(2);
      const totalComJuros = restante * Math.pow(1 + taxaMensal, i);
      const parcela = (totalComJuros / i).toFixed(2);
      const parcelaFormatada = parseFloat(parcela).toLocaleString('pt-BR', { minimumFractionDigits: 2 });
      const option = document.createElement("option");
      option.value = i;
      option.text = `${i}x de R$ ${parcelaFormatada} - juros ${jurosTotal}% (${(taxaMensal * 100).toFixed(2)}% a.m)`;
      selectParcelas.appendChild(option);
    }
  }

  document.getElementById("valorVenda").addEventListener("input", atualizarOpcoesParcelas);
  document.getElementById("entrada").addEventListener("input", atualizarOpcoesParcelas);
});

function calcular() {
  const vendedor = document.getElementById("vendedor").value;
  const pedido = document.getElementById("pedido").value;
  const valorVenda = parseMoeda(document.getElementById("valorVenda").value);
  const entrada = parseMoeda(document.getElementById("entrada").value || "0");
  const parcelas = parseInt(document.getElementById("parcelas").value);

  if (!valorVenda || !parcelas || !vendedor || !document.getElementById("cidade").value) {
    document.getElementById("resultado").innerHTML = "";
    return;
  }

  const restante = valorVenda - entrada;
  const taxas = {
    1: 2.23, 2: 4.51, 3: 6.84, 4: 9.22, 5: 11.66, 6: 14.15,
    7: 16.69, 8: 19.3, 9: 21.96, 10: 24.68, 11: 27.46, 12: 30.3
  };
  const taxa = taxas[parcelas];
  const totalComJuros = restante * (1 + taxa / 100);
  const valorParcela = totalComJuros / parcelas;

  document.getElementById("resultado").innerHTML = `
    <strong>Resumo do Pedido:</strong><br>
    Cidade: ${document.getElementById("cidade").value}<br>
    Vendedor: ${vendedor}<br>
    Nº do Pedido: ${pedido}<br>
    Entrada: ${formatarMoeda(parseFloat(entrada))}<br>
    Valor à vista (sem juros) parcelado: ${formatarMoeda(parseFloat(restante))}<br>
    Parcelamento: ${parcelas}x de ${formatarMoeda(parseFloat(valorParcela))}<br>
    Total Parcelado com Juros: ${formatarMoeda(parseFloat(totalComJuros))}<br>
    <strong>Entrada + Parcelado:</strong> ${formatarMoeda(parseFloat(entrada) + parseFloat(totalComJuros))}<br>
    Juros aplicados: ${taxa}% sobre o valor parcelado<br>
    <em>* Cada parcela inclui juros proporcionais ao período escolhido.</em>
  `;
}

function imprimirResumo() {
  const atendente = document.getElementById("vendedor").value;
  const cidade = document.getElementById("cidade").value;
  const pedido = document.getElementById("pedido").value;
  const entrada = parseMoeda(document.getElementById("entrada").value || "0");
  const valorVenda = parseMoeda(document.getElementById("valorVenda").value || "0");
  const parcelas = parseInt(document.getElementById("parcelas").value);
  const taxa = {
    1: 2.23, 2: 4.51, 3: 6.84, 4: 9.22, 5: 11.66, 6: 14.15,
    7: 16.69, 8: 19.3, 9: 21.96, 10: 24.68, 11: 27.46, 12: 30.3
  }[parcelas];
  const restante = valorVenda - entrada;
  const totalComJuros = restante * (1 + taxa / 100);
  const valorParcela = totalComJuros / parcelas;
  const dataHora = new Date().toLocaleString("pt-BR");

  const declaracao = `
    <h2 style="text-align:center;">Declaração de Compra - Infocell</h2>
    <p style="text-align: justify;">
    Eu, ____________________________________________, declaro para os devidos fins que realizei a compra de um item no valor total de ${formatarMoeda(valorVenda)}, referente ao pedido nº ${pedido}, na loja Infocell - unidade ${cidade}, atendido pelo atendente ${atendente}.
    </p>
    <p style="text-align: justify;">
    Efetuei o pagamento de uma entrada no valor de ${formatarMoeda(entrada)}, ficando o restante de ${formatarMoeda(restante)} a ser parcelado em ${parcelas}x de ${formatarMoeda(valorParcela)} com juros de ${taxa}% ao todo. O total final da compra é de ${formatarMoeda(entrada + totalComJuros)}.
    </p>
    <p style="text-align: justify;">
    Estou ciente de que o item do pedido poderá ser subtraído em caso de inadimplência após 40 dias. Para celulares, notebooks, tablets, MacBooks, iPads e Apple Watch financiados via PayJoy, o bloqueio será automático após falta de pagamento. Após 10 dias de atraso, a dívida será encaminhada para protesto.
    </p>
    <div style="margin-top: 40px;">
      <p style="margin-bottom: 10px;"><strong>${cidade}, ${dataHora}</strong></p>
      <p><strong>Assinatura do Cliente:</strong> _____________________________________________</p>
    </div>
    <br><br>
    <div style="margin-top: 40px;">
      <p><strong>Assinatura do Atendente:</strong> ____________________________________________</p>
    </div>
    <p style="font-size: 14px; margin-top: 40px; text-align: justify;">
    <strong>Base Legal:</strong> Esta declaração se apoia no disposto nos artigos 421 e 422 do Código Civil Brasileiro, 
    que garantem a liberdade contratual entre as partes, bem como no artigo 248 do Código Civil sobre perdas e danos por inadimplemento. 
    O protesto e a cobrança são respaldados pela Lei nº 9.492/1997 (Lei do Protesto) e o bloqueio remoto está amparado por cláusula contratual 
    nos termos do art. 113 do Código Civil, que trata da boa-fé objetiva nos contratos.
    </p>
  `;

  const janela = window.open('', '', 'width=800,height=900');
  janela.document.write('<html><head><title>Resumo da Venda</title><style>body{font-family:Arial; margin:3cm; line-height:1.5;}</style></head><body>');
  janela.document.write(declaracao);
  janela.document.write('</body></html>');
  janela.document.close();
  janela.focus();
  janela.print();
}
</script>
</body>
</html>
