
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Simulador de Parcelamento com Contrato</title>
    <style>
        @media print {
            body * {
                visibility: hidden;
            }
            #contrato, #contrato * {
                visibility: visible;
            }
            #contrato {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
        }
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: auto; }
        label { display: block; font-weight: bold; margin-bottom: 2px; margin-top: 15px; }
        input, select { width: 100%; padding: 8px; margin-bottom: 12px; box-sizing: border-box; font-size: 16px; }
        #resultado, #contrato { margin-top: 20px; font-size: 16px; background: #f9f9f9; padding: 15px; border-radius: 8px; white-space: pre-line; }
        #procedimento, #dadosExtras { margin-top: 30px; padding: 15px; border-left: 5px solid #2196f3; border-radius: 6px; background: #eef7ff; }
        #dadosExtras { border-color: #ffc107; background: #fffbe7; }
        button { padding: 12px; font-size: 16px; margin-top: 20px; cursor: pointer; }
    </style>
</head>
<body>
    <h2>Simulador de Parcelamento com Contrato</h2>

    <label for="valor">Valor da Venda (R$):</label>
    <input type="number" id="valor" step="0.01">

    <label for="entrada">Valor de Entrada (R$):</label>
    <input type="number" id="entrada" step="0.01">

    <label for="parcelas">Condições de Parcelamento:</label>
    <select id="parcelas" onchange="exibirDetalhes()">
        <option value="">Selecione...</option>
    </select>

    <div id="resultado"></div>

    <div id="procedimento">
        <strong>Procedimento para registrar a venda:</strong><br><br>
        1. Lance o valor total da venda no campo do produto.<br>
        2. Descreva o produto corretamente.<br>
        3. Registre o valor da entrada e o restante como boleto parcelado.
    </div>

    <div id="dadosExtras">
        <label for="cidade">Cidade:</label>
        <select id="cidade">
            <option value="Pratânia">Pratânia</option>
            <option value="São Manuel">São Manuel</option>
            <option value="Lençóis Paulista">Lençóis Paulista</option>
        </select>

        <label for="atendente">Nome do Atendente:</label>
        <input type="text" id="atendente">

        <label for="cliente">Nome do Cliente:</label>
        <input type="text" id="cliente">

        <label for="cpf">CPF do Cliente:</label>
        <input type="text" id="cpf">

        <label for="pedido">Pedido de Venda:</label>
        <input type="text" id="pedido">

        <label for="produto">Produto:</label>
        <input type="text" id="produto">
    </div>

    <button onclick="gerarContrato()">Imprimir Contrato</button>

    <div id="contrato" style="display:none;"></div>

    <script>
        const taxas = {
            1: 0.0752, 2: 0.0960, 3: 0.0728, 4: 0.0615, 5: 0.0550,
            6: 0.0509, 7: 0.0497, 8: 0.0475, 9: 0.0459, 10: 0.0448,
            11: 0.0440, 12: 0.0434
        };

        function formatar(v) { return v.toFixed(2).replace('.', ','); }

        function gerarOpcoes() {
            const v = parseFloat(document.getElementById("valor").value);
            const e = parseFloat(document.getElementById("entrada").value) || 0;
            const s = document.getElementById("parcelas");
            s.innerHTML = '<option value="">Selecione...</option>';
            if (!v || e >= v) return;
            const f = v - e;

            for (let i = 1; i <= 12; i++) {
                const t = taxas[i];
                const p = (f * Math.pow(1 + t, i)) / i;
                const g = p * i + e;
                const opt = document.createElement("option");
                opt.value = i;
                opt.text = `${i}x de R$ ${formatar(p)} - Total: R$ ${formatar(g)}`;
                s.appendChild(opt);
            }
        }

        document.getElementById("valor").addEventListener("input", gerarOpcoes);
        document.getElementById("entrada").addEventListener("input", gerarOpcoes);

        function exibirDetalhes() {
            const ps = parseInt(document.getElementById("parcelas").value);
            const v = parseFloat(document.getElementById("valor").value);
            const e = parseFloat(document.getElementById("entrada").value) || 0;
            if (!ps || !v || e >= v) { document.getElementById("resultado").innerText = ""; return; }

            const t = taxas[ps], f = v - e;
            const p = (f * Math.pow(1 + t, ps)) / ps;
            const tp = p * ps, tg = tp + e;
            document.getElementById("resultado").innerText =
                `Entrada: R$ ${formatar(e)}
` +
                `${ps}x de R$ ${formatar(p)}
` +
                `Juros: ${formatar(t * 100)}% a.m.
` +
                `Total Parcelado: R$ ${formatar(tp)}
` +
                `Total com Entrada: R$ ${formatar(tg)}`;
        }

        function gerarContrato() {
            const ps = parseInt(document.getElementById("parcelas").value);
            const v = parseFloat(document.getElementById("valor").value);
            const e = parseFloat(document.getElementById("entrada").value) || 0;
            const cidade = document.getElementById("cidade").value;
            const atendente = document.getElementById("atendente").value;
            const cliente = document.getElementById("cliente").value;
            const cpf = document.getElementById("cpf").value;
            const pedido = document.getElementById("pedido").value;
            const produto = document.getElementById("produto").value;

            if (!ps || !v || !cliente || !cpf || !produto) {
                alert("Preencha todos os campos obrigatórios.");
                return;
            }

            const t = taxas[ps], f = v - e;
            const p = (f * Math.pow(1 + t, ps)) / ps;
            const tp = p * ps, tg = tp + e;
            const hoje = new Date();
            const data = hoje.toLocaleDateString();
            const hora = hoje.toLocaleTimeString();

            const texto = `

DECLARAÇÃO DE COMPROMISSO DE PAGAMENTO

Eu, ${cliente}, CPF nº ${cpf}, residente em ${cidade}, declaro para os devidos fins que realizei a compra descrita abaixo e estou ciente e de acordo com as condições de pagamento e consequências legais em caso de inadimplência.

Detalhes da Venda:
Pedido nº: ${pedido}
Produto: ${produto}
Valor Total da Compra: R$ ${formatar(tg)}
Entrada paga no ato: R$ ${formatar(e)}
Parcelamento: ${ps}x de R$ ${formatar(p)} com juros de ${formatar(t * 100)}% ao mês

Declaro ainda que estou ciente e de acordo com as seguintes cláusulas:

1. Em caso de atraso superior a 10 (dez) dias, autorizo o envio do meu CPF para protesto em cartório, bem como sua inclusão nos cadastros de inadimplentes como SPC e Serasa, conforme prevê a Lei nº 9.492/1997.
2. Após 20 (vinte) dias de inadimplência, autorizo o bloqueio remoto do smartphone adquirido e reconheço o direito da loja de proceder à retomada do bem.
3. Estou ciente de que o não cumprimento integral do pagamento compromete meu acesso ao aparelho adquirido.

Por ser verdade, firmo a presente declaração de livre e espontânea vontade.

${cidade}, ${data} às ${hora}


TERMO DE COMPROMISSO DE PAGAMENTO


___________________________________
Assinatura do Cliente

___________________________________
Assinatura do Atendente (${atendente})
            `;
            const div = document.getElementById("contrato");
            div.style.display = "block";
            div.innerText = texto;

            setTimeout(() => window.print(), 200);
        }
    </script>
</body>
</html>
