const isProd = process.env.NODE_ENV === 'production';

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Configuração de imagens para permitir domínios externos
  images: {
    domains: ['tools-infocell.vercel.app', 'synthsolutions.com.br', 'i.imgur.com'],
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'tools-infocell.vercel.app',
        pathname: '/dashboard-infocell/**',
      },
      {
        protocol: 'https',
        hostname: 'synthsolutions.com.br',
        pathname: '/dashboard-infocell/**',
      },
      {
        protocol: 'https',
        hostname: 'i.imgur.com',
        pathname: '/**',
      }
    ]
  },

  // Configuração para o Chrome AWS Lambda e Puppeteer Core
  serverExternalPackages: ['chrome-aws-lambda', '@sparticuz/chromium'],
  
  // Aumentar o limite de tempo da função serverless para processamento de PDF
  serverRuntimeConfig: {
    maxDuration: 90,
  },
  
  experimental: {
    // Nota: serverMemoryTimeoutMs foi removido pois não é reconhecido pelo Next.js 15.3.2
  },

  // Configurações específicas para o webpack
  webpack: (config, { dev, isServer }) => {
    // Configurações específicas para produção
    if (!dev && isServer) {
      // Não incluir chrome-aws-lambda no bundle do servidor
      config.externals.push('chrome-aws-lambda');
      
      // Não incluir @sparticuz/chromium no bundle do servidor
      config.externals.push('@sparticuz/chromium');
      
      // Adicionar puppeteer-core aos externals para garantir compatibilidade
      config.externals.push('puppeteer-core');
    }

    return config;
  }
};

export default nextConfig;
